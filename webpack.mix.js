let mix = require('laravel-mix');
let webpack = require('webpack');
let path = require('path');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.webpackConfig({
	resolve: {
		extensions: ['.ts', '.vue', '.js'],
		alias: {
			jQuery: path.resolve(__dirname, 'node_modules/jquery/dist/jquery.js')
		}
	},
	plugins: [
		// ProvidePlugin helps to recognize $ and jQuery words in code
		// And replace it with require('jquery')
		new webpack.ProvidePlugin({
			$: 'jquery',
			jQuery: 'jquery'
		})
	],
	output: {
		hashFunction: "sha256"
	}
});

/* remove console messages in production builds */
if (mix.inProduction()) {
	mix.version();
	mix.options({
		terser: {
			terserOptions: {
				compress: {
					drop_console: true
				}
			}
		}
	});
} else {
	mix.sourceMaps()
}

mix.js([
		'resources/js/app.js'
	], 'public/js')
	.vue()
	.styles([
		'node_modules/jquery-ui/themes/base/core.css',
		'node_modules/jquery-ui/themes/base/datepicker.css',
		'node_modules/jquery-ui/themes/base/theme.css',
		'resources/css/jquery.datetimepicker.css'
	], 'public/css/all.css')
	.sass('resources/sass/app.scss', 'public/css');
