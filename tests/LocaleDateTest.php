<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\LocaleDate;

/**
 *  Class to test Mydat2Ndat class
 *
 *  <AUTHOR>
 */
class LocaleDateTest extends TestCase {

    /**
     * @group localedate
     */
    public function testIsThereAnySyntaxError() {
        $localedate = new LocaleDate();
        $this->assertTrue(is_object($localedate), "No syntax errors");
    }

    /**
     * @group localedate
     */
    public function testNormalDateDefault() {
        $retVal = LocaleDate::getLocaleSpecificDate("2017-08-25");
        $this->assertTrue($retVal === "25-08-2017", "Test normal date with default (nl)");
    }

    /**
     * @group localedate
     */
    public function testNormalDateENNL() {
        $retVal = LocaleDate::getLocaleSpecificDate("2017-08-25", "nl");
        $this->assertTrue($retVal === "25-08-2017", "Test en date to nl");
    }

    /**
     * @group localedate
     */
    public function testNormalDateNLNL() {
        $retVal = LocaleDate::getLocaleSpecificDate("25-08-2017", "nl");
        $this->assertTrue($retVal === "25-08-2017", "Test nl date to nl");
    }

    /**
     * @group localedate
     */
    public function testNormalDateNLEN() {
        $retVal = LocaleDate::getLocaleSpecificDate("25-08-2017", "en");
        $this->assertTrue($retVal === "2017-08-25", "Test nl date to en");
    }

    /**
     * @group localedate
     */
    public function testNormalDateENEN() {
        $retVal = LocaleDate::getLocaleSpecificDate("25-08-2017", "en");
        $this->assertTrue($retVal === "2017-08-25", "Test en date to en");
    }
}
