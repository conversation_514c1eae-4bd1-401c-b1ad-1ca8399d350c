<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\StringManipulator;

/**
 *  Class to test Color class
 *
 *  <AUTHOR> Sperber
 */
class StringManipulatorTest extends TestCase
{
    public function testClassExists() {
        $stringManipulator = new StringManipulator();
        $this->assertTrue(is_object($stringManipulator), "No syntax errors");
    }

    public function testTelephoneExtractor1() {
        $result = StringManipulator::telephoneExtractor('');
        $this->assertTrue(is_array($result));
        $this->assertTrue($result === []);
    }

    public function testTelephoneExtractor2() {
        $result = StringManipulator::telephoneExtractor('12345');
        $this->assertTrue($result === []);
    }

    // // 0612345678 and +31 6 12 34 55 66 should both match
    public function testTelephoneExtractor3() {
        $result = StringManipulator::telephoneExtractor('0612345678');
        $this->assertEquals($result['tel'], '0612345678');
    }
    public function testTelephoneExtractor4() {
        $result = StringManipulator::telephoneExtractor('+31 6 12 34 55 66');
        $this->assertEquals($result['tel'], '+31 6 12 34 55 66');
    }

    public function testTelephoneExtractor5() {
        $result = StringManipulator::telephoneExtractor('+31 6 12 34 55 66 extra text added');
        $this->assertEquals($result['tel'],'+31 6 12 34 55 66');
        $this->assertEquals($result['extra'],'extra text added');
    }
    public function testTelephoneExtractor6() {
        $result = StringManipulator::telephoneExtractor('0612345678 extra text added ending in space ');
        $this->assertEquals($result['tel'], '0612345678');
        $this->assertEquals($result['extra'],'extra text added ending in space');
    }
    public function testTelephoneExtractor7() {
        $result = StringManipulator::telephoneExtractor('0612345678 06123456780612345678');
        $this->assertEquals($result, []);
    }

    // TESTS EMAILEXTRACTOR
    public function testEmailExtractor1() {
        $result = StringManipulator::emailExtractor('');
        $this->assertTrue(is_array($result));
        $this->assertTrue($result === [], "test empty string as input");
    }

    public function testEmailExtractor2() {
        $result = StringManipulator::emailExtractor('aapnootmies');
        $this->assertEquals([], $result, "test non empty input but not a valid email address");
    }

    public function testEmailExtractor3() {
        $result = StringManipulator::emailExtractor('<EMAIL>');
        $this->assertEquals('<EMAIL>', $result['email'], "test correct email address");
    }

    public function testEmailExtractor4() {
        $result = StringManipulator::emailExtractor('<EMAIL> is mijn adres');
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('is mijn adres', $result['extra']);
    }

    public function testEmailExtractor5() {
        $result = StringManipulator::emailExtractor('<EMAIL> is mijn <NAME_EMAIL>');
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals('is mijn <NAME_EMAIL>', $result['extra'], "test correctly finding first email address in a string");
    }

    /**
     * added unicode characters should be stripped
     */
    public function testEmailExtractor6() {
        $result = StringManipulator::emailExtractor('liesbeth‌.‌looman‌@‌outlook‌.‌com');
        $this->assertEquals('<EMAIL>', $result['email'], "Test stripping of Unicode chracters");
    }

    // TESTS PRICE2DATABASE
    public function testPrice2Database1()
    {
        $result = StringManipulator::price2Database("10.00");
        $this->assertEquals("10.00", $result, "Test no change needed");
    }
    public function testPrice2Database2()
    {
        $result = StringManipulator::price2Database("10,00");
        $this->assertEquals("10.00", $result, "Test change comma to dot");
    }
    public function testPrice2Database3()
    {
        $result = StringManipulator::price2Database("10,00abcd");
        $this->assertEquals("0.00", $result, "Test invalid format, contains non-numeric");
    }
    public function testPrice2Database4()
    {
        $result = StringManipulator::price2Database("10,00,00");
        $this->assertEquals("10.00", $result, "Test invalid format, create likely format");
    }
    public function testPrice2Database5()
    {
        $result = StringManipulator::price2Database("100,000.00");
        $this->assertEquals("100000.00", $result, "Test remove thousand separator");
    }
    public function testPrice2Database6()
    {
        $result = StringManipulator::price2Database("100.000,00");
        $this->assertEquals("100000.00", $result, "Test remove thousand separator in dutch");
    }
    public function testPrice2Database7()
    {
        $result = StringManipulator::price2Database("11");
        $this->assertEquals("11.00", $result, "Test add decimals");
    }
    public function testPrice2Database8()
    {
        $result = StringManipulator::price2Database("12.0");
        $this->assertEquals("12.00", $result, "Test add 1 decimal");
    }
    public function testPrice2Database9()
    {
        $result = StringManipulator::price2Database("13.000");
        $this->assertEquals("13.00", $result, "Test remove 1 decimal");
    }
    // TESTS FLOAT2DATABASE
    public function testFloat2Database1()
    {
        $result = StringManipulator::float2Database("10.000");
        $this->assertEquals("10.000", $result, "Test no change needed");
    }
    public function testFloat2Database2()
    {
        $result = StringManipulator::float2Database("10,001");
        $this->assertEquals("10.001", $result, "Test replace comma");
    }
    public function testFloat2Database4()
    {
        $result = StringManipulator::float2Database("10,00abcd");
        $this->assertEquals("0", $result, "Test invalid format, contains non-numeric");
    }
}
