<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\Mydat2Ndat;

/**
 *  Class to test Mydat2Ndat class
 *
 *  <AUTHOR>
 */
class Mydat2NdatTest extends TestCase {

    /**
     * @group mydat2ndat
     */
    public function testIsThereAnySyntaxError() {
        $mdat2ndat = new Mydat2Ndat();
        $this->assertTrue(is_object($mdat2ndat), "No syntax errors");
    }

    /**
     * @group mydat2ndat
     */
    public function testNormalDate() {
        $retVal = Mydat2Ndat::getNdat("2017-08-25");
        $this->assertTrue($retVal === "25-08-2017", "Test normal date");
    }

    /**
     * @group mydat2ndat
     */
    public function testNdat2Ndat() {
        $retVal = Mydat2Ndat::getNdat("25-08-2017");
        $this->assertTrue($retVal === "25-08-2017");
    }

    /**
     * @group mydat2ndat
     */
    public function testNormalDateTime() {
        $retVal = Mydat2Ndat::getNdat("2017-08-25 11:00");
        $this->assertTrue($retVal === "25-08-2017 11:00");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateTimeAlreadyNdate() {
        $retVal = Mydat2Ndat::getNdat("25-08-2017 11:00");
        $this->assertTrue($retVal === "25-08-2017 11:00");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateTimeAlreadyNdateSpaces() {
        $retVal = Mydat2Ndat::getNdat("25 08 2017 11:00");
        $this->assertTrue($retVal === "25-08-2017 11:00");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateTimeAlreadyNdateSpacesNoZeros1() {
        $retVal = Mydat2Ndat::getNdat("5 8 2017 11:00");
        $this->assertTrue($retVal === "05-08-2017 11:00");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateTimeAlreadyNdateSpacesNoZeros2() {
        $retVal = Mydat2Ndat::getNdat("15 8 2017 11:00");
        $this->assertTrue($retVal === "15-08-2017 11:00");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateAlreadyNdateSpacesNoZeros1() {
        $retVal = Mydat2Ndat::getNdat("5 8 2017");
        $this->assertTrue($retVal === "05-08-2017");
    }

    /**
     * @group mydat2ndat
     */
    public function testDateAlreadyNdateSpacesNoZeros2() {
        $retVal = Mydat2Ndat::getNdat("15 8 2017");
        $this->assertTrue($retVal === "15-08-2017");
    }

    /**
     * @group mydat2ndat
     */
    public function testEmptyDate() {
        $retVal = Mydat2Ndat::getNdat();
        $this->assertTrue($retVal === "");
    }

    /**
     * @group mydat2ndat
     */
    public function testNonValidDate() {
        $retVal = Mydat2Ndat::getNdat("12345");
        $this->assertTrue($retVal === "12345");
    }

    /**
     * @group mydat2ndat
     */
    public function testNonValidDateCorrectLength() {
        $retVal = Mydat2Ndat::getNdat("1234567890");
        $this->assertTrue($retVal === "1234567890");
    }

}