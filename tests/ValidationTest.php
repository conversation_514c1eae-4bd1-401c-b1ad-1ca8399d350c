<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\Validation;

/**
 *  Class to test Color class
 *
 *  <AUTHOR> <PERSON>perber
 */
class ValidationTest extends TestCase
{
    /**
     * @group validation
     */
    public function testIsThereAnySyntaxError() {
        $validation = new Validation();
        $this->assertTrue(is_object($validation), "No syntax errors");
    }
    /**
     * @group validation
     */
    public function testValidEmail() {
        $email = "<EMAIL>";
        $this->assertTrue(Validation::isValidEmail($email), "$email is valid email");
    }
    /**
     * @group validation
     */
    public function testValidEmail2() {
        $email = "<EMAIL>";
        $this->assertTrue(Validation::isValidEmail($email), "$email is valid email");
    }
    /**
     * @group validation
     */
    public function testValidEmail3() {
        $email = "<EMAIL>";
        $this->assertTrue(Validation::isValidEmail($email), "$email is valid email");
    }
    /**
     * @group validation
     */
    public function testInvalidEmail() {
        $email = "michael.sperber.scolavisa.net";
        $this->assertFalse(Validation::isValidEmail($email), "$email is NOT valid email");
    }

}
