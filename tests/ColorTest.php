<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\Color;

/**
 *  Class to test Color class
 *
 *  <AUTHOR> Sperber
 */
class ColorTest extends TestCase {

    /**
     * @group color
     */
    public function testIsThereAnySyntaxError() {
        $color = new Color();
        $this->assertTrue(is_object($color), "No syntax errors");
    }

    // Tests: getHexColorFromSet
    public function testGetHexColorDefault() {
        // should retrieve the 0 index of the default set: 20contrasting
        $this->assertTrue(Color::getHexColorFromSet() === "#e6194b");
    }
    public function testGetHexColorIndex() {
        $this->assertTrue(Color::getHexColorFromSet(3) === "#4363d8");
    }
    public function testGetHexColorIndexOutOfBounds() {
        // should retrieve the 1 index of the default set: 20contrasting (21 % 20)
        $this->assertTrue(Color::getHexColorFromSet(21) === "#3cb44b");
    }
    public function testGetHexColorIndexNotAInt() {
        // should retrieve the 0 index of the default set: 20contrasting
        $this->assertTrue(Color::getHexColorFromSet('aap') === "#e6194b");
    }
    public function testGetHexColorIndexNegative() {
        // should retrieve the 0 index of the default set: 20contrasting
        $this->assertTrue(Color::getHexColorFromSet(-1) === "#e6194b");
    }
    public function testGetHexColorSetNoExists() {
        // should retrieve the 0 index of the default set: 20contrasting
        $this->assertTrue(Color::getHexColorFromSet(5, "dezesetbestaatniet") === "#e6194b");
    }
    public function testGetHexColorSetEmpty() {
        // should retrieve the 0 index of the default set: 20contrasting
        $this->assertTrue(Color::getHexColorFromSet(5, "emptyset") === "#e6194b");
    }

    // Tests: getAllColorsOfSet
    public function testGetAllColorsOfSetDefault() {
        $this->assertTrue(Color::getAllColorsOfSet() === []);
    }
    public function testGetAllColorsOfSetDefaultRealSet() {
        $colors = Color::getAllColorsOfSet('20contrasting');
        $this->assertTrue(is_array($colors));
        $this->assertTrue(count($colors) === 20);
    }
    public function testGetAllColorsOfSetDefaultNoExistSet() {
        $this->assertTrue(count(Color::getAllColorsOfSet('dezesetbestaatniet')) === 0);
    }

}