<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\MiscDate;

/**
 *  Class to test Mydat2Ndat class
 *
 *  <AUTHOR>
 */
class MiscDateTest extends TestCase {
    /**
     * @group miscdate
     */
    public function testCalculateAgeObj() {
        $miscDate = new MiscDate();
        $this->assertTrue(is_object($miscDate), "No syntax errors");
    }

    /**
     * @group miscdate
     */
    public function testCalculateAgeToday() {
        $today = new \DateTime();
        $age = MiscDate::calculateAge($today->format('Y-m-d'));
        $this->assertTrue($age === 0, "Age correct: today=0");
    }

    /**
     * @group miscdate
     */
    public function testCalculateAgeNextYear() {
        $date = new DateTime();
        $date->add(new DateInterval('P370D'));
        $testDate = $date->format('Y-m-d');
        $age = MiscDate::calculateAge($testDate);
        $this->assertTrue($age === 1, "Age correct: nextyear=1");
    }
    /**
     * @group miscdate
     */
    public function testCalculateAgeNullDate() {
        $age = MiscDate::calculateAge('');
        $this->assertTrue($age === 0, "Empty param for date: 0");
    }

    /**
     * @group miscdate
     */
    public function testCalculateAgeInvalidDate() {
        $age = MiscDate::calculateAge("2020-01-32");
        $this->assertTrue($age === 0, "Test: Invalid date param for date: 0");
    }

    /**
     * @group miscdate
     */
    // validateDate Tests
    public function testValidateDate1() {
        $this->assertTrue(MiscDate::validateDate('2020-01-31'), "Test Validate correct date");
        $this->assertFalse(MiscDate::validateDate('2020-02-31'), "Test Validate incorrect date");
        $this->assertFalse(MiscDate::validateDate(''), "Test Validate empty date");
        $this->assertTrue(MiscDate::validateDate('31-01-2020', 'd-m-Y'), "Test Validate correct date dutch format");
        $this->assertFalse(MiscDate::validateDate('2020-02-31', 'd-m-Y'), "Test Validate correct date, wrong format");
    }

    public function testOverlapsWithValidDates()
    {
        $this->assertFalse(
            MiscDate::overlaps('2022-01-01 11:00', '2022-01-01 12:00', '2022-01-01 13:00', '2022-01-01 14:00'),
            "Test non-overlapping valid dates, range 2 after range 1"
        );
        $this->assertFalse(
            MiscDate::overlaps('2022-01-01 13:00', '2022-01-01 14:00', '2022-01-01 11:00', '2022-01-01 12:00'),
            "Test non-overlapping valid dates, range 1 after range 2"
        );
        $this->assertTrue(
            MiscDate::overlaps('2022-01-01 11:00', '2022-01-01 12:00', '2022-01-01 10:00', '2022-01-01 11:30'),
            "Test overlapping valid dates e2 inside range 1"
        );
        $this->assertTrue(
            MiscDate::overlaps('2022-01-01 11:00', '2022-01-01 12:00', '2022-01-01 11:30', '2022-01-01 12:30'),
            "Test overlapping valid dates e1 inside range 2"
        );
        $this->assertTrue(
            MiscDate::overlaps('2022-01-01 11:00', '2022-01-01 12:00', '2022-01-01 10:30', '2022-01-01 12:30'),
            "Test overlapping valid dates s1-e1 inside range 2"
        );
        $this->assertTrue(
            MiscDate::overlaps('2022-01-01 10:30', '2022-01-01 12:30', '2022-01-01 11:00', '2022-01-01 12:00'),
            "Test overlapping valid dates s1-e1 inside range 2"
        );
    }

    public function testOverlapsWithInvalidDates()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('some date strings could not be used to generate a datetime object');
        MiscDate::overlaps('aap', 'noot', 'mies', 'jet');
    }

    public function testOverlapsWithNullDates()
    {
        $this->expectException(\TypeError::class);
        MiscDate::overlaps(null, null, null, null);
    }
}
