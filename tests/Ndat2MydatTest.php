<?php
use PHPUnit\Framework\TestCase;
use Scolavisa\scolib\Ndat2Mydat;

/**
 *  Class to test Ndat2Mydat class
 *
 *  <AUTHOR>
 */
class Ndat2MydatTest extends TestCase {

    /**
     * @group ndat2mydat
     */
    public function testIsThereAnySyntaxError() {
        $ndat2mydat = new Ndat2Mydat();
        $this->assertTrue(is_object($ndat2mydat), "No syntax errors");
    }

    /**
     * @group ndat2mdat
     */
    public function testNormalDate() {
        $retVal = Ndat2Mydat::getMydat("25-08-2017");
        $this->assertTrue($retVal === "2017-08-25");
    }

    /**
     * @group ndat2mdat
     */
    public function testNormalDateSlash() {
        $retVal = Ndat2Mydat::getMydat("25/08/2017");
        $this->assertTrue($retVal === "2017-08-25");
    }

    /**
     * @group ndat2mdat
     */
    public function testNormalDateUnderscore() {
        $retVal = Ndat2Mydat::getMydat("25_08_2017");
        $this->assertTrue($retVal === "2017-08-25");
    }

    /**
     * test date is already ndat
     * @group ndat2mdat
     */
    public function testMydat2mydat() {
        $retVal = Ndat2Mydat::getMydat("2017-08-25");
        $this->assertTrue($retVal === "2017-08-25");
    }

    /**
     * @group ndat2mdat
     */
    public function testNormalDateTime() {
        $retVal = Ndat2Mydat::getMydat("26-08-2017 11:00");
        $this->assertTrue($retVal === "2017-08-26 11:00");
    }

    /**
     * @group ndat2mdat
     */
    public function testNormalDateTimeSecs() {
        $retVal = Ndat2Mydat::getMydat("27-08-2017 11:00:05");
        $this->assertTrue($retVal === "2017-08-27 11:00:05");
    }

    /**
     * @group ndat2mdat
     */
    public function testDateTimeAlreadyNdate() {
        $retVal = Ndat2Mydat::getMydat("2017-08-25 11:00");
        $this->assertTrue($retVal === "2017-08-25 11:00");
    }

    /**
     * @group ndat2mdat
     */
    public function testEmptyDate() {
        $retVal = Ndat2Mydat::getMydat("");
        $this->assertTrue($retVal === "", "Test empty date");
    }

    /**
     * @group ndat2mdat
     */
    public function testNonValidDate() {
        $retVal = Ndat2Mydat::getMydat("12345");
        $this->assertTrue($retVal === "12345");
    }

    /**
     * @group ndat2mdat
     */
    public function testNonValidDate2() {
        $retVal = Ndat2Mydat::getMydat("10-12-2!05");
        $this->assertTrue($retVal === "10-12-2!05");
    }

    /**
     * @group ndat2mdat
     */
    public function testNonValidDate3() {
        $retVal = Ndat2Mydat::getMydat("10-!2-2005");
        $this->assertTrue($retVal === "10-!2-2005");
    }
    /**
     * @group ndat2mdat
     */
    public function testNonValidDateCorrectLength() {
        $retVal = Ndat2Mydat::getMydat("1234567890");
        $this->assertTrue($retVal === "1234567890");
    }

    /**
     * @group ndat2mdat
     */
    public function testWrittenDate() {
        $retVal = Ndat2Mydat::getMydat("30 juni 1963");
        $this->assertTrue($retVal === "1963-06-30");
    }

    /**
     * @group ndat2mdat
     */
    public function testWrittenShortDate() {
        $retVal = Ndat2Mydat::getMydat("30 jun 1963");
        $this->assertTrue($retVal === "1963-06-30");
    }
    /**
     * @group ndat2mdat
     */
    public function testWrittenShortDateNoCentury() {
        $retVal = Ndat2Mydat::getMydat("20 jun 63");
        $this->assertTrue($retVal === "1963-06-20");
    }

    /**
     * @group ndat2mdat
     */
    public function testWrittenShortAltDate() {
        $retVal = Ndat2Mydat::getMydat("30 mrt 1963");
        $this->assertTrue($retVal === "1963-03-30");
    }

    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate1() {
        $retVal = Ndat2Mydat::getMydat("30 juni");
        $this->assertTrue($retVal === "30 juni");
    }
    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate2() {
        $retVal = Ndat2Mydat::getMydat("30 januari");
        $this->assertTrue($retVal === "30 januari");
    }
    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate3() {
        $retVal = Ndat2Mydat::getMydat("30-06");
        $this->assertTrue($retVal === "30-06");
    }
    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate4() {
        $retVal = Ndat2Mydat::getMydat("30-06", true);
        $this->assertFalse($retVal);
    }
    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate5() {
        $retVal = Ndat2Mydat::getMydat("just a string", true);
        $this->assertFalse($retVal);
    }
    /**
     * @group ndat2mdat
     */
    public function testIncompleteDate6() {
        // deze komt door de eerste test want einigt op 4 digits
        $retVal = Ndat2Mydat::getMydat("just a string 1234", true);
        $this->assertFalse($retVal);
    }

    /**
     * @group ndat2mdat
     */
    public function testNoCentury1() {
        $retVal = Ndat2Mydat::getMydat("30/06/66");
        $this->assertTrue($retVal == "1966-06-30");
    }
    /**
     * @group ndat2mdat
     */
    public function testNoCentury2() {
        $retVal = Ndat2Mydat::getMydat("30/06/66 10:30");
        $this->assertTrue($retVal == "1966-06-30 10:30");
    }

    /**
     * @group nydat2mdat
     */
    public function testDateTimeAlreadyNdateSpacesNoZeros3() {
        $retVal = Ndat2Mydat::getMydat("5 12 2017 11:00");
        $this->assertTrue($retVal === "2017-12-05 11:00");
    }

    /**
     * @group nydat2mdat
     */
    public function testDateSpacesNoZeros1() {
        $retVal = Ndat2Mydat::getMydat("5 08 2017");
        $this->assertTrue($retVal === "2017-08-05");
    }
    /**
     * @group nydat2mdat
     */
    public function testDateSpacesNoZeros2() {
        $retVal = Ndat2Mydat::getMydat("15 8 2017");
        $this->assertTrue($retVal === "2017-08-15");
    }

    /**
     * @group nydat2mdat
     */
    public function testDateSingleChar() {
        $retVal = Ndat2Mydat::getMydat("15-a-2017", true);
        $this->assertFalse($retVal);
    }

    /**
     * @group nydat2mdat
     */
    public function testDateNoZeros1() {
        $retVal = Ndat2Mydat::getMydat("15-8-2017");
        $this->assertTrue($retVal === "2017-08-15");
    }
    /**
     * @group nydat2mdat
     */
    public function testDateNoZeros2() {
        $retVal = Ndat2Mydat::getMydat("5-08-2017");
        $this->assertTrue($retVal === "2017-08-05");
    }
    /**
     * @group nydat2mdat
     */
    public function testDateNoZeros3() {
        $retVal = Ndat2Mydat::getMydat("5-8-2017");
        $this->assertTrue($retVal === "2017-08-05");
    }
    /**
     * @group nydat2mdat
     */
    public function testDateAlreadyMFormat() {
        $retVal = Ndat2Mydat::getMydat("2017-08-15");
        $this->assertTrue($retVal === "2017-08-15");
    }
    /**
     * @group nydat2mdat
     */
    public function testDateNoDashes() {
        $retVal = Ndat2Mydat::getMydat("04042005");
        $this->assertTrue($retVal === "2005-04-04");
    }
}