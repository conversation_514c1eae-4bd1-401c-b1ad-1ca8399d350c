<?php

namespace App\Jobs;

use App\Models\Emaillogentry;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Mailgun\Mailgun;

class ProcessMailgunApiCallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Data needed for Mailgun API call.
     * $data['token'] = $uniqueToken
     * $data['domain_id'] = Auth::user()->domain->id
     * $data['mail_data'] = ["to", "from", "subject", "body", "attachment"]
     * $data['email_address'] = $emailAddress
     * See also https://documentation.mailgun.com/docs/mailgun/api-reference/openapi-final/tag/Messages/
     */
    protected $mgData; // additional data to be sent with the API call

    /**
     * Create a new job instance.
     */
    public function __construct(array $data = [])
    {
        $this->mgData = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!isset($this->mgData['token'])) {
            Log::error('Mailgun API call not executed because of missing token');
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing token prevents sending email');
            return;
        }
        // check if all data is present and valid
        if (
            !isset($this->mgData['mail_data']) ||
            !isset($this->mgData['mail_data']['subject']) ||
            !isset($this->mgData['mail_data']['body']) ||
            !isset($this->mgData['mail_data']['to']) ||
            !isset($this->mgData['mail_data']['from']) ||
            !isset($this->mgData['domain_id']))
        {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing data prevents sending email');
            Log::error(
                'Mailgun API call not executed because of missing data. Setting status of ' .
                $this->mgData["token"] .
                ' to failed.'
            );
            return;
        }

        if (!$this->isValidEmail($this->mgData['mail_data']['to']) ) {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Invalid email address prevents sending email');
            Log::error(
                'Mailgun API call not executed because of invalid email address: ' .
                $this->mgData['mail_data']['to']
            );
            return;
        }

        // All fine, send email
        $apiKey = env("MAILGUN_SECRET_" . $this->mgData['domain_id']);
        $mgSendingDomain = env("MAILGUN_SENDING_DOMAIN_" . $this->mgData['domain_id']);
        $mgApiEndpoint = env("MAILGUN_ENDPOINT");
        if (!$apiKey || !$mgSendingDomain || !$mgApiEndpoint) {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing environment variables prevents sending email');
            Log::error('Mailgun API call not executed because of missing environment variables');
            return;
        }
        $mgClient = Mailgun::create($apiKey, "https://$mgApiEndpoint");
        if (!$apiKey || !$mgSendingDomain || !$mgApiEndpoint) {
            $this->setMailStatus($this->mgData['token'], 'failed', 'Missing environment variables prevents sending email');
            Log::error('Mailgun API call not executed because of missing environment variables');
            return;
        }

        // Data needed for Mailgun API call. The rendered body (using the view) is passed in from the EmailController.
        $params = [
            'from'          => $this->mgData['mail_data']['from'],
            'to'            => $this->mgData['mail_data']['to'],
            'h:Reply-To'    => $this->mgData['mail_data']['replyTo'],
            'subject'       => $this->mgData['mail_data']['subject'],
            'html'          => $this->mgData['mail_data']['body'],
        ];

        // add attachments if any
        if (isset($this->mgData['mail_data']['attachments'])) {
            foreach ($this->mgData['mail_data']['attachments'] as $attachment) {
                $params['attachment'][] = [
                    'filePath' => $attachment['path'],
                    'filename' => $attachment['name'],
                    'contentType' => $attachment['mime']
                ];
            }
        }


        try {
            $mgClient->messages()->send($mgSendingDomain, $params);
        } catch (\Exception $e) {
            $this->setMailStatus($this->mgData['token'], 'failed', $e->getMessage());
            Log::error('Mailgun API call failed: ' . $e->getMessage());
            return;
        }
        Log::info('Mailgun API call executed successfully, setting status of ' . $this->mgData["token"] . ' to sent');
        $this->setMailStatus($this->mgData['token'], 'sent', 'Mail has been sent successfully');
    }

    /**
     * Update the status of the email in the database.
     * @param string $token
     * @param string $status: one of 'queued', 'sent', 'failed', 'unknown'
     */
    private function setMailStatus ($token, $status='unknown', $message=null): void
    {
        $message = date('Y-m-d H:i:s') . ' - ' . $message;
        $emailLogEntry = Emaillogentry::where('unique_token', $token)->first();
        if (!$emailLogEntry) {
            Log::error("EmailLogEntry with token $token not found, can't update status to $status");
            return;
        }
        $emailLogEntry->status = $status; // depends on response
        if ($message) {
            if ($emailLogEntry->log === null || $emailLogEntry->log === '') // first log entry
                $emailLogEntry->log = $message;
            else // append to existing log (if any)
                $emailLogEntry->log = $emailLogEntry->log . '<br>' . $message;
        }
        $emailLogEntry->updated_at = now();
        $emailLogEntry->save();
    }

    private function isValidEmail($email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
}
