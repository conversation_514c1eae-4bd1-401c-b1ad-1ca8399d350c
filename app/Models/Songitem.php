<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class Songitem extends Model {

	public function song() {
		return $this->belongsTo("App\Models\Song");
	}

	public function songitemfiles() {
		return $this->hasMany("App\Models\Songitemfile");
	}

	/**
	 * override delete to be able to delete referenced files from disk before removing entry in the database
	 * @return bool|null
	 */
	public function delete() {

		// get all songitemfile and delete the fysical files
		$songitemfiles = $this->songitemfiles;

		foreach ($songitemfiles as $songitemfile) {
			if (file_exists(storage_path() . '/app/public' . config( 'app.PATHTOSONGS' ) . $songitemfile->url)) {
				Log::info("Unlinking file: " . config( 'app.PATHTOSONGS' ) . $songitemfile->url);
				unlink(storage_path() . '/app/public' . config( 'app.PATHTOSONGS' ) . $songitemfile->url);
			} else {
				Log::warning("File " . config( 'app.PATHTOSONGS' ) . " $songitemfile->url not found");
			}
		}
		// now delete the entry in the database
		return parent::delete();
	}

    /**
     * Get the relationships for the entity.
     *
     * @return array
     */
    public function getQueueableRelations() {
        // TODO: Implement getQueueableRelations() method.
    }
}
