<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;


class Calendar extends Model {

    /**
     * Set standard filtering on queries:
     * always get the result for this (logged in) domain
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $loggedInUserDomain = Auth::user() ? Auth::user()->domain_id : 13;
        $query = parent::newQuery();
        $query->where('domain_id', '=', $loggedInUserDomain);
        return $query;
    }

    public function events() {
        return $this->belongsToMany("App\Models\Event", 'calendar_events')
                    ->using("App\Models\CalendarEvent")
                    ->withTimestamps();
    }

}
