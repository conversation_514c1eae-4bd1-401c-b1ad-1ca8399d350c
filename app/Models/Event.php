<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Event extends Model {

    public function calendars() {
        return $this->belongsToMany("App\Models\Calendar", 'calendar_events')
                    ->using("App\Models\CalendarEvent")
                    ->withTimestamps();
    }

    // Keep the old method for backward compatibility during transition
    public function calendar() {
        return $this->calendars()->first();
    }

}
