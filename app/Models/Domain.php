<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Domain extends Model {
    /**
     * Set standard filtering on queries:
     * always get the result for this (logged in) domain
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery() {
        $query = parent::newQuery();
        $query->where('id', '=', Auth::user()->domain_id);
        return $query;
    }

	public function users() {
		return $this->hasMany("App\Models\User");
	}
	public function domaintype() {
		return $this->belongsTo("App\Models\Domaintype");
	}

    public function Socials() {
        return $this->hasMany("App\Models\Social");
    }
}
