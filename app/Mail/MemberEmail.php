<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class MemberEmail extends Mailable implements ShouldQueue {
    use Queueable, SerializesModels;

    public $sender;          // don't call this "from"! (name clash)
    public $body;
    public $attachedDocs;    // don't call this attachments! (name clash)
    public $subject;
    public $logo;            // URL of logo
    public $city;
    public $telephone;
    public $sign_name;       // name to sign the email with


    /**
     * Create a new message instance.
     *
     * @param $from
     * @param $attachments
     * @param $subject
     * @param $body
     */
    public function __construct($from, $attachments, $subject, $body, $logo, $city, $telephone, $sign_name) {
        $this->sender           = $from;
        $this->body             = $body;
        $this->attachedDocs     = $attachments ?: null;
        $this->subject          = $subject;

        // sign fields
        $this->logo             = $logo;
        $this->city             = $city;
        $this->telephone        = $telephone;
        $this->sign_name        = $sign_name;
    }

    /**
     * Build the message.
     *
     * @return MemberEmail
     */
    public function build() {
        $body       = $this->body;
        $title      = $this->subject;
        $logo       = $this->logo;
        $city       = $this->city;
        $telephone  = $this->telephone;
        $sign_name  = $this->sign_name;
        $from       = $this->sender;

        $email = $this->view('email.membersmailtemplate', compact(
            'body', 'title',
            'logo', 'city', 'sign_name', 'telephone', 'from'
        ));
        $email->from($this->sender);
        $email->subject($this->subject);

        // add attachments if any
        if(!empty($this->attachedDocs)) {
            // has a 'path', 'name' and 'mime'
            foreach ($this->attachedDocs as $attachedDoc) {
                $email->attach($attachedDoc['path'], [
                    'as'    => $attachedDoc['name'],
                    'mime'  => $attachedDoc['mime']
                ]);
            }
        }

        // return mailable
        return $email;
    }
}
