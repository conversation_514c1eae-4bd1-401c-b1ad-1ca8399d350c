<?php

namespace App\Providers;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        URL::forceRootUrl(\Config::get('app.url'));
        if (str_contains(\Config::get('app.url'), 'https://')) {
            URL::forceScheme('https');
        }

        if (app()->environment('production')) {
            DB::listen(function($query) {
                if (str_contains(strtolower($query->sql), 'insert into users')) {
                    Log::warning('Verdachte INSERT query gedetecteerd', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'route' => request()->route()->getName(),
                        'url' => request()->fullUrl(),
                        'method' => request()->method(),
                        'ip' => request()->ip()
                    ]);
                }
            });
        }
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
