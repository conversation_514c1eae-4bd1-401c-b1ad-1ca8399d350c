<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EmailLogApiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'daysBack'  => 'nullable|integer|min:1',
            'q'         => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'daysBack.min' => 'The days back must be at least 1.',
        ];
    }
}