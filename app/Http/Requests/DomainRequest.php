<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            "name"                  => "required",
            "url"                   => "url",
            "default_passwd"        => "required|min:6",
            "default_language"      => "required",
            "email_email_from"      => "nullable|email",                // may be empty
            "email_logo"            => "nullable|url",                  // may be empty
            "social_displayname"    => "nullable|array",                // may be empty
            "social_displayname.*"  => "nullable|string|min:1|max:15",
            "social_link"           => "nullable|array",                // may be empty
            "social_link.*"         => "nullable|url",
        ];
    }
}
