<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;


class MembersChange extends FormRequest
{
    public function __construct()
    {
        parent::__construct();

        Validator::extend('emailsstring', function ($attribute, $value) {
            $rules = array('email' => 'required|email');
            // convert comma separated values to array
            $emails = explode(',', $value);
            foreach ($emails as $email) {
                $data = array('email' => trim($email));
                $validator = Validator::make($data, $rules);
                if ($validator->fails()) {
                    return false;
                }
            }
            return true;
        });

        Validator::replacer('emailsstring', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':attribute', $attribute, 'All :attribute must be a valid email addresses.');
        });
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */

    // Original from file MembersNew.php
    public function rules()
    {
        return [
            "first_name" => "required",
            "last_name" => "required",
            "email" => "required|emailsstring",
        ];
    }
}
