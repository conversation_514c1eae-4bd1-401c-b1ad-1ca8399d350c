<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SendMembersMailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'emailAddresses'    => 'required|min:1',   // minimal array count
            'message'           => 'required|min:10',
            'subject'           => 'required|min:5'
        ];
    }
}
