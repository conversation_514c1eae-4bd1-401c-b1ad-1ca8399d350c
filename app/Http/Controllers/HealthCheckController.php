<?php

namespace App\Http\Controllers;

use App\Models\Members;

class HealthCheckController extends Controller
{
    public function ohDearHealthCheck()
    {
        $ohDearSectret = env('OH_DEAR_HEALTH_CHECK_SECRET');
        // first check token in the request header oh-dear-health-check-secret to make sure the request originated from Oh Dear
        if (empty($ohDearSectret) || $ohDearSectret != env('OH_DEAR_HEALTH_CHECK_SECRET')) {
            return response()->json([
                'finishedAt' => time(),
                'checkResults' => [
                    [
                        'name' => 'OhDearHealthCheckSecret',
                        'label' => 'Oh Dear Health Check Secret',
                        'status' => 'skipped',
                        'notificationMessage' => 'Not Valid',
                        'shortSummary' => 'Oh Dear Health Check Secret is not correct'
                    ]
                ]
            ]);
        }

        return response()->json([
            'finishedAt' => time(),
            'checkResults' => [
                $this->checkDBConnection()
            ]
        ]);
    }

    public function checkDBConnection()
    {
        $respArray = [
            'name' => 'DatabaseConnection',
            'label' => 'DB Connection',
            'meta' => []
        ];
        // measure runtime of the query
        try {
            $start = microtime(true);
            $countMembers = Members::count();
            $end = microtime(true);
            $runTime = $end - $start;
            $respArray['status'] = 'ok';
            $respArray['notificationMessage'] = 'All good';
            $respArray['shortSummary'] = 'All good';
            $respArray['meta'] = [
                'queryRunTime' => $runTime,
                'query' => 'select count(*) from members',
                'countMembers' => $countMembers
            ];
            if ($runTime > 1) {
                $respArray['status'] = 'warning';
                $respArray['notificationMessage'] = 'Query took more than 1 second';
                $respArray['shortSummary'] = 'Query took more than 1 second';
            }
        } catch (\Exception $e) {
            $end = microtime(true);
            $runTime = $end - $start;
            $respArray['meta'] = [
                'queryRunTime' => $runTime,
                'query' => 'select count(*) from members',
                'countUsers' => 0
            ];
            $respArray['status'] = "failed";
            $respArray['notificationMessage'] = "Database connection failed";
            $respArray['shortSummary'] = $e->getMessage();
        }
        return $respArray;
    }
}
