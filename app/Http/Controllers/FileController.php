<?php

namespace App\Http\Controllers;


class FileController extends Controller {
	public function getFile($path, $filename) {
		// todo: find out if the requested file belongs to the domain of the logged in user
		// todo: for API requests this means: look for the website the request came from
		// todo: the API requests need a different controller (with their own security

		// $path is encoded to be able to send slashes along
		$path = str_replace("|", "/", $path);
		$path = str_replace("%7C", "/", $path);
		// $path _must_ start and end with a /
		$path = str_finish($path, '/');
		$path = str_start($path, '/');

		return response()->download(storage_path('app/public' . $path . $filename), null, [], null);
	}
}
