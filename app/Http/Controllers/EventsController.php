<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Http\Requests\EventRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Scolavisa\scolib\Ndat2Mydat;

class EventsController extends Controller {
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index() {

    }

    /**
     * Get events for api
     * param $calid
     * @return \Illuminate\Http\JsonResponse
     */
    public function apilist($calid) {
        $events = Event::whereHas('calendars', function($query) use ($calid) {
                $query->where('calendars.id', '=', $calid);
            })
            ->orderBy('start_datetime', 'asc')
            ->get();
        return response()->json( $events );
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(EventRequest $request) {
        Log::info('Storing event', [
            'request' => $request->all()
        ]);
        $event = new Event();
        $event->title = $request->title;
        $event->description = isset($request->description) ? $request->description : '';
        $event->start_datetime = Ndat2Mydat::getMydat($request->start_datetime);
        $event->duration = $request->duration;
        $event->save();

        // Attach the event to the calendar using the many-to-many relationship
        $event->calendars()->attach($request->calendar_id);

        return redirect()->route('calendars.edit', $request->calendar_id)->with('message', ucfirst(trans("generic.datasaved")));
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id) {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(EventRequest $request, $id) {
        Log::info('Updating event', [
            'request' => $request->all()
        ]);
        $event = Event::findOrFail($id);
        $event->title = $request->title;
        $event->description = isset($request->description) ? $request->description : '';
        $event->start_datetime = Ndat2Mydat::getMydat($request->start_datetime);
        $event->duration = $request->duration;
        $event->save();

        // Update calendar association if calendar_id is provided
        if ($request->has('calendar_id')) {
            $event->calendars()->sync([$request->calendar_id]);
        }

        return redirect()->route('calendars.edit', $request->calendar_id)->with('message', ucfirst(trans("generic.datasaved")));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id) {

    }

    /**
     * for API usage, returns the new set of events, after deleting
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function deleteEvent($id) {
        $event = Event::findOrFail($id);
        $calendar = $event->calendars()->first(); // Get the first calendar (for backward compatibility)

        if ($calendar && $calendar->domain_id === Auth::user()->domain_id) {
            Log::info("deleting event for calendar $calendar->id");
            $event->delete();
        } else {
            Log::error("faul play: domain id is not correct when deleting event for calendar!!");
            Log::error("logged in domain: " . Auth::user()->domain_id . ", cal domain: " . ($calendar ? $calendar->domain_id : 'null'));
        }

        return $calendar ? $calendar->events : [];
    }
}
