<?php

namespace App\Http\Controllers;

use App\Http\Requests\SongitemsRequest;
use App\Models\Songitem;
use App\Models\Songitemfile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SongitemsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        die("INDEX");
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        die("CREATE");
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param SongitemsRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SongitemsRequest $request)
    {
        Log::info("creating new songitem");
        $ownRecording = isset($request->own_recording) ? $request->own_recording : 0;
        if ($ownRecording === 'on') {
            $ownRecording = 1;
        }
        $reference = uniqid();
        $errorMsg = '';
        // Create new songitem
        $songitem = new Songitem();
        $songitem->song_id = $request->song_id;
        $songitem->name = $request->name;
        $songitem->linktype = $request->linktype;
        $songitem->own_recording = $ownRecording;
        $songitem->reference = $reference;
        $songitem->save();
        // File upload
        if ($request->hasFile('songitemfile')) {
            if ($request->file('songitemfile')->isValid()) {
                // use the chosen ref for the filename
                $fileName = $songitem->song->domain_id . '_' . $songitem->song->id . '_' . $reference . "." .
                    $request->file('songitemfile')->getClientOriginalExtension();
                Log::info("storing new file: " . storage_path() . '/app/public/songs/' . $fileName);
                $request->file("songitemfile")->move(
                    storage_path() . '/app/public/songs/',
                    $fileName
                );
                $originalName = $request->file('songitemfile')->getClientOriginalName();
                Log::info("original name: " . $originalName);

                // create new songitemfile record
                $songitemfile = new Songitemfile();
                $songitemfile->songitem_id = $songitem->id;
                $songitemfile->url = $fileName;
                $songitemfile->original_name = $originalName;
                $songitemfile->save();
                Log::info("songitemfile saved. ID = " . $songitemfile->id);
            } else {
                Log::error("File upload was unsuccesfull!");
                $errorMsg = "File upload was unsuccesfull!";
            }

        } elseif ($songitem->linktype === '2' || $songitem->linktype === '1') {
            // save a URL
            $songitemfile = new Songitemfile();
            $songitemfile->songitem_id = $songitem->id;
            $songitemfile->url = $request->weblink;
            $songitemfile->original_name = $request->weblink;
            $songitemfile->save();
            Log::info("songitem url saved. ID = " . $songitemfile->id);

        } else {
            Log::warning("no songitemfile saved.");
        }

        Log::info("songitem saved. ID = " . $songitem->id . " for song: " . $songitem->song->id);

        return redirect()
            ->route('songs.edit', $songitem->song->id)
            ->with('message', ucfirst(trans("generic.songitemsaved")) . " - " . $errorMsg);
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        die("SHOW");

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        die("EDIT");
    }

    /**
     * Update the specified resource in storage.
     *
     * @param SongitemsRequest|Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function update(SongitemsRequest $request, $id)
    {
        $errorMsg = '';
        Log::info("saving songitem data");
        $ownRecording = isset($request->own_recording) ? $request->own_recording : 0;
        if ($ownRecording === 'on') {
            $ownRecording = 1;
        }
        $songitem = Songitem::findOrFail($id);
        $songitem->name = $request->name;
        $songitem->linktype = $request->linktype;
        $songitem->own_recording = $ownRecording;
        $reference = $songitem->reference;
        // File upload
        if ($request->hasFile('songitemfile')) {
            if ($request->file('songitemfile')->isValid()) {
                // use the existing reference in the songitemname
                $fileName = Auth::user()->domain_id . '_' . $songitem->song->id . '_' . $reference . "." .
                    $request->file('songitemfile')->getClientOriginalExtension();
                Log::info("storing new file: " . storage_path() . '/app/public/songs/' . $fileName);
                $request->file("songitemfile")->move(
                    storage_path() . '/app/public/songs/',
                    $fileName
                );
                $originalName = $request->file('songitemfile')->getClientOriginalName();
                Log::info("original name: " . $originalName);

                // create new songitemfile record
                $songitemfile = new Songitemfile();
                $songitemfile->songitem_id = $id;
                $songitemfile->url = $fileName;
                $songitemfile->original_name = $originalName;

                $songitemfile->save();
                Log::info("songitemfile saved. ID = " . $songitemfile->id);

                // Archive old versions of this file
                // there should be just one (or zero) but just in case, iterate
                $archSongitemfiles = Songitemfile::where([
                    ['songitem_id', '=', $id],         //
                    ['id', '<>', $songitemfile->id], // not the one we just saved...
                ])->whereNull('history_date')
                    ->get();
                if (count($archSongitemfiles) > 0) {
                    foreach ($archSongitemfiles as $archfile) {
                        // old item present, transfer to archive
                        Log::info('archiving songitemfile: ' . $archfile->id);
                        $archfile->history_date = date('Y-m-d');
                        $archfile->save();
                    }
                }
            } else {
                Log::error("File upload failed!");
                $errorMsg = ' - ' . ucfirst(trans('generic.fileuploadfailed')) . '!';
            }
        } elseif ($songitem->linktype === '2' || $songitem->linktype === '1') {
            // save a URL
            $songitemfile = new Songitemfile();
            $songitemfile->songitem_id = $songitem->id;
            $songitemfile->url = $request->weblink;
            $songitemfile->original_name = $request->weblink;
            $songitemfile->save();
            Log::info("songitem url saved. ID = " . $songitemfile->id);
            // Archive old versions of this file
            // there should be just one (or zero) but just in case, iterate
            $archSongitemfiles = Songitemfile::where([
                ['songitem_id', '=', $id],         //
                ['id', '<>', $songitemfile->id], // not the one we just saved...
            ])->whereNull('history_date')
                ->get();
            if (count($archSongitemfiles) > 0) {
                foreach ($archSongitemfiles as $archfile) {
                    // old item present, transfer to archive
                    Log::info('archiving songitemfile: ' . $archfile->id);
                    $archfile->history_date = date('Y-m-d');
                    $archfile->save();
                }
            }

        } else { // end hasfile
            Log::error("No file upload");
        }

        Log::info("songitem saved. ID = " . $songitem->id . " for song: " . $songitem->song->id);
        $songitem->save();
        return redirect()->route('songs.edit', $songitem->song->id)->with('message', ucfirst(trans("generic.songitemsaved")) . $errorMsg);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // keep track of the song this songitem belongs to
        // so we can open that edit page afterwards
        $si = Songitem::findOrFail($id);
        $song = $si->song;
        Log::info("Deleting songitem id: " . $id . ' (' . $si->name . ')');
        Songitem::destroy($id);
        return redirect()->route('songs.edit', $song->id)->with('message', ucfirst(trans("generic.songitemdeleted")));
    }
}
