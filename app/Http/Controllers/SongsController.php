<?php

namespace App\Http\Controllers;

use App\Models\Song;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use App\Http\Requests\SongsRequest;
use Illuminate\Support\Facades\Auth;
use Scolavisa\scolib\Ndat2Mydat;

class SongsController extends Controller {
	// lookup / translation file types
	protected $fileTypes;

	public function __construct() {

		$this->fileTypes = [
			'3' => __('generic.genericdocument'),
			'7' => __('generic.genericimage'),
			'4' => 'PDF',
			'5' => __('generic.musicnotationfile'),
			'6' => __('generic.audiofile'),
            '2' => __('generic.weblinkvideo'),
            '1' => __('generic.weblinkpage')
		];
	}


	/**
	 * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
	public function index() {
		return view( 'repertoire.list' );
	}

	/**
	 * Show the form for creating a new resource.
	 *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
	public function create() {
		return view( 'songs.new' );
	}

	/**
	 * Store a newly created resource in storage.
	 *
     * @param SongsRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
	public function store( SongsRequest $request ) {
		$song = new Song();
		$song->domain_id            = Auth::user()->domain_id;
		$song->title                = $request->title;
		$song->year                 = $request->year;
		$song->tags                 = $request->tags;
		$song->time_in_setlist      = $request->time_in_setlist;
		$song->date_in_repertoire   = Ndat2Mydat::getMydat($request->date_in_repertoire);
		$song->archive_number       = $request->archive_number;
		$song->composer1            = $request->composer1;
		$song->composer2            = $request->composer2;
		$song->remarks              = $request->remarks;
		$song->save();
		// now go to the edit screen
		return redirect()->route('songs.edit', $song->id)->with('message', ucfirst(trans("generic.datasaved")));

	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  int $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
	public function edit( $id ) {
		$fileTypes = $this->fileTypes;
		$song = Song::findOrFail($id);
		return view( 'songs.edit', compact('song', 'fileTypes') );
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param SongsRequest|Request $request
	 * @param  int $id
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function update( SongsRequest $request, $id ) {

		$song = Song::findOrFail($id);
		$song->title                = $request->title;
		$song->year                 = $request->year;
		$song->tags                 = $request->tags;
		$song->time_in_setlist      = $request->time_in_setlist;
		$song->date_in_repertoire   = Ndat2Mydat::getMydat($request->date_in_repertoire);
		$song->archive_number       = $request->archive_number;
		$song->composer1            = $request->composer1;
		$song->composer2            = $request->composer2;
		$song->remarks              = $request->remarks;
		$song->save();
		// now go to the edit screen
		return redirect()->route('songs.edit', $id)->with('message', ucfirst(trans("generic.datasaved")));
	}

	/**
	 * Remove the specified resource from storage.
	 * Check for correct domain
	 * @param  int $id
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function destroy( $id ) {

		$song = Song::findOrFail($id);
		// remove fysical files from HD
		$songitems = $song->songitems;
		foreach ($songitems as $songitem) {
			Log::info("finding file: " . storage_path() . '/app/public' . config('app.PATHTOSONGS') . $songitem->url);
			if( ($songitem->url !== '') && (file_exists(storage_path() . '/app/public' . config('app.PATHTOSONGS') . $songitem->url))) {
				Log::info( "Removing songitem: $songitem->url" );
				File::delete( storage_path() . '/app/public' . config( 'app.PATHTOSONGS' ) . $songitem->url );
			} else {
				Log::info('/app/public' . config( 'app.PATHTOSONGS' ) . $songitem->url . " not found");
			}
		}
		Log::info("Removing song: $id");
		// removes song and cascades into songitems
		Song::destroy( $id );
		return redirect()->action('SongsController@index')->with('message', ucfirst(trans("generic.songdeleted")));

	}

	/////////////////////////////////////////
	/////////////// API CALLS ///////////////
	/////////////////////////////////////////

	/**
	 * get all songs, responds with json
	 * add the pivot / relations by using with
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function apiList() {
	    Log::info("Getting all song titles");
		$songs = Song::with( 'songitems' )->orderBy('archive_number')->orderBy("title")->get();
		return response()->json( $songs );
	}

	/**
	 * Get all songitems of a song.
	 * Also check if the songitem has a fysical file on disk
	 * @param int $songId
	 *
	 * @return \Illuminate\Http\JsonResponse
	 */
	public function apiSongitemsList($songId=0) {
		$song = Song::findOrFail($songId);
		$retArr = [];
		foreach ( $song->songitems as $songitem ) {
			$songitemFiles = $songitem->songitemfiles; // also get the file history
			foreach ( $songitemFiles as $index => $songitemFile ) {
				// get url if the song is available on disk
				if (file_exists(storage_path() . '/app/public' . config('app.PATHTOSONGS') . $songitemFile->url)) {
					$songitemFile->itemUrl = url('/file/' . str_replace('/', '|', config('app.PATHTOSONGS')) . '/' . $songitemFile->url);
				} else {
					$songitemFile->itemUrl = "";
				}
			}
			$songitem->typeDesc = $this->fileTypes[$songitem->linktype];
			$songitem->type = $songitem->linktype;
			$songitem->own_recordingDesc = $songitem->own_recording ? __('generic.yes') : __('generic.no');
			$retArr[] = $songitem;
		}
		return response()->json( $retArr );
	}

}
