<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h4>
                    <i class="fa fa-globe"></i> {{ucfirst(trans('generic.edit'))}} {{ucfirst(trans_choice('generic.domains', 1))}}:&nbsp;{{$domain->name}}
                </h4>
                <div class="clearfix"></div>
            </div>
            <div class="x_content"> <!-- START x-content -->

                {{----}}
                <div class="row">
                    <div class="col-xs-12 col-md-5">
                        <div class="form-group{{ $errors->has('name') ? ' has-error' : '' }}">
                            <label for="name" class="control-label">{!! ucfirst(trans('generic.domainname')) !!}&nbsp;*</label>
                            <input type="text" name="name" value="{{ old('name', $domain->name ?? '') }}" class="form-control" required>
                            @if ($errors->has('name'))
                                <span class="help-block"><strong>{{ $errors->first('name') }}</strong></span>
                            @endif
                        </div>
                    </div>
                    @if(Auth::user()->userHasModule('admin')) {{-- only scolavisa admin --}}
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('url') ? ' has-error' : '' }}">
                                <label for="url" class="control-label">{!! ucfirst(trans('generic.url')) !!}&nbsp;*</label>
                                    &nbsp;<i data-toggle="tooltip" title="this domain's main url's, including protocol, like http(s)." class="fa fa-question-circle"></i>
                                <input type="text" name="url" value="{{ old('url', $domain->url ?? '') }}" class="form-control" required>
                                @if ($errors->has('url'))
                                    <span class="help-block"><strong>{{ $errors->first('url') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-xs-12 col-md-4">
                            <div class="form-group{{ $errors->has('lookup_url') ? ' has-error' : '' }}">
                                <label for="lookup_url" class="control-label">{!! ucfirst(trans('generic.lookupurl')) !!}&nbsp;*</label>
                                    &nbsp;<i data-toggle="tooltip" title="use this domain's data from these url's. comma separated, no protocol" class="fa fa-question-circle"></i>
                                <input type="text" name="lookup_url" value="{{ old('lookup_url', $domain->lookup_url ?? '') }}" class="form-control" required>
                                @if ($errors->has('lookup_url'))
                                    <span class="help-block"><strong>{{ $errors->first('lookup_url') }}</strong></span>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>

                {{----}}
                <div class="row">
                    <div class="col-xs-6 col-md-2">
                        <div class="form-group{{ $errors->has('default_language') ? ' has-error' : '' }}">
                            <?php
                            if (isset($domain) && $domain->default_language === 'en') {
                                $setNl = ''; $setEn = 'selected';
                            } else {
                                $setNl = 'selected'; $setEn = '';
                            }
                            ?>
                            <label for="default_language" class="control-label">{{ucfirst(trans('generic.defaultlanguage'))}}</label>
                            <select class="form-control" name="default_language">
                                <option value="nl" {{$setNl}}>Nederlands</option>
                                <option value="en" {{$setEn}}>English</option>
                            </select>
                            @if ($errors->has('default_language'))
                                <span class="help-block"><strong>{{ $errors->first('default_language') }}</strong></span>
                            @endif
                        </div>
                    </div> 
                    <div class="col-xs-6 col-md-2">
                        <div class="form-group{{ $errors->has('default_passwd') ? ' has-error' : '' }}">
                            <label for="default_passwd" class="control-label">{{ucfirst(trans('generic.defaultpassword'))}}</label>
                                &nbsp;<i data-toggle="tooltip" title="used if you reset a members password" class="fa fa-question-circle"></i>
                            <input type="text" name="default_passwd" value="{{ old('default_passwd', $domain->default_passwd ?? '') }}" class="form-control" required>
                            @if ($errors->has('default_passwd'))
                                <span class="help-block"><strong>{{ $errors->first('default_passwd') }}</strong></span>
                            @endif
                        </div>
                    </div> 
                </div>

            </div> <!-- END x-content -->
        </div> <!-- START x-panel -->

        {{-- ********************** --}}
        {{-- Modules--}}
        @if(Auth::user()->userHasModule('admin')) {{-- only scolavisa admin --}}
            <div class="x_panel">
                <div class="x_title">
                    <h4>
                    <i class="fa fa-caret-square-o-right"></i> {{ucfirst(trans('generic.modules'))}}
                    </h4>
                    <div class="clearfix"></div>
                </div>
                <div class="x_content"> <!-- START x-content -->
                    <div class="row">
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_repertoire">
                                {{ucfirst(trans('generic.repertoire'))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_repertoire === 1)
                                    <input id="mod_repertoire" name="mod_repertoire" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_repertoire" name="mod_repertoire" type="checkbox" value="1" />
                                @endif
                                <label for="mod_repertoire" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_members">
                                {{ucfirst(trans_choice('generic.members',2))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_members === 1)
                                    <input id="mod_members" name="mod_members" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_members" name="mod_members" type="checkbox" value="1" />
                                @endif
                                <label for="mod_members" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_calendar">
                                {{ucfirst(trans('generic.calendar'))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_calendar === 1)
                                    <input id="mod_calendar" name="mod_calendar" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_calendar" name="mod_calendar" type="checkbox" value="1" />
                                @endif
                                <label for="mod_calendar" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_sponsors">
                                {{ucfirst(trans('generic.sponsors'))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_sponsors === 1)
                                    <input id="mod_sponsors" name="mod_sponsors" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_sponsors" name="mod_sponsors" type="checkbox" value="1" />
                                @endif
                                <label for="mod_sponsors" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_setlist">
                                {{ucfirst(trans('generic.setlist'))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_setlist === 1)
                                    <input id="mod_setlist" name="mod_setlist" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_setlist" name="mod_setlist" type="checkbox" value="1" />
                                @endif
                                <label for="mod_setlist" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                        <div class="col-xs-6 col-md-2">
                            <label class="control-label" for="mod_membershipfee">
                                {{ucfirst(trans('generic.membershipfee'))}}
                            </label>
                            <div class="material-switch">
                                @if(isset($domain) && $domain->mod_membershipfee === 1)
                                    <input id="mod_membershipfee" name="mod_membershipfee" type="checkbox" value="1" checked/>
                                @else
                                    <input id="mod_membershipfee" name="mod_membershipfee" type="checkbox" value="1" />
                                @endif
                                <label for="mod_membershipfee" class="label-success"></label>&nbsp;
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        {{-- END Modules--}}
        {{-- ********************** --}}


        {{-- ********************** --}}
        {{-- EMAIL SETTINGS --}}
        @if(isset($domain) && $domain->mod_members)
            <div class="x_panel">
                <div class="x_title">
                    <h4>
                        <i class="fa fa-send"></i> {{ucfirst(trans('generic.email'))}}
                        <small>{{trans('generic.explain_emailfields')}}</small>
                    </h4>
                    <div class="clearfix"></div>
                </div>
                <div class="x_content">
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_name') ? ' has-error' : '' }}">
                                <label for="email_name" class="control-label">{{ucfirst(trans('generic.name'))}}</label>
                                <input type="text" name="email_name" value="{{ old('email_name', $domain->email_name ?? '') }}" class="form-control">
                                @if ($errors->has('email_name'))
                                    <span class="help-block"><strong>{{ $errors->first('email_name') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_email_from') ? ' has-error' : '' }}">
                                <label for="email_email_from" class="control-label">{{ucfirst(trans('generic.email'))}}</label>
                                <input type="text" name="email_email_from" value="{{ old('email_email_from', $domain->email_email_from ?? '') }}" class="form-control">
                                @if ($errors->has('email_email_from'))
                                    <span class="help-block"><strong>{{ $errors->first('email_email_from') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_addressline1') ? ' has-error' : '' }}">
                                <label for="email_addressline1" class="control-label">{{ucfirst(trans('generic.addressline'))}} 1</label>
                                <input type="text" name="email_addressline1" value="{{ old('email_addressline1', $domain->email_addressline1 ?? '') }}" class="form-control">
                                @if ($errors->has('email_addressline1'))
                                    <span class="help-block"><strong>{{ $errors->first('email_addressline1') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_addressline2') ? ' has-error' : '' }}">
                                <label for="email_addressline2" class="control-label">{{ucfirst(trans('generic.addressline'))}} 2</label>
                                <input type="text" name="email_addressline2" value="{{ old('email_addressline2', $domain->email_addressline2 ?? '') }}" class="form-control">
                                @if ($errors->has('email_addressline2'))
                                    <span class="help-block"><strong>{{ $errors->first('email_addressline2') }}</strong></span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_zip') ? ' has-error' : '' }}">
                                <label for="email_zip" class="control-label">{!! ucfirst(trans('generic.zipcode')) !!}&nbsp;</label>
                                <input type="text" name="email_zip" value="{{ old('email_zip', $domain->email_zip ?? '') }}" class="form-control">
                                @if ($errors->has('email_zip'))
                                    <span class="help-block"><strong>{{ $errors->first('email_zip') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_city') ? ' has-error' : '' }}">
                                <label for="email_city" class="control-label">{!! ucfirst(trans('generic.city')) !!}&nbsp;</label>
                                <input type="text" name="email_city" value="{{ old('email_city', $domain->email_city ?? '') }}" class="form-control">
                                @if ($errors->has('email_city'))
                                    <span class="help-block"><strong>{{ $errors->first('email_city') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_telephone') ? ' has-error' : '' }}">
                                <label for="email_telephone" class="control-label">{{ucfirst(trans('generic.telephone'))}}</label>
                                <input type="text" name="email_telephone" value="{{ old('email_telephone', $domain->email_telephone ?? '') }}" class="form-control">
                                @if ($errors->has('email_telephone'))
                                    <span class="help-block"><strong>{{ $errors->first('email_telephone') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <div class="form-group{{ $errors->has('email_logo') ? ' has-error' : '' }}">
                                <label for="email_logo" class="control-label">{{ucfirst(trans('generic.logourl'))}}</label>
                                <input type="text" name="email_logo" value="{{ old('email_logo', $domain->email_logo ?? '') }}" class="form-control">
                                @if ($errors->has('email_logo'))
                                    <span class="help-block"><strong>{{ $errors->first('email_logo') }}</strong></span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Rows for socials for this domain -->
                    <!-- initial value from DB is available in domain->socials, make the data available in vue by adding a global -->
                    <script>
                        const socials = {!! json_encode($domain->socials) !!};
                    </script>
                    <div><strong>Socials &amp; links</strong></div>
                    <div class="row">
                        <div class="col-sm-6 col-md-3"><strong>Display name</strong></div>
                        <div class="col-sm-6 col-md-3"><strong>Link</strong></div>
                    </div>
                    <div class="row" v-for="social in socials">
                        <div class="col-sm-6 col-md-3">
                            <input class="form-control" v-model="social.display_name" name="social_displayname[]" placeholder="display name" />
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <input class="form-control" v-model="social.link" name="social_link[]" placeholder="link" />
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <button class="btn btn-danger btn-sm" @click.prevent="removeSocial(social)"><i class="fa fa-trash"></i></button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 col-md-3">
                            <input class="form-control" v-model="newSocialDdisplayName" name="social_displayname[]" placeholder="display name" />
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <input class="form-control" v-model="newSocialLink" name="social_link[]" placeholder="link" />
                        </div>
                        <div class="col-sm-6 col-md-3">
                            <button class="btn btn-success btn-sm" @click.prevent="addSocial"><i class="fa fa-plus"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        {{-- END EMAIL SETTINGS --}}
        {{-- ********************** --}}

        <div class="x_panel">
            <div class="x_content">
                {{-- buttons --}}
                <div class="form-group mt-1">
                    <button type="submit" class="btn btn-primary">{{ucfirst(trans('generic.save'))}}</button>
                    @if(Auth::user()->userHasModule('admin'))
                        <a class="btn btn-default" href="/domains">{{ucfirst(trans('generic.cancel'))}}</a>
                    @else
                        <a class="btn btn-default" href="/">{{ucfirst(trans('generic.cancel'))}}</a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
