@extends('layouts.app')

@section("content")
    <div class="container" id="memberfunctions" v-cloak>

        <div class="page-title">
            <div class="title_left">
                <h3>{{ucfirst(trans_choice('generic.memberfunctions', 2))}}</h3>
            </div>
        </div>

        @include('memberfunctions._listing')

        {{-- popup edit memberfunction --}}
        <modal
                closetext="{{ucfirst(trans('generic.close'))}}"
                popup-title="{{ucfirst(trans_choice('generic.memberfunctions', 1))}}"
                modal-id="edit-memberfunction">

            <div class="form-group">
                <label for="description">{{ucfirst(trans('generic.description'))}}</label>
                <input type="text" class="form-control" id="description" v-model="editFunctionDesc">
            </div>

            <button slot="okbutton" type="button" @click="saveMemberfunctionChanges" data-dismiss="modal"
                    class="btn btn-primary">{{ucfirst(trans('generic.savechanges'))}}</button>
        </modal>

        {{-- popup confirm delete memberfunction --}}
        <modal
                closetext="{{ucfirst(trans('generic.close'))}}"
                popup-title="{{ucfirst(trans('generic.areyousure'))}}?"
                modal-id="confirm-del-memberfunction">
            <p>
                <strong>{{ucfirst(trans('generic.delete'))}} &quot;@{{ delFunctionDesc }}&quot;?</strong><br/>
                {{ucfirst(trans('generic.thiscannotbeundone'))}}!
            </p>
            <button slot="okbutton" type="button" @click="deleteMemberfunction" data-dismiss="modal"
                    class="btn btn-primary">{{ucfirst(trans('generic.deletethismemberfunction'))}}</button>
        </modal>
    </div>
@endsection