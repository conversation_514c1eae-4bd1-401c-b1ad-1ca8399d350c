<div class="x_panel">
    <div class="x_title">
        <h2>
            <i class="fa fa-user"></i> {{ucfirst(trans('generic.functions'))}}&nbsp;
            <small>{{trans('generic.explain_nodeletememberfunctions')}}</small>
        </h2>
        <div class="nav navbar-right">
            <span data-toggle="tooltip" title="{{ucfirst(trans('generic.editthismember'))}}"
                  data-placement="right">
                <button @click="fillEditFormMemberfunction(0)" class="btn btn-primary"
                        data-toggle="modal" data-target="#edit-memberfunction">
                    <i class="glyphicon glyphicon-plus"></i> {{ucfirst(trans('generic.newex'))}} {{trans_choice('generic.memberfunctions', 1)}}
                </button>
            </span>
        </div>

        <div class="clearfix"></div>
    </div>
    <div class="x_content">
        <table class="table table-responsive">
            <col width="80"/>
            <col width="100"/>
            <col/>

            <thead>
            <tr>
                <th>{{ucfirst(trans('generic.functions'))}}</th>
                <th>{{ucfirst(trans('generic.description'))}}</th>
                <th>{{ucfirst(trans('generic.nrofmembers'))}}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="memfunction in memberfunctionOptions">
                <td>
                    <span data-toggle="tooltip" title="{{ucfirst(trans('generic.editthismember'))}}"
                          data-placement="right">
                        <button @click="fillEditFormMemberfunction(memfunction.id)" class="btn btn-success btn-xs"
                                data-toggle="modal" data-target="#edit-memberfunction">
                            <i class="glyphicon glyphicon-edit"></i>
                        </button>
                    </span>
                    <span v-if="memfunction.members.length == 0" data-toggle="tooltip"
                          title="{{ucfirst(trans('generic.deletethismember'))}}"
                          data-placement="right">
                        <button @click="fillDeleteFormMemberfunction(memfunction.id)" class="btn btn-danger btn-xs"
                                data-toggle="modal" data-target="#confirm-del-memberfunction">
                            <i class="glyphicon glyphicon-trash"></i>
                        </button>
                    </span>
                </td>

                <td>@{{ memfunction.description }}</td>
                <td v-if="memfunction.members.length > 0">
                    <button class="btn btn-primary btn-xs" type="button" data-toggle="collapse"
                            :data-target="'#showmembers'+memfunction.id" aria-expanded="false"
                            aria-controls="collapseExample">
                        <i class="glyphicon glyphicon-chevron-down"></i>&nbsp;@{{ memfunction.members.length }}
                    </button>
                    <div class="collapse" :id="'showmembers'+memfunction.id">
                        <div class="card card-body">
                            <span v-for="member in memfunction.members">@{{ member.first_name }} @{{ member.infix == '' ? '' : member.infix + " " }}@{{ member.last_name }},&nbsp;</span>
                        </div>
                    </div>
                </td>
                <td v-else>
                    @{{ memfunction.members.length }}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

