@extends('layouts.app')

@section("content")
    <div class="container" id="membersemail" v-cloak>


        <div class="x_panel">
            <div class="x_title">
                <h2><i class="fa fa-send"></i>&nbsp;{{ucfirst(trans('generic.email'))}}</h2>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <div class="row">
                    {{-- Left Column        --}}
                    {{-- Receiver part      --}}
                    <div class="col-md-2">
                        <div class="row">
                            <h4>{{ucfirst(trans('generic.to'))}}</h4>
                        </div>
                        <hr class="hr-success"/>
                        <div class="row" v-for="mFunction in memberfunctionOptions">
                            <div class="form-check" v-if="mFunction.members.length > 0">
                                <input type="checkbox" :value="mFunction.id" class="form-check-input" :id="'functioncheck_' + mFunction.id" v-model="selectedFunctions">
                                <label class="form-check-label" :for="'functioncheck_' + mFunction.id">
                                    @{{mFunction.description}}
                                    <span class="badge badge-info">@{{ mFunction.members.length }}</span>
                                </label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="btn-group">
                                <button @click.prevent="selectAllFunctions" class="btn btn-sm btn-primary">
                                    {{trans('generic.all')}}
                                </button>
                                <button @click.prevent="selectNoFunctions" class="btn btn-sm btn-primary">
                                    {{trans('generic.none')}}
                                </button>
                            </div>
                        </div>
                    </div>

                    {{-- Center Column      --}}
                    {{-- Message part       --}}
                    <div class="col-md-7">
                        <div class="row">
                            <h4>
                                {{ucfirst(trans('generic.message'))}}
                                <label class="pull-right">
                                    {{ucfirst(trans('generic.numberOfRecipients'))}}:
                                    <span :class="{'text-danger': totalNrOfRecepients==0}">
                                        @{{totalNrOfRecepients}}
                                    </span>
                                </label>
                            </h4>
                        </div>
                        <hr class="hr-success"/>
                        <div class="row">
                            <label>{{ucfirst(trans('generic.from'))}}: </label>
                            <span>{{$from}}</span>
                        </div>
                        <div class="row">
                            <label>{{ucfirst(trans('generic.replyto'))}}:</label><br>
                            <input
                                type="text"
                                class="form-control"
                                :class="{'incomplete': replyTo.length > 0 && !isValidEmail(replyTo)}"
                                v-model="replyTo"
                                placeholder="{{ trans('email.explain_replyto') }}"
                            >
                        </div>
                        <div class="row">
                            <label>{{ucfirst(trans('email.extra_email_adresses'))}}</label>
                            <input type="text" class="form-control" v-model="extraEmailAddresses" placeholder="{{ trans('email.explain_extra_email_addresses') }}">
                        </div>                    
                        <div class="row">
                            <label>{{ucfirst(trans('generic.subject'))}}</label>
                            <input type="text" class="form-control" v-model="subject">
                        </div>
                        <div class="row">
                            <label>{{ucfirst(trans('generic.message'))}}</label>
                            <textarea
                                v-model="message" id="message"
                                @change="value => { message = value }" class="form-control summernote">
                            </textarea>
                        </div>
                    </div>

                    {{-- Right Column       --}}
                    {{-- Attachment part    --}}
                    <div class="col-md-3">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="ml-1">{{ucfirst(trans('email.sending'))}}</h4>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <hr class="hr-success ml-1"/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">

                                <p class="ml-1">
                                    {{ trans('email.explain_companyfooterwillbeadded') }}
                                </p>
                                <p class="ml-1 alert alert-warning">
                                    {{ trans('email.warnsendmailtoselffirst') }}
                                </p>

                                <multifileupload
                                    class="ml-1"
                                    @submitfiles="sendmail"
                                    uploadbuttontext="{{ucfirst(trans('generic.upload'))}}"
                                    okbuttontext="{{ucfirst(trans('generic.send'))}}"
                                    :okbuttondisabled="!fieldsValid"
                                    :working="working"
                                    :max-upload-total-file-size="<?php echo config('app.MAXATTACHUPLOADFILESTOTALSIZE') ?>"
                                >
                                </multifileupload>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Archive --}}
        <div class="x_panel">
            <div class="x_title">
                <h2>
                    <i class="fa fa-archive"></i>&nbsp;{{ucfirst(trans('generic.emaillog'))}}
                    <small>{{ ucfirst(trans('email.past_period_14_days')) }}</small>
                </h2>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <emaillog></emaillog>
            </div>
        </div>
    </div>

@endsection
