{{-- Inject Scolavisa library --}}
@inject('Mydat2Ndat', 'Scolavisa\scolib\Mydat2Ndat')
<?php
$dayArray = [''=>trans('generic.choose'), '01'=>'01','02'=>'02','03'=>'03','04'=>'04','05'=>'05','06'=>'06','07'=>'07','08'=>'08','09'=>'09',
    '10'=>'10','11'=>'11', '12'=>'12','13'=>'13','14'=>'14','15'=>'15','16'=>'16','17'=>'17','18'=>'18','19'=>'19',
    '20'=>'20', '21'=>'21','22'=>'22','23'=>'23','24'=>'24','25'=>'25','26'=>'26','27'=>'27','28'=>'28','29'=>'29',
    '30'=>'30','31'=>'31'];
$monthArray = [''=>trans('generic.choose'),'01'=>trans('localisation.january'),'02'=>trans('localisation.february'),
    '03'=>trans('localisation.march'), '04'=>trans('localisation.april'),'05'=>trans('localisation.may'),
    '06'=>trans('localisation.june'),'07'=>trans('localisation.july'),'08'=>trans('localisation.august'),
    '09'=>trans('localisation.september'), '10'=>trans('localisation.october'),'11'=>trans('localisation.november'),
    '12'=>trans('localisation.december')];
?>
<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h3>
                    <i class="fa fa-user"></i> {{ucfirst(trans('generic.edit'))}} {{ucfirst(trans_choice('generic.members', 1))}}
                </h3>
                <div class="clearfix"></div>
            </div>
            <div class="x_content"> <!-- START x-content -->

                {{-- outer frame --}}
                <div class="row">

                    <div class="col-md-8" data-role="outerframe-left-col">

                        {{----}}
                        <div class="row">
                            <div class="col-xs-12 col-md-5">
                                <div class="form-group{{ $errors->has('first_name') ? ' has-error' : '' }}">
                                    <label for="first_name" class="control-label">{!! ucfirst(trans('generic.firstname')) !!}&nbsp;*</label>
                                    <input type="text" name="first_name" value="{{ old('first_name', $member->first_name ?? '') }}" class="form-control" required>
                                    @if ($errors->has('first_name'))
                                        <span class="help-block"><strong>{{ $errors->first('first_name') }}</strong></span>
                                    @endif
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-2">
                                <div class="form-group">
                                    <label for="infix" class="control-label">{{ucfirst(trans('generic.infix'))}}</label>
                                    <input type="text" name="infix" value="{{ old('infix', $member->infix ?? '') }}" class="form-control">
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-5">
                                <div class="form-group{{ $errors->has('last_name') ? ' has-error' : '' }}">
                                    <label for="last_name" class="control-label">{!! ucfirst(trans('generic.lastname')) !!}&nbsp;*</label>
                                    <input type="text" name="last_name" value="{{ old('last_name', $member->last_name ?? '') }}" class="form-control" required>
                                    @if ($errors->has('last_name'))
                                        <span class="help-block"><strong>{{ $errors->first('last_name') }}</strong></span>
                                    @endif
                                </div> {{--end formgroup--}}
                            </div>
                        </div>

                        {{----}}
                        <div class="row">
                            <div class="col-xs-12 col-md-4">
                                <div class="form-group">
                                    <label for="streetname" class="control-label">{{ucfirst(trans('generic.streetname'))}}</label>
                                    <input type="text" name="streetname" value="{{ old('streetname', $member->streetname ?? '') }}" class="form-control">
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-2">
                                <div class="form-group">
                                    <label for="house_number" class="control-label">{{ucfirst(trans('generic.housenumber'))}}</label>
                                    <input type="text" name="house_number" value="{{ old('house_number', $member->house_number ?? '') }}" class="form-control">
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-3">
                                <div class="form-group">
                                    <label for="zipcode" class="control-label">{{ucfirst(trans('generic.zipcode'))}}</label>
                                    <input type="text" name="zipcode" value="{{ old('zipcode', $member->zipcode ?? '') }}" class="form-control">
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-3">
                                <div class="form-group">
                                    <label for="city" class="control-label">{{ucfirst(trans('generic.city'))}}</label>
                                    <input type="text" name="city" value="{{ old('city', $member->city ?? '') }}" class="form-control">
                                </div> {{--end formgroup--}}
                            </div>
                        </div>

                        {{----}}
                        <div class="row">
                            <div class="col-xs-12 col-md-2">
                                <div class="form-group">
                                    <label for="birth_day" class="control-label">{{ucfirst(trans('generic.birthday'))}}</label>
                                    <select name="birth_day" class="form-control">
                                        @foreach($dayArray as $value => $label)
                                            <option value="{{ $value }}" {{ old('birth_day', $birthDay ?? '') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>

                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-2">
                                <div class="form-group">
                                    <label for="birth_month" class="control-label">{{ucfirst(trans('generic.birthmonth'))}}</label>
                                    <select name="birth_month" class="form-control">
                                        @foreach($monthArray as $value => $label)
                                            <option value="{{ $value }}" {{ old('birth_month', $birthMonth ?? '') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>

                                </div> {{--end formgroup--}}
                            </div>

                            <div class="col-xs-12 col-md-2">
                                <div class="form-group">
                                    {!! Form::label("birth_year", ucfirst(trans('generic.birthyear')), ["class"=>"control-label"]) !!}
                                    {!! Form::number("birth_year", old('birth_year'), ['class' => 'form-control', 'min' => '1900']) !!}
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-3">
                                <div class="form-group">
                                    <?php
                                    if (isset($member) && isset($member->membership_start_date)) {
                                        $msd = $Mydat2Ndat::getNdat($member->membership_start_date);
                                    } else {
                                        $msd = date('d-m-Y');
                                    }
                                    ?>
                                    {!! Form::label("membership_start_date", ucfirst(trans('generic.membershipstartdate')), ["class"=>"control-label"]) !!}
                                    {!! Form::text("membership_start_date", $msd, ['class' => 'form-control datepicker']) !!}
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-3">
                                <div class="form-group">
                                    <?php
                                    if (isset($member) && isset($member->membership_end_date)) {
                                        $msed = $Mydat2Ndat::getNdat($member->membership_end_date);
                                    } else {
                                        $msed = '';
                                    }
                                    ?>
                                    {!! Form::label("membership_end_date", ucfirst(trans('generic.membershipenddate')), ["class"=>"control-label"]) !!}
                                    {!! Form::text("membership_end_date", $msed, ['class' => 'form-control datepicker']) !!}
                                </div> {{--end formgroup--}}
                            </div>
                        </div>
                        {{----}}
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    {!! Form::label("description", ucfirst(trans('generic.webdescription')), ["class"=>"control-label"]) !!}
                                    <i class="fa fa-question-circle" data-toggle="tooltip"
                                       title="{{trans('generic.explain_webdescription')}}"></i>
                                    {!! Form::textarea("description", old('description'), ['class' => 'form-control summernote', "rows" => "5"]) !!}
                                </div> {{--end formgroup--}}
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="row">
                                    <div class="col-xs-12 col-md-12">
                                        <div class="form-group">
                                            {!! Form::label("remarks", ucfirst(trans('generic.remarks')), ["class"=>"control-label"]) !!}
                                            {!! Form::textarea("remarks", old('remarks'), ['class' => 'form-control', "rows" => "5"]) !!}
                                        </div> {{--end formgroup--}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12 col-md-6">
                                        <div class="form-group">
                                            {!! Form::label("telephone1", ucfirst(trans('generic.telephone')), ["class"=>"control-label"]) !!}
                                            {!! Form::text("telephone1", old('telephone1'), ['class' => 'form-control']) !!}
                                        </div> {{--end formgroup--}}
                                    </div>
                                    <div class="col-xs-12 col-md-6">
                                        <div class="form-group">
                                            {!! Form::label("telephone2", '&nbsp;', ["class"=>"control-label"]) !!}
                                            {!! Form::text("telephone2", old('telephone2'), ['class' => 'form-control']) !!}
                                        </div> {{--end formgroup--}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12 col-md-12">
                                        <div class="form-group{{ $errors->has('username') ? ' has-error' : '' }}">
                                            {!! Form::label("username", ucfirst(trans('generic.username')) . " (" . trans('generic.emailaddress') . ")", ["class"=>"control-label"]) !!}
                                            <?php
                                            if (isset($member)) {
                                                ?>
                                                <small>{{ trans('generic.explain_cannotbechanged') }}</small>
                                                {!! Form::text("username", old('username'), ['class' => 'form-control','disabled' => 'true']) !!}
                                                <?php
                                            } else {
                                                ?>
                                                <small>{{ trans('generic.explain_mustbeunique') }}</small>
                                                {!! Form::text("username", old('username'), ['class' => 'form-control']) !!}
                                                <?php
                                            }
                                            ?>
                                            @if ($errors->has('username'))
                                                <span class="help-block"><strong>{{ $errors->first('username') }}</strong></span>
                                            @endif

                                        </div> {{--end formgroup--}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-12 col-md-12">
                                        <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">
                                            {!! Form::label("email", ucfirst(trans('generic.email')) . "&nbsp;*&nbsp;", ["class"=>"control-label"]) !!}
                                            <small>{{ trans('generic.explain_multipleemail') }}</small>
                                            {!! Form::text("email", old('email'), ['class' => 'form-control', 'required' => 'required']) !!}
                                            @if ($errors->has('email'))
                                                <span class="help-block"><strong>{{ $errors->first('email') }}</strong></span>
                                            @endif
                                        </div> {{--end formgroup--}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4" data-role="outerframe-right-col">

                        <div class="col-xs-12 col-md-12 ">
                            <div class="form-group">
                                {!! Form::label("photo", ucfirst(trans('generic.photo')), ["class"=>"control-label"]) !!}
                                <i class="fa fa-question-circle" data-toggle="tooltip"
                                   title="{{trans('generic.explain_photo')}}"></i>
                                {!! Form::hidden("photo", old('photo'), ['class' => 'form-control', "disabled" => "disabled"]) !!}
                            </div> {{--end formgroup--}}
                        </div>

                        <div class='col-xs-12 col-md-12'>
                            @if(isset($member) && !empty($member->photo))
                                <img src="/app/public/members/{{ $member->photo }}" width='150px'/>
                            @endif
                        </div>

                        <div class='col-xs-12 col-md-12'>
                            <fileupload
                                    buttontext="{{ucfirst(trans('generic.photoupload'))}}"
                                    helptext="{{ucfirst(trans('generic.nowpleasepresssave'))}}"
                                    fileinputid="memberphotofile"
                                    @filewasreceived="fileReceived">
                            </fileupload>
                        </div>

                        @if(isset($member) && !empty($member->passwd))
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <button @click.prevent="resetPassword" class="btn btn-danger btn-sm">Reset
                                        password
                                    </button>
                                    <small> default: {{$member->domain->default_passwd}}</small>
                                </div>
                            </div>
                        @elseif(isset($member) && empty($member->passwd))
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <span title="{{ucfirst(trans('generic.explain_memberinvitation'))}}"
                                          data-toggle="tooltip">
                                        <button @click.prevent="fillemailcontentwithinvite" data-toggle="modal"
                                                data-target="#popup-invite-member-email"
                                                class="btn btn-primary btn-sm">{{ucfirst(trans('generic.invitememberbyemail'))}}</button>
                                    </span>
                                </div>
                            </div>
                        @else
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <span title="{{ucfirst(trans('generic.youneedtosavefirst'))}}"
                                          data-toggle="tooltip">
                                        <button class="btn btn-primary btn-sm btn-dark">{{ucfirst(trans('generic.invitememberbyemail'))}}</button><br/>
                                    </span>
                                </div>
                            </div>
                        @endif

                        <div class="col-12" v-if="member.password_reset_code">
                            <div class="ml-1">
                                {{ucfirst(trans('generic.memberrequestedpasswordreset'))}}
                                <br/>
                                @php
                                    $now = new \DateTime();
                                @endphp
                                @if(!empty($member->pw_reset_valid_until))
                                    @php
                                        $tokenValidUntil = new \DateTime($member->pw_reset_valid_until);
                                    @endphp
                                    @if($now < $tokenValidUntil)
                                        {{ucfirst(trans('generic.tokenisnotexpired'))}}
                                    @else
                                        {{ucfirst(trans('generic.tokenisexpired'))}}
                                    @endif
                                @else
                                    {{ucfirst(trans('generic.tokenisexpired'))}}
                                @endif
                            </div>
                        </div>
                        <div class="col-12 mt-2">
                            <div class="ml-1">
                                <div class="form-group">
                                    @if(isset($member) && $member->email_subscription == '1')
                                        <input type="checkbox" name="email_subscription" checked
                                               class="form-check-input" id="email_subscription" value="1"/>
                                    @else
                                        <input type="checkbox" name="email_subscription"
                                               class="form-check-input" id="email_subscription" value="1"/>
                                    @endif
                                    <label for="email_subscription" class="control-label">
                                        {{ucfirst(trans('generic.emailsubscription'))}}
                                    </label><br/>
                                    <small>{{ucfirst(trans('generic.emailsubscriptionexplain'))}}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- buttons --}}
                <div class="form-group">
                    {!! Form::submit(ucfirst(trans('generic.save')), ['class'=>'btn btn-primary']) !!}
                    <a class="btn btn-default" href="/members">{{ucfirst(trans('generic.cancel'))}}</a>
                </div>

            </div>{{--END x-content --}}
        </div>
    </div>
</div>

