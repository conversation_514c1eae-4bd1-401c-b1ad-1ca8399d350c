<div class="modal fade" id="editEventPopup">
    <div class="modal-dialog" role="document">

        <form method="POST" action="/events/0" class="form-horizontal" role="form" id="eventsEditForm">
            @csrf
            @method('PUT')
            <input type="hidden" name="calendar_id" value="{{ $calendar->id }}">


            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ucfirst(trans("generic.editthisevent"))}}</h4>
                </div>
                <div class="modal-body">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <label for="title" class="control-label">{{ucfirst(trans('generic.title'))}}</label>
                                    <input type="text" name="title" :value="eventToEdit.title" class="form-control" />
                                </div> {{--end formgroup--}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="start_datetime" class="control-label">{{ucfirst(trans('generic.startdatetime'))}}</label>
                                    <input type="text" id="eventStartDateTime" name="start_datetime" :value="mydat2ndat(eventToEdit.start_datetime)" class="form-control datetimepicker" />
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label class="mt-1">{{ trans('generic.allday') }}</label>
                                    <div class="material-switch">
                                        <input id="is_wholeday" v-model="wholeDay" type="checkbox" value="1" checked/>
                                        <label for="is_wholeday" class="label-success"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row" v-show="!wholeDay">
                            <div class="col-xs-12 col-md-6">
                                &nbsp;
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="duration" class="control-label">{{ucfirst(trans('generic.until'))}}</label>
                                    <input type="text" id="eventEndTime" class="datetimepicker" :value="getEnddate(eventToEdit.start_datetime, eventToEdit.duration)">
                                    <input type="hidden" id="eventDuration" name="duration" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <label for="description" class="control-label">{{ucfirst(trans('generic.description'))}}</label>
                                    <textarea name="description" id="sn-description" class="form-control summernote" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">{{ucfirst(trans('generic.save'))}}</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ucfirst(trans("generic.cancel"))}}</button>
                </div>
            </div>
        </form>
    </div>
</div>



