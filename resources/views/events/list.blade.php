<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h3>
                    <i class="fa fa-edit"></i> {{ ucfirst(trans('generic.currentAndFutureEvents')) }}
                    <small>
                        <a class='btn btn-success pull-right' data-toggle="modal" data-target="#newCalendarPopup" href="javascript:;" >{{ucfirst(trans('generic.new'))}} {{ucfirst(trans_choice('generic.events', 1))}}</a>
                    </small>
                </h3>
                <div class="clearfix"></div>
            </div>
            <div class="x_content">
                <table class="table">
                    <colgroup>
                        <col width="90px"/>
                        <col width="150px"/>
                        <col width="150px"/>
                        <col width="200px"/>
                        <col />
                    </colgroup>
                    <thead>
                    <tr>
                        <th>{{ucfirst(trans('generic.functions'))}}</th>
                        <th>{{ucfirst(trans('generic.date'))}}</th>
                        <th>{{ucfirst(trans('generic.at'))}}</th>
                        <th>{{ucfirst(trans('generic.until'))}}</th>
                        <th>{{ucfirst(trans('generic.title'))}}</th>
                        <th>{{ucfirst(trans('generic.description'))}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="event in currentOrFutureEvents">
                        <td>
                            <span data-toggle="tooltip"
                                  title="{{ucfirst(trans('generic.editthisevent'))}}"
                                  data-placement="right">
                                <button @click="setEventToEdit(event)"
                                        class="btn btn-success btn-xs"
                                        data-toggle="modal"
                                        data-target="#editEventPopup">
                                    <i class="glyphicon glyphicon-edit"></i>
                                </button>
                            </span>
                            <span data-toggle="tooltip"
                                  title="{{ucfirst(trans('generic.deletethisevent'))}}"
                                  data-placement="right">
                                <button @click="setEventToDelete(event)"
                                        class="btn btn-danger btn-xs"
                                        data-toggle="modal"
                                        data-target="#deleteConfirmEventPopup">
                                    <i class="glyphicon glyphicon-trash"></i>
                                </button>
                            </span>
                        </td>
                        <td>@{{ mydat2ndat(event.start_datetime).substr(0,16) }}</td>{{-- no seconds--}}
                        <td>@{{ getStartTime(event.start_datetime, event.duration) }}</td>
                        <td>@{{ getEndDateOrTime(event.start_datetime, event.duration) }}</td>
                        <td>@{{ event.title }}</td>
                        {{-- remove HTML tags --}}
                        <td>@{{ reformatEntry(event.description, 200) }}</td>
                    </tr>
                    <tr v-if="!events.length">
                        <td colspan="6">
                            {{ucfirst(trans('generic.nodatafound'))}}
                            &nbsp;=>&nbsp;
                            <a data-toggle="modal" data-target="#newCalendarPopup" href="javascript:;" >{{ucfirst(trans('generic.new'))}} {{ucfirst(trans_choice('generic.events', 1))}}</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h3>
                    <i class="fa fa-edit"></i> {{ ucfirst(trans('generic.pastEvents')) }}
                    <small>
                        <button class='btn btn-success pull-right' @click.prevent="togglePastEventsDisplay">
                            <span v-if="showPastEvents" style="color: white" class="fa fa-eye-slash">&nbsp;{{ ucfirst(trans('generic.hide')) }}</span>
                            <span v-else style="color: white" class="fa fa-eye">&nbsp;{{ ucfirst(trans('generic.show')) }}</span>
                        </button>
                    </small>
                    <div class="clearfix"></div>
                </h3>
            </div>
            <div class="x_content" v-if="showPastEvents">
                <table class="table">
                    <thead>
                    <tr>
                        <th>{{ucfirst(trans('generic.date'))}}</th>
                        <th>{{ucfirst(trans('generic.at'))}}</th>
                        <th>{{ucfirst(trans('generic.until'))}}</th>
                        <th>{{ucfirst(trans('generic.title'))}}</th>
                        <th>{{ucfirst(trans('generic.description'))}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="event in pastEvents">
                        <td>@{{ mydat2ndat(event.start_datetime).substr(0,16) }}</td>{{-- no seconds--}}
                        <td>@{{ getStartTime(event.start_datetime, event.duration) }}</td>
                        <td>@{{ getEndDateOrTime(event.start_datetime, event.duration) }}</td>
                        <td>@{{ event.title }}</td>
                        {{-- remove HTML tags --}}
                        <td>@{{ reformatEntry(event.description, 200) }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>


