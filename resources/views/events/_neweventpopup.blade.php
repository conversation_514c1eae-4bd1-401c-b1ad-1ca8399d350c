<div class="modal fade" id="newCalendarPopup">
    <div class="modal-dialog" role="document">

        <form method="POST" action="/events" class="form-horizontal" role="form">
            @csrf
            <input type="hidden" name="calendar_id" value="{{ $calendar->id }}">


            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ucfirst(trans("generic.newex"))}} {{trans("generic.calendarevent")}}</h4>
                </div>
                <div class="modal-body">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <label for="title" class="control-label">{{ucfirst(trans('generic.title'))}}</label>
                                    <input type="text" name="title" class="form-control" />
                                </div> {{--end formgroup--}}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="start_datetime" class="control-label">{{ucfirst(trans('generic.startdatetime'))}}</label>
                                    <input type="text" name="start_datetime" class="form-control datetimepicker" />
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="duration" class="control-label">{{ucfirst(trans('generic.duration'))}}</label>
                                    <select name="duration" class="form-control">
                                        <option value="1440">1 {{trans_choice('generic.days', 1)}}</option>
                                        <option value="15">15 {{trans('generic.minutes')}}</option>
                                        <option value="30">30 {{trans('generic.minutes')}}</option>
                                        <option value="45">45 {{trans('generic.minutes')}}</option>
                                        <option value="60">1 {{trans_choice('generic.hours', 1)}}</option>
                                        <option value="90">1.5 {{trans_choice('generic.hours', 1)}}</option>
                                        <option value="120">2 {{trans_choice('generic.hours', 1)}}</option>
                                        <option value="180">3 {{trans_choice('generic.hours', 1)}}</option>
                                        <option value="240">4 {{trans_choice('generic.hours', 1)}}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12 col-md-12">
                                <div class="form-group">
                                    <label for="description" class="control-label">{{ucfirst(trans('generic.description'))}}</label>
                                    <textarea name="description" class="form-control summernote" rows="5"></textarea>

                                </div> {{--end formgroup--}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">{{ucfirst(trans('generic.save'))}}</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ucfirst(trans("generic.cancel"))}}</button>
                </div>
            </div>
        </form>
    </div>
</div>



