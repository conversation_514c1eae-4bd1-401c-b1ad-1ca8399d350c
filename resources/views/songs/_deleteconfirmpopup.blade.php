<div class="modal fade" id="deleteFileConfirmPopup">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{ucfirst(trans("generic.areyousure"))}}?</h4>
            </div>
            <div class="modal-body">
                <h5><strong>@{{songitemName}}</strong></h5>
                <p>{{ucfirst(trans('generic.youareabouttodeletefile'))}}.</p>
                <p>{{ucfirst(trans('generic.thiscannotbeundone'))}}!</p>
            </div>
            <div class="modal-footer">
                <form :action="'/songitems/' + songitemToDeleteId" method = "post">
                    <input name="_method" type="hidden" value="DELETE">
                    {{-- token --}}
                    {{ csrf_field() }}
                    <button type="submit" class="btn btn-danger">{{ucfirst(trans('generic.deletethisfile'))}}</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ucfirst(trans("generic.cancel"))}}</button>
                </form>
            </div>
        </div>
    </div>
</div>



