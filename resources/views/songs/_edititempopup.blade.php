<div class="modal fade" id="editSongitemPopup">
    <div class="modal-dialog modal-lg" role="document">
        <form method="POST" :action="'/songitems/' + songitemToEdit.id" enctype="multipart/form-data" id="songitemForm">
            @csrf
            @method('PUT')
            <input type="hidden" name="song_id" value="{{ $song->id }}">

            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ucfirst(trans("generic.edit"))}} {{trans_choice('generic.titleparts', 1)}}</h4>
                </div>
                <div class="modal-body">
                    <div class="container">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="name" class="control-label">{{ucfirst(trans('generic.name'))}}</label>
                                <input type="text" name="name" class="form-control" v-model="songitemToEdit.name" />
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-md-9 col-xs-12">
                            <div class="form-group">
                                <label for="linktype" class="control-label">{{ucfirst(trans('generic.linktype'))}}</label>
                                <select name="dummylinktype" class="form-control" v-model="linktype" disabled>
                                    <option value="" disabled>{{trans('generic.pleasechoose')}}</option>
                                    @foreach($fileTypes as $ftValue => $filetype)
                                        <option v-if="songitemToEdit.linktype === '{{$ftValue}}'" value="{{$ftValue}}"
                                                selected>{{$filetype}}</option>
                                        <option v-else value="{{$ftValue}}">{{$filetype}}</option>
                                    @endforeach
                                </select>
                                <input type="hidden" name="linktype" :value="songitemToEdit.linktype" />
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-md-3 col-xs-12" v-if="isMusicFile">
                            <div class="form-group">
                                <label for="own_recording" class="control-label">{{ucfirst(trans('generic.ownrecording'))}}</label>
                                <br/>
                                <div class="btn-group" data-toggle="buttons">
                                    <label :class="{ active: ownRecordingYes }" class="btn btn-default">
                                        <input type="radio" name="own_recording"
                                               id="yes" value="1"> {{ucfirst(trans('generic.yes'))}}
                                    </label>
                                    <label :class="{ active: ownRecordingNo }" class="btn btn-default ">
                                        <input type="radio" name="own_recording"
                                               id="no" value="0"> {{ucfirst(trans('generic.no'))}}
                                    </label>
                                </div>
                            </div> {{--end formgroup--}}
                        </div>
                        {{----}}
                        <div class="col-md-12" v-if="isLink">
                            <label>Web URL</label>
                            <input class="form-control" name="weblink" type="url" v-model="webLink" />
                            <span class="text-danger" v-if="webLink!==''">
                                {{ucfirst(trans('generic.thiswillbethenewcurrentfile'))}}
                            </span>
                        </div>
                        <div v-else class="col-md-12">
                            <div class="form-group}}">
                                <label for="fileupload" class="control-label">{{ucfirst(trans('generic.linkedfile'))}}</label>
                                {{-- Vue component --}}
                                <fileupload
                                    buttontext="{{ucfirst(trans('generic.fileupload'))}}"
                                    helptext="{{ucfirst(trans('generic.thiswillbethenewcurrentfile'))}}"
                                    :max-filesize="{{config('app.MAXUPLOADFILESIZE')}}"
                                    fileinputid="songitemfile"
                                    fileinputname="songitemfile"
                                    @filewasreceived="validateSongitemSaveable"
                                    @componentcleared="validateSongitemSaveable"
                                    @maxfilesizwaseexceeded="maxFileSizeWasExceeded">
                                </fileupload>
                            </div> {{--end formgroup--}}
                        </div>
                        {{----}}
                        <div class="col-md-12" >
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>{{ucfirst(trans_choice('generic.files', 1))}}</th>
                                    <th>{{ucfirst(trans('generic.originalname'))}}</th>
                                    <th>{{ucfirst(trans('generic.archive'))}}</th>
                                    <th>{{ucfirst(trans('generic.lastupdate'))}}</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <th colspan="4" class="info">{{ucfirst(trans('generic.currentfile'))}}</th>
                                    </tr>
                                    <tr v-for="songitemfile in songitemfilesActual">
                                        <td v-if="songitemfile.itemUrl !== ''">
                                            <a :href="songitemfile.itemUrl" target="_blank">@{{ songitemfile.url }}</a>
                                        </td>
                                        <td v-else-if="linktype==='1'||linktype==='2'">
                                            <a :href="songitemfile.url" target="_blank">@{{ songitemfile.url }}</a>
                                        </td>
                                        <td v-else>
                                            @{{ songitemfile.url }}
                                        </td>
                                        <td v-if="linktype==='1'||linktype==='2'">&nbsp;</td>
                                        <td v-else>@{{ songitemfile.original_name }}</td>
                                        <td>@{{ songitemfile.history_date }}</td>
                                        <td>@{{ songitemfile.updated_at }}</td>
                                    </tr>
                                    <tr>
                                        <th colspan="4" class="info">{{ucfirst(trans_choice('generic.archivefiles', 2))}}</th>
                                    </tr>
                                    <tr v-for="songitemfile in songitemfilesArchive">
                                        <td v-if="songitemfile.itemUrl !== ''">
                                            <a :href="songitemfile.itemUrl" target="_blank">@{{ songitemfile.url }}</a>
                                        </td>
                                        <td v-else-if="linktype==='1'||linktype==='2'">
                                            <a :href="songitemfile.url" target="_blank">@{{ songitemfile.url }}</a>
                                        </td>
                                        <td v-else>
                                            @{{ songitemfile.url }}
                                        </td>
                                        <td v-if="linktype==='1'||linktype==='2'">&nbsp;</td>
                                        <td v-else>@{{ songitemfile.original_name }}</td>
                                        <td>@{{ songitemfile.history_date }}</td>
                                        <td>@{{ songitemfile.updated_at }}</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>

                        {{-- theres also the reference and the songid but both may not be changed --}}

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" :disabled="!songitemSaveable">{{ucfirst(trans('generic.save'))}}</button>
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal">{{ucfirst(trans("generic.cancel"))}}</button>
                </div>
            </div>
        </form>
    </div>
</div>



