{{-- Inject Scolavisa library --}}
@inject('Mydat2Ndat', 'Scolavisa\scolib\Mydat2Ndat')

<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h2><i class="fa fa-music"></i> {{ucfirst(trans('generic.genericdata'))}}</h2>
                <ul class="nav navbar-right panel_toolbox">
                    <li>
                        <a class="collapse-link"><i class="fa fa-chevron-up"></i></a>
                    </li>
                </ul>
                <div class="clearfix"></div>
            </div>

            <div class="x_content"> <!-- START x-content -->
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('title') ? ' has-error' : '' }}">
                                <label for="title" class="control-label">{{ucfirst(trans('generic.title'))}}</label>
                                <input type="text" name="title" value="{{ old('title', $song->title ?? '') }}" class="form-control">
                                @if ($errors->has('title'))
                                    <span class="help-block"><strong>{{ $errors->first('title') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('year') ? ' has-error' : '' }}">
                                <label for="year" class="control-label">{{ucfirst(trans('generic.year'))}}</label>
                                <input type="text" name="year" value="{{ old('year', $song->year ?? '') }}" class="form-control">
                                @if ($errors->has('year'))
                                    <span class="help-block"><strong>{{ $errors->first('year') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('time_in_setlist') ? ' has-error' : '' }}">
                                <label for="time_in_setlist" class="control-label">{{ucfirst(trans('generic.timeinsetlist'))}}</label>
                                <i class="fa fa-question-circle" title="{{ucfirst(trans('generic.explain_timeinsetlist'))}}" data-toggle="tooltip"></i>
                                <input type="text" name="time_in_setlist" value="{{ old('time_in_setlist', $song->time_in_setlist ?? '') }}" class="form-control">
                                @if ($errors->has('time_in_setlist'))
                                    <span class="help-block"><strong>{{ $errors->first('time_in_setlist') }}</strong></span>
                                @endif
                            </div>
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('tags') ? ' has-error' : '' }}">
                                <label for="tags" class="control-label">{{ucfirst(trans('generic.tags'))}}</label>
                                <i class="fa fa-question-circle" title="{{ucfirst(trans('generic.explain_tags'))}}" data-toggle="tooltip"></i>
                                <input type="text" name="tags" value="{{ old('tags', $song->tags ?? '') }}" class="form-control">
                                @if ($errors->has('tags'))
                                    <span class="help-block"><strong>{{ $errors->first('tags') }}</strong></span>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{----}}
                    <div class="row">
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('date_in_repertoire') ? ' has-error' : '' }}">
                                <?php
                                $date = isset($song) ?
                                    $Mydat2Ndat::getNdat(old('date_in_repertoire', $song->date_in_repertoire)) :
                                    $Mydat2Ndat::getNdat(old('date_in_repertoire'));
                                ?>
                                <label for="date_in_repertoire" class="control-label">{{ucfirst(trans('generic.dateinrepertoire'))}}</label>
                                <input type="text" name="date_in_repertoire" value="{{ $date }}" class="form-control datepicker">
                                @if ($errors->has('date_in_repertoire'))
                                    <span class="help-block"><strong>{{ $errors->first('date_in_repertoire') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('archive_number') ? ' has-error' : '' }}">
                                <label for="archive_number" class="control-label">{{ucfirst(trans('generic.archivenumber'))}}</label>
                                <i class="fa fa-question-circle" title="{{ucfirst(trans('generic.explain_archivenumber'))}}" data-toggle="tooltip"></i>
                                <input type="text" name="archive_number" value="{{ old('archive_number', $song->archive_number ?? '') }}" class="form-control">
                                @if ($errors->has('archive_number'))
                                    <span class="help-block"><strong>{{ $errors->first('archive_number') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('composer1') ? ' has-error' : '' }}">
                                <label for="composer1" class="control-label">{{ucfirst(trans('generic.composer'))}} 1</label>
                                <input type="text" name="composer1" value="{{ old('composer1', $song->composer1 ?? '') }}" class="form-control">
                                @if ($errors->has('composer1'))
                                    <span class="help-block"><strong>{{ $errors->first('composer1') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-xs-12 col-md-3">
                            <div class="form-group{{ $errors->has('composer2') ? ' has-error' : '' }}">
                                <label for="composer2" class="control-label">{{ucfirst(trans('generic.composer'))}} 2</label>
                                <input type="text" name="composer2" value="{{ old('composer2', $song->composer2 ?? '') }}" class="form-control">
                                @if ($errors->has('composer2'))
                                    <span class="help-block"><strong>{{ $errors->first('composer2') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                    </div>
                    {{----}}
                    <div class="row">
                        <div class="col-xs-12 col-md-12">
                            <div class="form-group{{ $errors->has('remarks') ? ' has-error' : '' }}">
                                <label for="remarks" class="control-label">{{ucfirst(trans('generic.remarks'))}}</label>
                                <textarea name="remarks" class="form-control" rows="3">{{ old('remarks', $song->remarks ?? '') }}</textarea>
                                @if ($errors->has('remarks'))
                                    <span class="help-block"><strong>{{ $errors->first('remarks') }}</strong></span>
                                @endif
                            </div> {{--end formgroup--}}
                        </div>
                    </div>

                </div>
            </div>{{--END x-content --}}
        </div>
    </div>
</div>

@if(isset($song) )
    @include('songs._filelistupload')   
@endif

