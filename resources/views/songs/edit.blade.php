@extends('layouts.app')

@section("content")
    <input type="hidden" id="songId" value="{{$song->id}}"/>

    <div class="container" id="songs">

        <div class="page-title">
            <div class="title_left">
                <h3>{{ucfirst(trans('generic.edit'))}} {{ucfirst(trans_choice('generic.titles', 1))}}</h3>
            </div>
        </div>

        <form method="POST" action="/songs/{{ $song->id }}" class="form" id="songform">
            @csrf
            @method('PUT')
            @include('songs._entry')
            <div class="form-group">
                <button type="submit" class="btn btn-primary">{{ucfirst(trans('generic.save'))}}</button>
                <a class="btn btn-default" href="/songs">{{ucfirst(trans('generic.cancel'))}}</a>
            </div>
        </form>

        @include("songs._deleteconfirmpopup")
        @include("songs._edititempopup")
        @include("songs._newitempopup")

    </div>
@endsection
