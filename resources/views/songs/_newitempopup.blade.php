<div class="modal fade" id="newSongitemPopup">
    <div class="modal-dialog modal-lg" role="document">
        <form method="POST" action="/songitems" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="song_id" value="{{ $song->id }}">

            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ucfirst(trans("generic.newtitlepart"))}}</h4>
                </div>
                <div class="modal-body">
                    <div class="container">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="name" class="control-label">{{ucfirst(trans('generic.name'))}}</label>
                                <input type="text" name="name" class="form-control" v-model="itemName"/>
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-md-9 col-xs-12">
                            <div class="form-group">
                                <label for="linktype" class="control-label">{{ucfirst(trans('generic.linktype'))}}</label>
                                <select name="linktype" id="heard" class="form-control" required="" v-model="linktype">
                                    <option value="" disabled>{{trans('generic.pleasechoose')}}</option>
                                    @foreach($fileTypes as $ftValue => $filetype)
                                        <option value="{{$ftValue}}">{{$filetype}}</option>
                                    @endforeach
                                </select>
                            </div> {{--end formgroup--}}
                        </div>
                        <div class="col-md-3 col-xs-12" v-if="isMusicFile">
                            <div class="form-group">
                                <label for="own_recording" class="control-label">{{ucfirst(trans('generic.ownrecording'))}}</label>
                                <br/>
                                <div class="btn-group" data-toggle="buttons">
                                    <label :class="{ active: ownRecordingYes }" class="btn btn-default">
                                        <input type="radio" name="own_recording"
                                               id="yes"> {{ucfirst(trans('generic.yes'))}}
                                    </label>
                                    <label :class="{ active: ownRecordingNo }" class="btn btn-default ">
                                        <input type="radio" name="own_recording"
                                               id="no"> {{ucfirst(trans('generic.no'))}}
                                    </label>
                                </div>
                            </div> {{--end formgroup--}}
                        </div>

                        {{-- show this bit after a linktype is chosen --}}
                        <div v-if="hasLinkType">
                            <div class="col-md-12" v-if="isLink">
                                <label>Web URL</label>
                                <input class="form-control" name="weblink" type="url" v-model="webLink"/>
                            </div>
                            <div v-else class="col-md-12">
                                <div class="form-group}}">
                                    <label for="fileupload" class="control-label">{{ucfirst(trans('generic.linkedfile'))}}</label>
                                    {{-- Vue component --}}
                                    <fileupload
                                        buttontext="{{ucfirst(trans('generic.fileupload'))}}"
                                        helptext=""
                                        :max-filesize="{{config('app.MAXUPLOADFILESIZE')}}"
                                        fileinputid="newsongitemfile"
                                        fileinputname="songitemfile"
                                        @filewasreceived="validateSongitemSaveable"
                                        @componentcleared="validateSongitemSaveable"
                                        @maxfilesizwaseexceeded="maxFileSizeWasExceeded">
                                    </fileupload>
                                </div> {{--end formgroup--}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" :disabled="!songitemSaveable">{{ucfirst(trans('generic.save'))}}</button>
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal">{{ucfirst(trans("generic.cancel"))}}</button>
                </div>
            </div>
        </form>
    </div>
</div>



