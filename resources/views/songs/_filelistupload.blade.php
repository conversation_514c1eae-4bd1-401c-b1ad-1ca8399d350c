<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h2><i class="fa fa-file"></i> {{ucfirst(trans_choice('generic.titleparts',2))}}</h2>
                <div class="nav navbar-right">
                    <button
                        @click.prevent="resetFileupload"
                        class="btn btn-primary"
                        data-target="#newSongitemPopup"
                        data-toggle="modal">{{ucfirst(trans('generic.newtitlepart'))}}
                    </button>
                </div>
                <div class="clearfix"></div>
            </div>

            <div class="x_content"> <!-- START x-content -->
                @if(strpos(Route::getCurrentRoute()->getActionName(), "create") !== false)
                    <p>
                        {{-- mock file area. Real upload will be available after save, on the edit page --}}
                        {{trans('generic.explain_fileuploadaftersave')}}
                    </p>
                @else
                    @if(!empty($song->songitems))
                        <table class="table">
                            <thead>
                            <tr>
                                <th>{{ucfirst(trans('generic.functions'))}}</th>
                                <th>{{ucfirst(trans('generic.title'))}}</th>
                                <th>{{ucfirst(trans('generic.type'))}}</th>
                                <th>{{ucfirst(trans_choice('generic.files',1))}}</th>
                                <th>{{ucfirst(trans('generic.ownrecording'))}}</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr v-for="songitem in songitems">
                                    <td>
                                        <span data-toggle="tooltip" title="{{ucfirst(trans('generic.editthisfile'))}}" data-placement="right">
                                            <button @click.prevent="getSongitemToEdit(songitem.id)" class="btn btn-success btn-xs" data-toggle="modal" data-target="#editSongitemPopup">
                                                <span class="glyphicon glyphicon-edit"></span>
                                            </button>
                                        </span>
                                        <span data-toggle="tooltip" title="{{ucfirst(trans('generic.deletethisfile'))}}" data-placement="right">
                                            <button @click.prevent="fillDeleteForm(songitem.id)" class="btn btn-danger btn-xs" data-toggle="modal" data-target="#deleteFileConfirmPopup">
                                                <span class="glyphicon glyphicon-trash"></span>
                                            </button>
                                        </span>
                                    </td>
                                    <td>@{{ songitem.name }}</td>
                                    <td>@{{ songitem.typeDesc }}</td>
                                    <td v-if="songitem.itemUrl === ''" class="grayed-out">@{{ songitem.url }}</td>
                                    <td v-else><a :href="songitem.itemUrl" target="_blank">@{{ songitem.url }}</a></td>
                                    <td>@{{ songitem.own_recordingDesc }}</td>
                                </tr>
                            </tbody>
                        </table>
                    @else
                        {{ucfirst(trans('generic.nofilesyet'))}}
                    @endif
                @endif
            </div>
        </div>
    </div>
</div>

