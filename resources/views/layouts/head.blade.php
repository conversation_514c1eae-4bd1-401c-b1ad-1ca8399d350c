<!DOCTYPE html>
<html lang="{{App::getLocale()}}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    {{-- CSRF Token --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Yfantis</title>

    {{-- Styles --}}
    {{--<link href="/css/all.css" rel="stylesheet"> only needed when we compile some external styles like jQuery UI --}}
    <link href="/css/app.css" rel="stylesheet">
    <link href="/css/all.css" rel="stylesheet">

    {{-- include summernote css, werkt niet via nodsjs src --}}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.6/summernote.css" rel="stylesheet">

    {{-- pnotify --}}
    <link href="/css/pnotify.custom.min.css" media="all" rel="stylesheet" type="text/css" />

    {{-- Scripts --}}
    <script>
		window.Laravel = <?php echo json_encode([
			'csrfToken' => csrf_token(),
		]); ?>;
    </script>

    {{-- translations for javascript --}}
    {{-- see https://laracasts.com/discuss/channels/vue/use-trans-in-vuejs --}}
    <script>
		window.trans = <?php
        // copy all translations from /resources/lang/CURRENT_LOCALE/* to global JS variable
        // $lang_files = File::files(resource_path() . '/lang/' . App::getLocale() . '/generic.php');
        // MS: I will only get the appropriate generic file, to save on request size
        // I will leave the foreach in case we need more in future
        $lang_files = [
            resource_path() . '/lang/' . App::getLocale() . '/generic.php',
            resource_path() . '/lang/' . App::getLocale() . '/email.php',
        ];
		$trans = [];
        foreach ($lang_files as $f) {
            $filename = pathinfo($f)['filename'];
            $trans[$filename] = trans($filename);
        }
        echo json_encode($trans);
		?>;
    </script>

</head>
