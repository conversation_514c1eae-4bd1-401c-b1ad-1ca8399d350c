<div class="col-md-3 left_col menu_fixed">
    <div class="left_col scroll-view">
        <div class="navbar nav_title" style="border: 0;">
            <img src="/images/logo_yf.png" style="width:100%"/>
        </div>
        <div class="clearfix"></div>

        {{-- Authentication Left Navigation Menu --}}
        @if (Auth::guest())
            {{-- IF USER NOT AUTHENTICATED LEFT MENU IS EMPTY--}}
        @else
            {{-- menu profile quick info --}}
            <div class="profile">
                @if(!empty(Auth::user()->avatar))
                    <div class="profile_pic">
                        {{--user picture--}}&nbsp;
                        <img src="{{Auth::user()->avatar}}" height="70px"/>
                    </div>
                @endif
                <div class="profile_info">
                    <span>{{ucfirst(trans('generic.welcome'))}},</span>
                    <h2>{{Auth::user()->name}}</h2>
                    {{-- Domain --}}
                    <h3>{{Auth::user()->domain->name}}</h3>
                    @if(!empty(Auth::user()->domain->url))
                        <a class="btn btn-success" href="{{Auth::user()->domain->url}}" target="_blank">Open Website</a>
                    @endif
                </div>
            </div>
            {{-- /menu profile quick info --}}

            <div class="clearfix"></div>
            {{-- show we're in the test environment--}}

            {{-- litle bit of spacing --}}
            <br/>&nbsp;<br/>

            {{-- sidebar menu --}}
            <div id="sidebar-menu" class="main_menu_side hidden-print main_menu">
                <div class="menu_section">
                    <ul class="nav side-menu">
                        <li><a href="{{ url('/home') }}"><i class="fa fa-home"></i>Home</a></li>


                        {{-- DOMAIN --}}
                        @if(Auth::user()->userIsA('admin') && Auth::user()->userHasModule('admin'))
                            {{-- administer all domains (Scolavisa employee) --}}
                            <li><a><i class="fa fa-globe"></i> {{ucfirst(trans_choice('generic.domains',2))}} <span class="fa fa-chevron-down"></span></a>
                                <ul class="nav child_menu">
                                    <li>
                                        <a href="{{url('/domains')}}">
                                            {{ucfirst(trans('generic.list'))}}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        @elseif(Auth::user()->userIsA('admin'))
                            {{-- only edit your own domain --}}
                            <li><a><i class="fa fa-globe"></i> {{ucfirst(trans_choice('generic.domains',1))}} <span class="fa fa-chevron-down"></span></a>
                                <ul class="nav child_menu">
                                    <li>
                                        <a href="{{url('/domains/' . Auth::user()->domain->id . '/edit')}}">
                                            {{ucfirst(trans('generic.edit'))}}
                                        </a>
                                    </li>
                                </ul>
                            </li>

                        @endif

                        {{-- REPERTOIRE --}}
                        @if(Auth::user()->userIsA('admin') && Auth::user()->userHasModule('repertoire'))
                            <li><a><i class="fa fa-music"></i> {{ucfirst(trans('generic.repertoire'))}} <span class="fa fa-chevron-down"></span></a>
                                <ul class="nav child_menu">
                                    <li>
                                        <a href="{{url('/songs')}}">
                                            {{ucfirst(trans_choice('generic.titles', 2))}}
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        @endif

                        {{-- CALENDARS --}}
                        @if(Auth::user()->userIsA('admin') && Auth::user()->userHasModule('calendar'))
                            <li><a><i class="fa fa-calendar"></i> {{ucfirst(trans_choice('generic.calendars', 2))}} <span class="fa fa-chevron-down"></span></a>
                                <ul class="nav child_menu">
                                    <li>
                                        <a href="{{url('/calendars')}}">{{ucfirst(trans('generic.list'))}}</a>
                                    </li>
                                </ul>
                            </li>
                        @endif

                        {{-- MEMBERS --}}
                        @if(Auth::user()->userIsA('admin') && Auth::user()->userHasModule('members'))
                            <li><a><i class="fa fa-database"></i> {{ucfirst(trans_choice('generic.members', 2))}} <span class="fa fa-chevron-down"></span></a>
                                <ul class="nav child_menu">
                                    <li>
                                        <a href="{{url('/members')}}">{{ucfirst(trans('generic.memberslist'))}} </a>
                                    </li>
                                    <li>
                                        <a href="{{url('/memberfunctions')}}">{{ucfirst(trans('generic.functions'))}} </a>
                                    </li>
                                    {{--nog niet af--}}
                                    <li>
                                        <a href="{{url('/membersemail')}}">{{ucfirst(trans('generic.email'))}} </a>
                                    </li>
                                </ul>
                            </li>
                        @endif

                    </ul>
                </div>

            </div>
            {{-- /sidebar menu --}}
        @endif

        {{-- /menu footer buttons --}}
        <div class="sidebar-footer hidden-small">
{{--            @if (Auth::guest())
                --}}{{-- IF USER NOT AUTHENTICATED FOOTER MENU IS EMPTY--}}{{--
            @else
                <a data-toggle="tooltip" data-placement="top" title="{{ucfirst(trans('generic.logout'))}}" href="{{ url('/logout') }}"
                   onclick="event.preventDefault();
                    document.getElementById('logout-form').submit();">
                    <span class="glyphicon glyphicon-off" aria-hidden="true"></span>
                </a>
            @endif--}}
        </div>

        {{-- /menu footer buttons --}}
    </div>
</div>
