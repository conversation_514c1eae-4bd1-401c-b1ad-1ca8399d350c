{{-- top navigation --}}
<div class="top_nav">
    <div class="nav_menu">
        <nav>
            <div class="nav toggle">
                <a id="menu_toggle"><i class="fa fa-bars"></i></a>
            </div>

            {{-- Right Side Of Navbar --}}
            <ul class="nav navbar-nav navbar-right">
                {{-- Authentication Links --}}
                @if (Auth::guest())
                    @if ( isset($hideLogin) && $hideLogin )
                        {{-- No login to show --}}
                        <li id="nologinlink"></li>
                    @else
                        <li id="loginlink"><a href="{{ url('/login') }}">Login</a></li>
                    @endif
                @else
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                            {{ Auth::user()->name }} <span class="caret"></span>
                        </a>

                        <ul class="dropdown-menu" role="menu">
                            <li>
                                <a href="/users/profile">{{--fixme! dit werkt niet!--}}<br/></a>
                            </li>
                            <li>
                                <a href="{{ url('/logout') }}"
                                   onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                    {{ucfirst(trans('generic.logout'))}}
                                </a>
                                <form id="logout-form" action="{{ url('/logout') }}" method="POST" style="display: none;">
                                    {{ csrf_field() }}
                                </form>
                            </li>
                        </ul>
                    </li>
                    @if(Auth::user()->userIsA('admin'))
                        <li class="dropdown">
                            <a href="javascript:;" class="user-profile dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                                {{ucfirst(trans('generic.settings'))}} <span class="caret"></span>
                            </a>
                            <ul class="dropdown-menu dropdown-usermenu pull-right" role="menu">
                                <li>
                                    <a href="{{  url('/adminpage') }}">{{--fixme! dit werkt niet!--}}<br/></a>
                                </li>
                                <li>
                                    <a href="{{ url('/adminpage') }}">
                                        {{ucfirst(trans('generic.adminpage'))}}
                                    </a>

                                </li>
                            </ul>

                        </li>

                    @endif
                @endif
            </ul>
        </nav>
    </div>
</div>
{{-- /top navigation --}}