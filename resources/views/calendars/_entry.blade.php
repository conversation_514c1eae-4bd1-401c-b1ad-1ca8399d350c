<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <div class="x_panel">
            <div class="x_title">
                <h3>
                    <i class="fa fa-calendar"></i> {{ucfirst(trans('generic.edit'))}} {{ucfirst(trans_choice('generic.calendars', 1))}}
                </h3>
                <div class="clearfix"></div>
            </div>
            <div class="x_content"> <!-- START x-content -->

                {{----}}
                <div class="row">
                    <div class="col-xs-12 col-md-3">
                        <div class="form-group{{ $errors->has('name') ? ' has-error' : '' }}">
                            <label for="name" class="control-label">{{ucfirst(trans('generic.name'))}}</label>
                            <i class="fa fa-question-circle" data-html="true" data-toggle="tooltip" title="{!! trans('generic.explain_usenameforshortcode')!!}"></i>
                            <input type="text" name="name" id="name" value="{{ old('name', $calendar->name) }}" class="form-control">
                            @if ($errors->has('name'))
                                <span class="help-block"><strong>{{ $errors->first('name') }}</strong></span>
                            @endif
                        </div> {{--end formgroup--}}
                    </div>
                    <div class="col-xs-12 col-md-3">
                        <div class="form-group">
                            <label class="control-label" for="is_public">
                                {{ucfirst(trans('generic.public'))}}
                            </label>
                            <i class="fa fa-question-circle" data-toggle="tooltip" title="{{trans('generic.explain_publicsetting')}}"></i>
                            <div class="material-switch">
                                @if(isset($calendar) && $calendar->is_public === 1)
                                    <input id="is_public" name="is_public" type="checkbox" value="1" checked/>
                                @else
                                    <input id="is_public" name="is_public" type="checkbox" value="1" />
                                @endif
                                <label for="is_public" class="label-success"></label>&nbsp;
                            </div>
                        </div> {{--end formgroup--}}
                    </div>
                </div>

                {{----}}
                <div class="row">
                    <div class="col-xs-12 col-md-6">
                        <div class="form-group{{ $errors->has('description') ? ' has-error' : '' }}">
                            <label for="description" class="control-label">{{ucfirst(trans('generic.description'))}}</label>
                            <textarea name="description" id="description" class="form-control" rows="3">{{ old('description', $calendar->description) }}</textarea>
                            @if ($errors->has('description'))
                                <span class="help-block"><strong>{{ $errors->first('description') }}</strong></span>
                            @endif
                        </div> {{--end formgroup--}}
                    </div>
                </div>

                {{----}}
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">{{ucfirst(trans('generic.save'))}}</button>
                    <a class="btn btn-default" href="/calendars">{{ucfirst(trans('generic.cancel'))}}</a>
                </div>

            </div>{{--END x-content --}}
        </div>
    </div>
</div>

