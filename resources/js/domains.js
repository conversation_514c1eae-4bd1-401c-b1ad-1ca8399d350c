import Vue from "vue";
export function domains() {
    new Vue({
        el: "#domains",
        data() {
            return {
                socials: [],
                newSocialDdisplayName: "",
                newSocialLink: "",
            }
        },
        methods: {
            addSocial() {
                this.socials.push({
                    display_name: this.newSocialDdisplayName,
                    link: this.newSocialLink
                });
                this.newSocialDdisplayName = "";
                this.newSocialLink = "";
            },
            removeSocial(index) {
                this.socials.splice(index, 1);
            }
        },
        mounted() {
            console.log("domains page.");
            // generated in _entry.blade.php
            this.socials = socials;
        }
    });
}