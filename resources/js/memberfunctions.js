import Vue from "vue";
export function memberfunctions() {

    Vue.component('fileupload', require('./components/FileUpload.vue').default);
    Vue.component('modal', require('./components/Modal').default);

    new Vue({
        el: "#memberfunctions",
        data: {
            memberfunctionOptions: [],      // all possible memberfunctions
            editFunctionId: 0,
            editFunctionDesc: '',
            delFunctionId: 0,
            delFunctionDesc: ''
        },
        mounted() {
            console.log("memberfunctions page.");
            this.getFunctionOptions();
        },
        methods: {
            getFunctionOptions() {
                this.resetFields();
                console.log("Retrieving functionoptions");
                axios.get('/api/memberfunctions')
                    .then(response => {
                        this.memberfunctionOptions = response.data;
                    })
                    .catch(error => {
                        console.log("Error retrieving functionoptions");
                        console.log(error)
                    });
            },
            fillEditFormMemberfunction(functionid) {
                if (functionid === 0) {
                    this.resetFields();
                } else {
                    this.editFunctionId = functionid;
                }
            },
            fillDeleteFormMemberfunction(functionid) {
                this.delFunctionId = functionid;
            },
            saveMemberfunctionChanges() {
                axios.post("/api/savememberfunction", {"id": this.editFunctionId, 'desc': this.editFunctionDesc})
                    .then(response => {
                        if (response.data.result === "success") {
                            // show notice
                            this.pnotify_success(this.trans('generic.savesuccess'), this.trans('generic.changessaved'));
                        } else {
                            // show error notice
                            this.pnotify_danger(this.trans('generic.savefailed'), response.data.result);
                        }
                    })
                    .catch(err => {
                        this.pnotify_danger(this.trans('generic.savefailed'), this.trans('generic.anerroroccurredduringsave'));
                        console.log(`Error saving memberfunction data`);
                        console.log(`${err}`);
                    })
                    .then(() => {
                        // in any case re-retrieve local values
                        this.getFunctionOptions();
                    })
            },
            deleteMemberfunction() {
                axios.delete(`/api/delmemberfunction/${this.delFunctionId}`)
                    .then(response => {
                        if (response.data.result === "success") {
                            this.pnotify_success(this.trans('generic.deletesuccess'), this.trans('generic.functionremoved'));
                        } else {
                            this.pnotify_danger(this.trans('generic.deletefail'), this.trans('generic.functionnotremoved'));
                        }
                    })
                    .catch(err => {
                        this.pnotify_danger(this.trans('generic.deletefail'), this.trans('generic.functionnotremoved'));
                        console.log(`Error deleting memberfunction data`);
                        console.log(`${err}`);
                    })
                    .then(() => {
                        // in any case re-retrieve local values
                        this.getFunctionOptions();
                    })
            },
            resetFields() {
                this.editFunctionId = 0;
                this.editFunctionDesc = '';
                this.delFunctionId = 0;
                this.delFunctionDesc = '';
            }
        },
        watch: {
            editFunctionId() {
                if (this.editFunctionId > 0) {
                    // find the correct function
                    const theFunction = this.memberfunctionOptions.filter(f => {
                        return f.id === this.editFunctionId;
                    })[0];
                    if (theFunction !== undefined) {
                        this.editFunctionDesc = theFunction.description;
                    } else {
                        this.editFunctionDesc = "";
                    }
                }
            },
            delFunctionId() {
                // find the correct function
                const theFunction = this.memberfunctionOptions.filter(f => {
                    return f.id === this.delFunctionId;
                })[0];
                this.delFunctionDesc = theFunction.description;
            }
        }
    });
}
