window._ = require('lodash');

/**
 * We'll load jQ<PERSON>y and the Bootstrap jQuery plugin which provides support
 * for JavaScript based Bootstrap features such as modals and tabs. This
 * code may be modified to fit the specific needs of your application.
 */

try {
    window.$ = window.jQuery = require('jquery');
	require('bootstrap-sass');
	require('gentelella/build/js/custom');
	require('jquery-mousewheel');
	require("jquery-ui");
	require("jquery-ui/ui/widgets/datepicker");
	require("jquery-ui/ui/i18n/datepicker-nl");
    require("moment");
    require('summernote');
	require('./jquery.datetimepicker.full');
	require('promise-polyfill/src/polyfill');
    require('codemirror');
} catch (e) {}

/**
 * We'll load the axios HTTP library which allows us to easily issue requests
 * to our Laravel back-end. This library automatically handles sending the
 * CSRF token as a header based on the value of the "XSRF" token cookie.
 */

window.axios = require('axios');

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

/**
 * Next we will register the CSRF Token as a common header with Axios so that
 * all outgoing HTTP requests automatically have it attached. This is just
 * a simple convenience so we don't have to attach every token manually.
 */

// {**} sometimes querySelector this stops working
// {**} (if executed too early, before <canvas> is loaded)
// {**} https://stackoverflow.com/questions/43147320/document-queryselector-doesnt-work-in-js-file
// {**} Laravel.csrfToken is available, why not use it?

//let token = document.head.querySelector('meta[name="csrf-token"]'); see {** ^^}
let token = window.Laravel.csrfToken;

if (token) {
//    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content; see {** ^^}
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}
