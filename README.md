# Scolavisa functions library

This library contains functions in classes that are being (re)used in Scolavisa projects

## Documentation
The documentation is located in the .phpdoc directory. 
The generator for these pages is https://docs.phpdoc.org/3.0/
Install by following the instructions there. Currently, we use php7.4 to generate the docs. 
It needs a couple of extensions
```bash
sudo apt-get install -y php7.4-cli php7.4-json php7.4-common php7.4-mysql php7.4-zip php7.4-gd php7.4-mbstring php7.4-curl php7.4-xml php7.4-bcmath
```
Read the docs from index.html in .phpdoc/lib

## Tests
run normal unit tests:
```bash
composer test
```
add watcher
```bash
composer test:watch
```

## Creating a verion
* Update the version number in composer.json.
* Create a tag with the new version number in git and push to the repo
* Update the apps to use the new version 
