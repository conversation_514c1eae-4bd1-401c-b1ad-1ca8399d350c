<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $statements = [
            // side note: remove null values from infix column, save and update now prevents null values
            "UPDATE members SET infix='' WHERE infix IS NULL;",
            // set all members with function oud lid to have a membership_end_date of 2024-08-01
            "UPDATE members SET membership_end_date='2024-08-01' WHERE id IN (SELECT member_id FROM member_memberfunction WHERE memberfunction_id=78);",
            // remove all functions of ex-members
            "DELETE FROM member_memberfunction WHERE member_id IN (SELECT id FROM members WHERE members.membership_end_date IS NOT NULL);",
        ];

        DB::beginTransaction();
        try {
            foreach ($statements as $statement) {
                DB::statement($statement);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
