<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SetOwnRecordingToTinyint extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up() {
		// first fill all null values with 0
		DB::table("songitems")
		  ->whereNull('own_recording')
		  ->update(['own_recording' => '0']);

		Schema::table( 'songitems', function ( Blueprint $table ) {
			$table->smallInteger( 'own_recording' )->nullable( false )->change()->default( 0 );
		} );
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down() {
		Schema::table( 'songitems', function ( Blueprint $table ) {
			$table->string( "own_recording", 50 )->nullable()->change();
		} );
	}
}
