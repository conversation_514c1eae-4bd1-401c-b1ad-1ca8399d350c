<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: 887823be-b107-4ad0-9896-3ce244caf525
 * Migration local datetime: 2025-07-02 18:56:45.507127
 * Migration UTC datetime: 2025-07-02 16:56:45.507127
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2025070218564550 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('calendar_events', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('calendar_id')->nullable(true)->unsigned();
            $table->integer('event_id')->nullable(true)->unsigned();
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->foreign('calendar_id')->references('id')->on('calendars');
        });
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->foreign('event_id')->references('id')->on('events');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->dropForeign(['calendar_id']);
            $table->dropForeign(['event_id']);
        });
        Schema::dropIfExists('calendar_events');
    }
}
