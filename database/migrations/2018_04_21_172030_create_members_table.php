<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMembersTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('members', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('domain_id')->unsigned()->index('members_domain_id_foreign');
            $table->string("first_name", 45);
            $table->string("infix", 45)->nullable();
            $table->string("last_name", 45);
            $table->string("streetname", 45)->nullable();
            $table->string("house_number", 10)->nullable();
            $table->string("zipcode", 10)->nullable();
            $table->string("city", 45)->nullable();
            $table->string("birth_date", 5)->nullable();
            $table->integer("birth_year")->nullable();
            $table->string("telephone1", 15)->nullable();
            $table->string("telephone2", 15)->nullable();
            $table->string("email", 45)->nullable();
            $table->date("membership_start_date")->nullable();
            $table->date("membership_end_date")->nullable();
            $table->string("photo", 45)->nullable();
            $table->text("description")->nullable();
            $table->text("remarks")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('members');
    }
}
