<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLoginFieldsToMembersTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('members', function (Blueprint $table) {
            $table->string("loginname", 25)->nullable()->after("remarks");
            $table->string("passwd", 255)->nullable()->after("loginname");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('members', function (Blueprint $table) {
            $table->dropColumn("loginname");
            $table->dropColumn("passwd");
        });
    }
}
