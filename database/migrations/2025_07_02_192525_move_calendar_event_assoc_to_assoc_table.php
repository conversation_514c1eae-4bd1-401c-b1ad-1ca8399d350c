<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Migrate data in a transaction (data operations only)
        DB::transaction(function () {
            // Migrate existing calendar_id relationships from events table to calendar_events table
            $events = DB::table('events')
                ->whereNotNull('calendar_id')
                ->select('id', 'calendar_id', 'created_at', 'updated_at')
                ->get();

            $migratedCount = 0;
            $totalEvents = $events->count();

            foreach ($events as $event) {
                // Check if the relationship doesn't already exist in calendar_events
                $exists = DB::table('calendar_events')
                    ->where('event_id', $event->id)
                    ->where('calendar_id', $event->calendar_id)
                    ->exists();

                if (!$exists) {
                    DB::table('calendar_events')->insert([
                        'event_id' => $event->id,
                        'calendar_id' => $event->calendar_id,
                        'created_at' => $event->created_at ?? now(),
                        'updated_at' => $event->updated_at ?? now(),
                    ]);
                    $migratedCount++;
                }
            }

            // Verify that all events with calendar_id have been migrated
            $unmigrated = DB::table('events')
                ->whereNotNull('calendar_id')
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('calendar_events')
                        ->whereColumn('calendar_events.event_id', 'events.id')
                        ->whereColumn('calendar_events.calendar_id', 'events.calendar_id');
                })
                ->count();

            if ($unmigrated > 0) {
                throw new \Exception("Migration failed: {$unmigrated} events were not properly migrated to calendar_events table");
            }

            // Log the migration results
            \Log::info("Calendar-Event migration completed: {$migratedCount} relationships migrated out of {$totalEvents} total events");
        });

        // Step 2: Drop the column (schema operation outside transaction)
        // first drop the foreign key constraint (schema operation)
        Schema::table('events', function (Blueprint $table) {
            $table->dropForeign(['calendar_id']);
        });

        Schema::table('events', function (Blueprint $table) {
            $table->dropColumn('calendar_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Step 1: Add back the calendar_id column to events table (schema operation)
        Schema::table('events', function (Blueprint $table) {
            $table->integer('calendar_id')->unsigned()->nullable()->after('id');
        });

        // Step 2: Migrate data back in a transaction (data operations only)
        DB::transaction(function () {
            // Migrate data back from calendar_events to events.calendar_id
            $calendarEvents = DB::table('calendar_events')
                ->select('event_id', 'calendar_id')
                ->get();

            $migratedCount = 0;
            $totalRelationships = $calendarEvents->count();

            foreach ($calendarEvents as $calendarEvent) {
                // For rollback, we'll take the first calendar association for each event
                // since we're going back to a 1:n relationship
                $existingCalendarId = DB::table('events')
                    ->where('id', $calendarEvent->event_id)
                    ->value('calendar_id');

                if (is_null($existingCalendarId)) {
                    DB::table('events')
                        ->where('id', $calendarEvent->event_id)
                        ->update(['calendar_id' => $calendarEvent->calendar_id]);
                    $migratedCount++;
                }
            }

            // Verify that all events that should have calendar_id now have it
            $eventsWithoutCalendar = DB::table('events')
                ->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('calendar_events')
                        ->whereColumn('calendar_events.event_id', 'events.id');
                })
                ->whereNull('calendar_id')
                ->count();

            if ($eventsWithoutCalendar > 0) {
                throw new \Exception("Rollback failed: {$eventsWithoutCalendar} events that should have calendar_id are missing it");
            }

            // Log the rollback results
            \Log::info("Calendar-Event rollback completed: {$migratedCount} relationships restored out of {$totalRelationships} total relationships");

            // Remove the calendar_events records (they'll be recreated if migration runs again)
            DB::table('calendar_events')->truncate();
        });

        // Step 3: Re-add the foreign key constraint (schema operation)
        Schema::table('events', function (Blueprint $table) {
            $table->foreign('calendar_id')->references('id')->on('calendars')->onUpdate('RESTRICT')->onDelete('CASCADE');
        });
    }
};
