<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ConvertSongitemUrlToSongitemsfilesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up() {
		// get all URLs and create a record in songitemfiles for them
		$songitems = DB::table("songitems")->get();
		foreach ($songitems as $songitem) {
			DB::table('songitemfiles')->insert([
				"songitem_id"   => $songitem->id,
				"url"           => $songitem->url,
				"original_name" => $songitem->url,
				"history_date"  => null
			]);
		}
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down() {
		DB::table('songitemfiles')->truncate();
	}
}
