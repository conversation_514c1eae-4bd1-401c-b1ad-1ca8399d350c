<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRoleUseTTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up() {
		Schema::create( 'role_user', function ( Blueprint $table ) {
			$table->integer( 'user_id' )->unsigned()->nullable()->index( 'role_user_user_id_foreign' );
			$table->integer( 'role_id' )->unsigned()->nullable()->index( 'role_user_role_id_foreign' );
			$table->timestamps();
		} );
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down() {
		Schema::dropIfExists('role_user');
	}
}
