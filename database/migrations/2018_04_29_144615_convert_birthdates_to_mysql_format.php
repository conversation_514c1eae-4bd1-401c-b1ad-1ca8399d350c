<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\Members;

class ConvertBirthdatesToMysqlFormat extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        // members
        $members = DB::table('members')->get();

        foreach ($members as $member) {
            if ((!empty($member->birth_date)) && (strlen($member->birth_date) == 5)) {
                $m = substr($member->birth_date, 3,2);
                $d = substr($member->birth_date, 0,2);
                $newDate = $m . $d; // get rid of the hyphen to make sorting easier
                DB::table('members')
                    ->where('id', $member->id)
                    ->update(['birth_date' => $newDate]);
            }
        }
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        //
    }
}
