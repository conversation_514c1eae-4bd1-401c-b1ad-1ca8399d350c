<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFunctionsTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('memberfunctions', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('domain_id')->unsigned()->index('memberfunctions_domain_id_foreign');
            $table->string("description", 45);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('memberfunctions');
    }
}
