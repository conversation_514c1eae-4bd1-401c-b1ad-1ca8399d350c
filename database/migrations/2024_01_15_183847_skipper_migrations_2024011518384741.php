<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: 4e6e2731-be66-4e70-89c2-07f45a1f25b0
 * Migration local datetime: 2024-01-15 18:38:47.412269
 * Migration UTC datetime: 2024-01-15 17:38:47.412269
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2024011518384741 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('socials', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement()->unsigned();
            $table->integer('domain_id')->unsigned();
            $table->string('display_name', 25);
            $table->string('link', 150);
            $table->timestamp('created_at')->nullable(true);
            $table->timestamp('updated_at')->nullable(true);
        });
        Schema::table('socials', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasTable('socials')) {
            Schema::table('socials', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        Schema::dropIfExists('socials');
    }
}
