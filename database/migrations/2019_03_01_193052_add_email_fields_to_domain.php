<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/*
 * .env of CLASS should be available in the DB for Yfantis, because we have only 1 DB for all domains
CUSTOMER_DOMAIN="zangschoolbeesd.nl"
CUSTOMER_NAME="Zangschool Beesd"
CUSTOMER_ADDRESS_1="Industrieweg 24a"
CUSTOMER_ADDRESS_2="4153 BW Beesd"
CUSTOMER_ZIP="4153 BW"
CUSTOMER_CITY="Beesd"
CUSTOMER_TELEPHONE="085 877 19 61"
CUSTOMER_EMAIL=<EMAIL>
CUSTOMER_LOGO_URL=https://www.zangschoolbeesd.nl/wp-content/uploads/2015/11/cropped-logo_op_wit_300.png
CUSTOMER_ADULT_THRESHOLD=21
CUSTOMER_RATES_CONDITIONS=https://www.zangschoolbeesd.nl/tarieven/
CUSTOMER_DEFAULT_PASSWORD=beesdbeesd
CUSTOMER_CONTACT_PERSON="Marieke Zevenbergen"
CUSTOMER_WEBSITE="https://www.zangschoolbeesd.nl"
CUSTOMER_SCHEDULE_THRESHOLD=59
 */


class AddEmailFieldsToDomain extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if(Schema::hasColumn('domains', 'memebershipfee_endtext')) {
            Schema::table('domains', function (Blueprint $table) {
                $table->dropColumn("memebershipfee_endtext");
            });
        }
        if(Schema::hasColumn('domains', 'memebershipfee_starttext')) {
            Schema::table('domains', function (Blueprint $table) {
                $table->dropColumn("memebershipfee_starttext");
            });
        }

        Schema::table('domains', function (Blueprint $table) {

            $table->string("email_name", 100)->nullable()->after("default_passwd");
            $table->string("email_addressline1", 100)->nullable()->after("email_name");
            $table->string("email_addressline2", 100)->nullable()->after("email_addressline1");
            $table->string("email_zip", 100)->nullable()->after("email_addressline2");
            $table->string("email_city", 100)->nullable()->after("email_zip");
            $table->string("email_telephone", 100)->nullable()->after("email_city");
            $table->string("email_email_from", 100)->nullable()->after("email_telephone");
            $table->string("email_logo", 100)->nullable()->after("email_email_from");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropColumn("email_name");
            $table->dropColumn("email_addressline1");
            $table->dropColumn("email_addressline2");
            $table->dropColumn("email_zip");
            $table->dropColumn("email_city");
            $table->dropColumn("email_telephone");
            $table->dropColumn("email_email_from");
            $table->dropColumn("email_logo");
        });
    }
}
