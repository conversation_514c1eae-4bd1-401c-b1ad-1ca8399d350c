<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCalendarsTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('calendars', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('domain_id')->unsigned()->index('events_domain_id_foreign');
            $table->string('name', 45);
            $table->text('description')->nullable();
            $table->tinyInteger('is_public')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('calendars');
    }
}
