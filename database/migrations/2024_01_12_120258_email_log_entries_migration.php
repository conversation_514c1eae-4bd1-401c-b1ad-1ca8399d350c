<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('emaillogentries', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('domain_id')->index();
            $table->string('to');
            $table->string('cc');
            $table->string('bcc');
            $table->string('from');
            $table->string('subject');
            $table->text('body');
            $table->text('attachments');
            $table->string('unique_token');
            $table->string('status')->default('queued');
            $table->text('log');
            $table->timestamps();
        });
        Schema::table('emaillogentries', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('emaillogentries')) {
            Schema::table('emaillogentries', function (Blueprint $table) {
                $table->dropForeign(['domain_id']);
            });
        }
        Schema::dropIfExists('emaillogentries');
    }
};
