<?php
/* 
 * Migrations generated by: Skipper (http://www.skipper18.com)
 * Migration id: f30e9c34-fcf8-4bbd-8b45-f07957c87d38
 * Migration local datetime: 2024-01-14 19:43:36.727098
 * Migration UTC datetime: 2024-01-14 18:43:36.727098
 */ 

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SkipperMigrations2024011419433672 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('basisbedrag');
        Schema::dropIfExists('calculated_field');
        Schema::dropIfExists('content');
        Schema::dropIfExists('content_historie');
        Schema::dropIfExists('contributie');
        Schema::dropIfExists('contributieperiode');
        Schema::dropIfExists('document');
        Schema::dropIfExists('domein');
        Schema::dropIfExists('domein_rekenregel');
        Schema::dropIfExists('domeintype');
        Schema::dropIfExists('emailqueue');
        Schema::dropIfExists('foto');
        Schema::dropIfExists('fotoverzameling');
        Schema::dropIfExists('functie');
        Schema::dropIfExists('herhaling_uitzondering');
        Schema::dropIfExists('herhalingtype');
        Schema::dropIfExists('kalender');
        Schema::dropIfExists('kalender_item');
        Schema::dropIfExists('kleurcode');
        Schema::dropIfExists('laatstenieuws');
        Schema::dropIfExists('lid');
        Schema::dropIfExists('log');
        Schema::dropIfExists('login');
        Schema::dropIfExists('muziekwerk');
        Schema::dropIfExists('muziekwerklink');
        Schema::dropIfExists('pagina');
        Schema::dropIfExists('rekenregel');
        Schema::dropIfExists('responsetekst');
        Schema::dropIfExists('sponsor');
        Schema::dropIfExists('temperatuur');
        Schema::dropIfExists('temperatuur_servers');
        Schema::dropIfExists('twitter');
        Schema::dropIfExists('verstuurde_formulieren');
        Schema::dropIfExists('visits');
        Schema::dropIfExists('visits_bots');
        Schema::dropIfExists('webform');
        Schema::dropIfExists('webformveld');
        Schema::create('calendar_memberfunction', function (Blueprint $table) {
            $table->integer('calendar_id')->unsigned();
            $table->integer('memberfunction_id')->unsigned();
            $table->primary(['calendar_id','memberfunction_id']);
        });
        Schema::table('calendar_memberfunction', function (Blueprint $table) {
            $table->foreign('calendar_id')->references('id')->on('calendars');
            $table->foreign('memberfunction_id')->references('id')->on('memberfunctions');
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('calendar_memberfunction', function (Blueprint $table) {
            $table->dropForeign(['calendar_id']);
            $table->dropForeign(['memberfunction_id']);
        });
        Schema::dropIfExists('calendar_memberfunction');
        Schema::create('webformveld', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_webform');
            $table->string('naam', 150);
            $table->tinyInteger('veldtype');
            $table->string('veldspec', 255)->nullable(true);
            $table->tinyInteger('verplicht')->default(0);
            $table->index(['id_webform'], 'idx_webformveld');
        });
        Schema::create('webform', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('titel', 50);
            $table->integer('id_domein');
            $table->string('emailtarget', 255);
            $table->string('tekstopverzendenknop', 25)->default('verzenden');
            $table->tinyInteger('usecaptcha')->default(0);
            $table->text('veld_volgorde')->nullable(true);
            $table->tinyInteger('breedte_labelkolom')->default(20);
            $table->tinyInteger('breedte_tabel')->nullable(true);
            $table->index(['id_domein'], 'idx_webform');
        });
        Schema::create('visits_bots', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->integer('id_page');
            $table->string('ipaddress', 40);
            $table->timestamp('dt')->useCurrent();
            $table->string('useragent', 255)->nullable(true);
            $table->string('opmerking', 100)->nullable(true);
        });
        Schema::create('visits', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->integer('id_page');
            $table->string('ipaddress', 40);
            $table->timestamp('dt')->useCurrent();
            $table->string('useragent', 255)->nullable(true);
            $table->string('opmerking', 100)->nullable(true);
        });
        Schema::create('verstuurde_formulieren', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_webform');
            $table->timestamp('datetime_send')->useCurrent();
            $table->text('content')->nullable(true);
            $table->index(['id_webform'], 'idx_verstuurde_formulieren');
        });
        Schema::create('twitter', function (Blueprint $table) {
            $table->integer('id_domein');
            $table->string('tekst', 255);
            $table->timestamp('timestamp')->useCurrent();
            $table->string('publication', 50);
            $table->string('whosays', 50)->nullable(true);
            $table->index(['id_domein'], 'idx_twitter');
        });
        Schema::create('temperatuur_servers', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('name', 50);
            $table->string('ip', 50);
            $table->string('probe', 20);
        });
        Schema::create('temperatuur', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->date('date');
            $table->time('time');
            $table->dateTime('dt');
            $table->string('probeid', 20);
            $table->float('temp');
        });
        Schema::create('sponsor', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 50)->nullable(true);
            $table->string('adres', 50)->nullable(true);
            $table->string('postcode', 50)->nullable(true);
            $table->string('woonplaats', 50)->nullable(true);
            $table->string('logo', 50)->nullable(true);
            $table->string('onderschrift', 50)->nullable(true);
            $table->string('website', 150)->nullable(true);
            $table->integer('tijdtonen')->default(0);
            $table->string('cp_naam', 50)->nullable(true);
            $table->string('cp_telefoon', 50)->nullable(true);
        });
        Schema::create('responsetekst', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_webform');
            $table->text('starttekst')->nullable(true);
            $table->string('sommatielabel', 50)->nullable(true);
            $table->text('slottekst');
            $table->integer('id_emailveld')->nullable(true)->default(0);
        });
        Schema::create('rekenregel', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('operator', 45);
            $table->string('label', 75);
            $table->tinyInteger('heeft_operand');
        });
        Schema::create('pagina', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 50);
            $table->text('content_volgorde');
            $table->tinyInteger('doorzoekbaar')->nullable(true)->default(1);
            $table->tinyInteger('is_default')->default(0);
            $table->tinyInteger('protected')->default(0);
            $table->tinyInteger('maxaantalberichten')->default(1);
            $table->integer('maxberichtlength')->default(0);
            $table->integer('volgnummer')->default(0);
            $table->string('controller_path', 75);
            $table->string('controller', 25);
            $table->tinyInteger('toon_in_cms')->default(1);
            $table->tinyInteger('add_to_menu')->default(1);
            $table->tinyInteger('isblogpagina')->default(0);
            $table->tinyInteger('islaatstenieuwspagina')->default(0);
            $table->tinyInteger('iskalenderpagina')->default(0);
            $table->index(['id_domein'], 'idx_pagina');
        });
        Schema::create('muziekwerklink', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('url', 255);
            $table->string('linktype', 25);
            $table->tinyInteger('eigen_uitvoering')->default(0);
            $table->integer('id_muziekwerk');
            $table->string('referentie', 15);
            $table->string('naam', 50)->nullable(true);
        });
        Schema::create('muziekwerk', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('titel', 75)->nullable(true);
            $table->string('jaar', 15)->nullable(true);
            $table->string('tags', 50)->nullable(true);
            $table->string('duur_uitvoering', 15)->nullable(true);
            $table->date('datum_in_repertoire')->nullable(true);
            $table->string('bestaand_nummer', 25)->nullable(true);
            $table->integer('id_domein');
            $table->string('componist1', 50)->nullable(true);
            $table->string('componist2', 50)->nullable(true);
            $table->text('opmerkingen')->nullable(true);
        });
        Schema::create('login', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 75);
            $table->string('telefoon', 15)->nullable(true);
            $table->string('email', 75)->nullable(true);
            $table->string('login_naam', 25);
            $table->string('password', 35);
            $table->integer('rechten')->default(0);
            $table->index(['id_domein'], 'idx_login');
        });
        Schema::create('log', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->timestamp('timestamp')->useCurrent();
            $table->string('entrytype', 15);
            $table->text('waarde');
            $table->integer('id_domein')->default(0);
            $table->string('ip-address', 50)->default('0');
        });
        Schema::create('lid', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 50);
            $table->string('adres', 50)->nullable(true);
            $table->string('straatnaam', 100)->nullable(true);
            $table->string('huisnummer', 10)->nullable(true);
            $table->string('postcode', 7)->nullable(true);
            $table->string('woonplaats', 50)->nullable(true);
            $table->string('verjaardag', 5)->nullable(true);
            $table->integer('geboortejaar')->nullable(true);
            $table->string('telefoon1', 15)->nullable(true);
            $table->string('telefoon2', 15)->nullable(true);
            $table->string('email', 50)->nullable(true);
            $table->integer('id_functie')->nullable(true);
            $table->date('lid_sinds')->nullable(true);
            $table->date('einde_lidmaatschap')->nullable(true);
            $table->string('pasfoto', 50)->nullable(true);
            $table->text('beschrijving')->nullable(true);
            $table->text('opmerking')->nullable(true);
            $table->string('login_naam', 25)->nullable(true);
            $table->string('password', 35)->nullable(true);
            $table->string('login_hash', 50)->nullable(true);
            $table->string('login_hash_ip', 25)->nullable(true);
            $table->dateTime('login_hash_expire')->nullable(true);
            $table->integer('id_functie2')->nullable(true);
            $table->integer('id_functie3')->nullable(true);
            $table->string('anaam', 50)->nullable(true);
            $table->string('tvnaam', 15)->nullable(true);
            $table->string('vnaam', 50)->nullable(true);
            $table->tinyInteger('extern_contact')->default(0);
            $table->tinyInteger('suspend')->default(0);
            $table->tinyInteger('is_erelid')->default(0);
        });
        Schema::create('laatstenieuws', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->date('datum');
            $table->string('titel', 75);
            $table->text('bericht');
        });
        Schema::create('kleurcode', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('kleur_html', 7);
            $table->string('label', 25);
            $table->integer('id_domein');
            $table->tinyInteger('volgnummer')->default(0);
            $table->tinyInteger('inevenementenkalender')->default(0);
        });
        Schema::create('kalender_item', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->dateTime('datumtijd');
            $table->integer('id_kalender');
            $table->text('omschrijving')->nullable(true);
            $table->string('titel', 50);
            $table->time('eindtijd')->nullable(true);
            $table->integer('id_kleurcode');
            $table->integer('id_herhalingtype');
            $table->date('herhaling_einddatum');
        });
        Schema::create('kalender', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 50);
        });
        Schema::create('herhalingtype', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('naam', 15);
        });
        Schema::create('herhaling_uitzondering', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->date('opdatum');
            $table->integer('id_kalenderitem');
            $table->boolean('incidenteel_niet');
            $table->text('extra_opmerking');
        });
        Schema::create('functie', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('omschrijving', 50);
            $table->integer('id_domein');
            $table->tinyInteger('volgnummer')->default(0);
        });
        Schema::create('fotoverzameling', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('titel', 75);
            $table->text('foto_volgorde')->nullable(true);
            $table->date('datum')->nullable(true);
        });
        Schema::create('foto', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_fotoverzameling');
            $table->string('source', 35);
            $table->string('caption', 150)->nullable(true);
        });
        Schema::create('emailqueue', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('sender', 75);
            $table->string('recepient', 75);
            $table->string('carboncopy', 75)->nullable(true);
            $table->string('blindcarboncopy', 75)->nullable(true);
            $table->string('subject', 45);
            $table->text('body');
            $table->string('attachment', 45)->nullable(true);
            $table->timestamp('datum_aangemaakt')->useCurrent();
            $table->timestamp('datum_verzonden')->nullable(true);
        });
        Schema::create('domeintype', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->string('omschrijving', 50)->nullable(true);
        });
        Schema::create('domein_rekenregel', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->integer('id_rekenregel');
            $table->integer('id_basisbedrag');
            $table->string('operand', 45)->nullable(true);
            $table->integer('factor');
            $table->timestamp('md')->useCurrent();
            $table->string('mu', 45);
        });
        Schema::create('domein', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domeintype')->default(0);
            $table->string('naam', 50);
            $table->string('url', 100)->nullable(true);
            $table->text('lookup_url');
            $table->string('lookup_map', 20);
            $table->string('cp_naam', 25)->nullable(true);
            $table->string('cp_email', 50)->nullable(true);
            $table->string('cp_telefoon', 75)->nullable(true);
            $table->string('std_email', 50);
            $table->string('std_naam', 50)->nullable(true);
            $table->tinyInteger('mod_pagina')->default(0);
            $table->tinyInteger('mod_webform')->default(0);
            $table->tinyInteger('mod_fotoboek')->default(0);
            $table->tinyInteger('mod_kalender')->default(0);
            $table->tinyInteger('mod_repertoire')->default(0);
            $table->tinyInteger('mod_leden')->default(0);
            $table->tinyInteger('mod_sponsoren')->default(0);
            $table->tinyInteger('mod_documenten')->default(0);
            $table->tinyInteger('mod_contributie')->default(0);
            $table->tinyInteger('mod_setlist');
            $table->tinyInteger('online')->default(0);
            $table->text('offline_tekst')->nullable(true);
            $table->text('notes')->nullable(true);
            $table->tinyInteger('useshorturl')->nullable(true)->default(0);
            $table->char('basistaal', 3)->default('nl');
            $table->string('related_domains', 50)->nullable(true);
            $table->tinyInteger('heeft_profielpagina')->default(0);
            $table->tinyInteger('GA_ID');
            $table->string('std_homepath', 50);
            $table->tinyInteger('yamlversion');
            $table->text('description')->nullable(true);
            $table->string('keywords', 255)->nullable(true);
            $table->string('betaalfreq', 10)->default('jaar');
            $table->text('contributie_introtekst')->nullable(true);
            $table->text('contributie_slottekst')->nullable(true);
        });
        Schema::create('document', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->date('datum');
            $table->string('titel', 50);
            $table->string('filename', 50);
            $table->string('tags', 100)->nullable(true);
            $table->string('doctype', 25)->nullable(true);
            $table->integer('id_domein');
            $table->text('omschrijving')->nullable(true);
        });
        Schema::create('contributieperiode', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein')->nullable(true);
            $table->string('naam', 35);
            $table->timestamp('datum_berekend')->useCurrent();
            $table->timestamp('datum_goedgekeurd')->default('0000-00-00 00:00:00');
            $table->string('goedgekeurd_door', 45);
            $table->date('datum_verstuurd')->default('0000-00-00');
        });
        Schema::create('contributie', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_lid')->default(0);
            $table->integer('id_contributieperiode')->default(0);
            $table->date('datum_betaald')->nullable(true);
            $table->float('bedrag');
            $table->string('toegepaste_rekenregel', 255);
        });
        Schema::create('content_historie', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_content');
            $table->timestamp('timestamp')->useCurrent();
            $table->text('content');
        });
        Schema::create('content', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_pagina');
            $table->text('content')->nullable(true);
            $table->date('tonen_vanaf_datum');
            $table->date('tonen_tot_datum')->nullable(true);
            $table->string('titel', 50);
            $table->text('description')->nullable(true);
            $table->string('keywords', 255)->nullable(true);
            $table->index(['content','titel'], 'fulltextindex');
            $table->index(['id_pagina'], 'idx_content');
        });
        Schema::create('calculated_field', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_responsetekst');
            $table->integer('id_webformveld');
            $table->string('label', 50);
            $table->float('constante');
            $table->char('bewerking', 1);
        });
        Schema::create('basisbedrag', function (Blueprint $table) {
            $table->integer('id')->autoIncrement();
            $table->integer('id_domein');
            $table->string('naam', 45);
            $table->float('bedrag');
            $table->timestamp('timestamp')->useCurrent();
        });
    }
}
