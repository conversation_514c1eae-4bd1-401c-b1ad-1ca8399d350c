<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddPwResetToMembersTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('members', function (Blueprint $table) {
            // create with echo bin2hex(random_bytes(64));
            $table->string("password_reset_code", 150)->nullable()->after('passwd');
            $table->dateTime("pw_reset_valid_until")->nullable()->after('password_reset_code');
            $table->string("email", 45)->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('members', function (Blueprint $table) {
            $table->string("email", 45)->nullable()->change();
            $table->dropColumn("pw_reset_valid_until");
            $table->dropColumn("password_reset_code");
        });
    }
}
