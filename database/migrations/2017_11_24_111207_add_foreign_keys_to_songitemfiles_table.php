<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddForeignKeysToSongitemfilesTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up() {
		Schema::table( 'songitemfiles', function ( Blueprint $table ) {
			$table->foreign( 'songitem_id' )->references( 'id' )->on( 'songitems' )->onUpdate( 'RESTRICT' )->onDelete( 'CASCADE' );
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down() {
		Schema::table( 'songitems', function ( Blueprint $table ) {
			$table->dropForeign( 'songitemfiles_songitem_id_foreign' );
		});
	}
}
