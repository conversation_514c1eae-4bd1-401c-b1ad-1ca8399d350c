<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddForeignKeysToDomainsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
	    Schema::table( 'domains', function ( Blueprint $table ) {
		    $table->foreign( 'domaintype_id' )->references( 'id' )->on( 'domaintypes' )->onUpdate( 'RESTRICT' )->onDelete( 'CASCADE' );
	    });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
	    Schema::table( 'domains', function ( Blueprint $table ) {
		    $table->dropForeign( 'domains_domaintype_id_foreign' );
	    });
    }
}
