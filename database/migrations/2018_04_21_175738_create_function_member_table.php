<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFunctionMemberTable extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::create('member_memberfunction', function (Blueprint $table) {
            $table->integer('memberfunction_id')->unsigned()->index('member_memberfunction_function_id_foreign');
            $table->integer('member_id')->unsigned()->index('member_memberfunction_member_id_foreign');
            $table->primary(['memberfunction_id', 'member_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::dropIfExists('member_memberfunction');
    }
}
