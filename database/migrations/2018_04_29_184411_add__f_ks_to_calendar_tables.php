<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFKsToCalendarTables extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('calendars', function (Blueprint $table) {
            $table->foreign('domain_id')->references('id')->on('domains')->onUpdate('RESTRICT')->onDelete('CASCADE');
        });
        Schema::table('events', function (Blueprint $table) {
            $table->foreign('calendar_id')->references('id')->on('calendars')->onUpdate('RESTRICT')->onDelete('CASCADE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table( 'events', function ( Blueprint $table ) {
            $table->dropForeign( 'events_calendar_id_foreign' );
        });
        Schema::table( 'calendars', function ( Blueprint $table ) {
            $table->dropForeign( 'calendars_domain_id_foreign' );
        });
    }
}
