<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use App\User;
use App\Domain;

class AddAdminModuleToDomain extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        Schema::table('domains', function (Blueprint $table) {
            $table->tinyInteger("mod_admin",false,true)
                ->after('lookup_directory')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropColumn("mod_admin");
        });
    }
}
