<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSongsTable extends Migration {
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up() {
		Schema::create( 'songs', function ( Blueprint $table ) {
			$table->increments( 'id' );
			$table->integer( 'domain_id' )->unsigned()->nullable();
			$table->string( "title", 75 );
			$table->string( "year", 15 )->nullable();
			$table->string( "tags", 50 )->nullable();
			$table->string( "time_in_setlist", 50 )->nullable();
			$table->date( 'date_in_repertoire' )->nullable();
			$table->string( "archive_number", 25 )->nullable();
			$table->string( "composer1", 50 )->nullable();
			$table->string( "composer2", 50 )->nullable();
			$table->text( "remarks" )->nullable();
			$table->timestamps();
		} );
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down() {
		Schema::dropIfExists( 'songs' );
	}
}
