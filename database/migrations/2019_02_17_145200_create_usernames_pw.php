<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsernamesPw extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        // can't do this with the model, because we have no domain_id when migrating
        $members = DB::table('members')->get();
        foreach ($members as $member) {
            if($member->loginname == '') {
                $member->loginname = $member->first_name . substr($member->last_name, 0, 1);
                $member->passwd = password_hash($member->domain->default_passwd,PASSWORD_BCRYPT);
                $member->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {
    }
}
