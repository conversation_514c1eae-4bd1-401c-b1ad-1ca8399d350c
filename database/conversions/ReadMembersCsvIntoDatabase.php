<?php
global $queries, $memberCounterStart;
$memberCounterStart = 200;

// data for memberfunctions table: "<PERSON>ang<PERSON><PERSON>", "Opleidingsklas Junior", "Opleidingsklas Senior", "Jeugdkoor", "Bestuur", "Dirigent"
$queries = [
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (70, 1001, 'Zangklas');",
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (71, 1001, 'Opleidingsklas Junior');",
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (72, 1001, 'Opleidingsklas Senior');",
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (73, 1001, 'Jeugdkoor');",
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (74, 1001, 'Bestuur');",
    "INSERT INTO `memberfunctions` (id, domain_id, description) VALUES (75, 1001, 'Dirigent');",
];


// -------------------------------------------------------
// singers, read csv from file
// -------------------------------------------------------
csvToSql(str_getcsv(file_get_contents('/home/<USER>/Downloads/Zangklas.csv'), "\n"), 70);
csvToSql(str_getcsv(file_get_contents('/home/<USER>/Downloads/Opleidingsklas_junior.csv'), "\n"), 71);
csvToSql(str_getcsv(file_get_contents('/home/<USER>/Downloads/Opleidingsklas_senior.csv'), "\n"), 72);
csvToSql(str_getcsv(file_get_contents('/home/<USER>/Downloads/Jeugdkoor.csv'), "\n"), 73);

// -------------------------------------------------------
// Bestuur
// -------------------------------------------------------
$queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`)
            VALUES ($memberCounterStart, 1001, 'Sjaak', '', 'Sperber', '', '<EMAIL>,<EMAIL>', '0655346397');";
$queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, 74);";
$memberCounterStart++;
$queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`)
            VALUES ($memberCounterStart, 1001, 'Sjaak', '', 'Beirnaert', '', '<EMAIL>,<EMAIL>', '0622547907');";
$queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, 74);";
$memberCounterStart++;
$queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`)
            VALUES ($memberCounterStart, 1001, 'Karin', '', 'Albers', '', '<EMAIL>,<EMAIL>', '0638923525');";
$queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, 74);";
$memberCounterStart++;

// -------------------------------------------------------
// Dirigenten
// -------------------------------------------------------
$queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`)
            VALUES ($memberCounterStart, 1001, 'Peggy', '', 'Hegeman', '', '<EMAIL>', '0648492097');";
$queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, 75);";
$memberCounterStart++;
$queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`)
            VALUES ($memberCounterStart, 1001, 'Lieke', '', 'Beirnaert', '', '<EMAIL>', '0653749351');";
$queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, 75);";

// -------------------------------------------------------
// OUTPUT
// -------------------------------------------------------
foreach ($queries as $query) {
    echo $query . "\n";
}

function csvToSql($csv, $memberfunctionId)
{
    global $queries, $memberCounterStart;
    foreach ($csv as $value) {
        // read csv from string
        $value = str_getcsv($value, ",");
        $voornaam = $value[0];
        $achternaam = $value[1];
        // split achternaam into last_name and infix. infix can be '', 'van' or 'van de'
        $achternaam = explode(' ', $achternaam);
        $last_name = array_pop($achternaam);
        $infix = implode(' ', $achternaam);
        $infix = trim($infix);
        $naam_ouders = $value[2];
        $email1 = $value[3];
        $email2 = $value[4];
        $email = implode(',', [$email1, $email2]);
        $telnr1 = $value[5];
        $telnr2 = $value[6];
        $geboortedatum = $value[7];
        // geboortedatum is of the form m/d/y - split into birthdate of form dd-mm and year of form yyyy
        $geboortedatum = explode('/', $geboortedatum);
        $month = sprintf('%02s', $geboortedatum[0]);
        $day = sprintf('%02s', $geboortedatum[1]);
        $birth_date = $day . '-' . $month;
        $birth_year = $geboortedatum[2];
        $adres = $value[8] ?: '';
        $lid_sinds = $value[9] ?: 'null';
        // convert $lid_sinds into form yyyy-mm-dd
        $lid_sinds = explode('/', $lid_sinds);
        if (count($lid_sinds) == 3 && is_numeric($lid_sinds[0]) && is_numeric($lid_sinds[1]) && is_numeric($lid_sinds[2])) {
            $lid_sinds = $lid_sinds[2] . '-' . sprintf('%02s', $lid_sinds[0]) . '-' . sprintf('%02s', $lid_sinds[1]);
        } else {
            $lid_sinds = '';
        }

        // convert $adres into three parts: straat, postcode, plaats
        if(!empty($adres)) {
            $adres = explode(',', $adres);
            $straat = $adres[0];
            $postcode = substr(trim($adres[1]), 0, 6);
            $plaats = substr(trim($adres[1]), 7);
        } else  {
            $straat = null;
            $postcode = null;
            $plaats = null;
        }
        $description = "naam ouders: " . $naam_ouders;
        // there may still be a column 10 that should be added to the description
        if (count($value) > 10) {
            $description .= "<br>extra e-mail: " . $value[10];
        }
        $queries[] = "INSERT INTO `members` (`id`, `domain_id`,`first_name`, `infix`,`last_name`, `remarks`, `email`, `telephone1`, `telephone2`, `birth_date`, `birth_year`, `streetname`, `zipcode`, `city`, `membership_start_date`)
            VALUES ($memberCounterStart, 1001, '$voornaam', '$infix', '$last_name', '$description', '$email', '$telnr1', '$telnr2', '$birth_date', '$birth_year', '$straat', '$postcode', '$plaats', " . (empty($lid_sinds) ? "null" : "'$lid_sinds'") . ");";
        // entry in member_memberfunction table
        $queries[] = "INSERT INTO `member_memberfunction` (`member_id`, `memberfunction_id`) VALUES ($memberCounterStart, $memberfunctionId);";
        $memberCounterStart++;
    }
    return $memberCounterStart;
}

