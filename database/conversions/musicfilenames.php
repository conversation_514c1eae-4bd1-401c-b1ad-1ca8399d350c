<?php
$linkTypes = ['4' => 'bladmuziek', '6' => 'opname'];
$updates = [];

// get connection
$servername = "localhost:8889";
$username = "scolar";
$password = "tochawru";
$dbname = "cms2017";


try {
    $conn = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $username, $password);

    // set the PDO error mode to exception and charset to utf8
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::MYSQL_ATTR_INIT_COMMAND, "SET NAMES utf8mb4");

    echo "Connected successfully\n";
} catch(PDOException $e)  {
    die("Connection failed: " . $e->getMessage() . "\n");
}


// get all files from the directory
// ie: read database for those files
// type 1 and 2 are url's
$q = "SELECT sif.id, s.domain_id, sif.url, si.linktype, sif.id, si.name, s.title from songitemfiles sif " .
    "left JOIN songitems si ON sif.songitem_id = si.id " .
    "left JOIN songs s ON si.song_id = s.id " .
    "WHERE si.linktype NOT IN ('2', '1') " .
    "AND s.domain_id = 13";

$stmt = $conn->prepare($q);
$stmt->execute();
$stmt->setFetchMode(PDO::FETCH_ASSOC);
$result = $stmt->fetchAll();

foreach ($result as $row) {
    $path = pathinfo($row["url"]);
    $extension = $path["extension"];

    $name = handlenamepart($row["name"]);
    $title = handlenamepart($row["title"]);
    // simple check if title is repeated in the name
    $name = trim(str_replace($title, "", $name), '_');
    // als name nu "" is moet er wat komen te staan. afhankelijk van het type bestand
    if($name == "") {
        switch($row["linktype"]) {
            case '4':
            case '6':
                $name = $linkTypes[$row["linktype"]];
                break;
            default:
                $name = "onbekend type";
        }
    }
    if(is_numeric($name)) $name = $linkTypes[$row["linktype"]] . "_" . $name;
    $fileName = str_replace("_-_", "_", $title . "|" . $name)  . "." . $extension;
    // create update for the filenames
    $updates[$row['id']] = "UPDATE songitemfiles SET original_name = '$fileName' WHERE id = " . $row['id'];
}

mb_internal_encoding("UTF-8");
$conn->exec("SET NAMES utf8mb4");
setlocale(LC_ALL, "en_US.utf8");

$h = fopen("/tmp/zz_conv.sql", "w");

foreach ($updates as $update) {
    fwrite($h, $update . ";\n");
}

fclose($h);

/**
 * Remove invalid chars from string to be used as filename
 * remove spaces, capitals, comma's, period and quotes
 * @param string $nameIn
 * @return mixed
 */
function handlenamepart($nameIn = "") {
    $name = str_replace(" ", "_", strtolower($nameIn));
    $name = str_replace(",", "_", $name);
    $name = str_replace("'", "", $name);
    $name = str_replace('"', "", $name);
    $name = str_replace('.', "", $name);
    $name = str_replace('(', "_", $name);
    $name = str_replace(')', "_", $name);
    $name = str_replace('__', "_", $name);
    $name = str_replace('___', "_", $name);
    $name = trim($name, '_');
    return $name;
}