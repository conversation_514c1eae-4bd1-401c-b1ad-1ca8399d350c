<?xml version="1.0"?>
<skipper version="3.3.7.1828" mvc="Laravel" orm="Laravel" name="Yfantis2017" uuid="b3da5a30-0faf-4b9f-b73c-9b403e049012">
  <module name="\cms2017" local-name="cms2017" namespace="\App\Models" local-namespace="App\Models" export-format="Laravel" export-path="." uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087">
    <entity name="\App\Models\Domain" local-name="Domain" namespace="\App\Models" uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb">
      <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="741c8d44-f7b8-4901-8681-e3ff68c69e64"/>
      <field name="domaintype_id" type="integer" required="true" unsigned="true" uuid="5fa43199-09bd-48b4-bc7f-e5f13d85cec0"/>
      <field name="name" type="string" size="50" required="true" uuid="471007d8-710e-4750-aa56-93bac3fd01e8"/>
      <field name="url" type="string" size="100" uuid="7720f564-9cfa-4e5f-9fe4-0681edfb14b2"/>
      <field name="lookup_url" type="text" required="true" uuid="b4f9e998-8e9b-4fc1-97ae-682d898bace3"/>
      <field name="lookup_directory" type="string" size="20" required="true" uuid="6a93c0d5-e4c5-45b6-81cb-b913c3d81c65"/>
      <field name="mod_admin" type="tinyInteger" default="0" required="true" unsigned="true" uuid="c766eb66-09b5-4dd3-ba86-157891b7997f"/>
      <field name="mod_repertoire" type="tinyInteger" required="true" unsigned="true" uuid="a29c5ff4-4f93-4fb0-bde5-cd2675379495"/>
      <field name="mod_members" type="tinyInteger" required="true" unsigned="true" uuid="f3bd606b-0e93-4a87-94f6-1b3028ad3d23"/>
      <field name="mod_sponsors" type="tinyInteger" required="true" unsigned="true" uuid="fb91d9e2-3dd3-4c85-9f9a-fd2b6c7ee6e8"/>
      <field name="mod_setlist" type="tinyInteger" required="true" unsigned="true" uuid="ddbfbd2f-501d-48b7-9ae5-5723939fab82"/>
      <field name="mod_membershipfee" type="tinyInteger" required="true" unsigned="true" uuid="fa68ef39-6be9-4f03-af9b-22a429515400"/>
      <field name="mod_calendar" type="tinyInteger" default="0" required="true" uuid="7e8e58a3-f02f-4602-a487-078a4e92d709"/>
      <field name="default_language" type="string" size="2" default="nl" required="true" uuid="b8a5dc83-**************-5b3c1b43eff3"/>
      <field name="default_passwd" type="string" size="25" required="true" uuid="7b6f2592-4632-4f9c-8ca7-5458e20d1aa1"/>
      <field name="email_name" type="string" size="100" uuid="46d65fec-4359-49af-ac49-4cf6ebbb9b90"/>
      <field name="email_addressline1" type="string" size="100" uuid="f0cc8c66-5164-4f39-b678-8c5958751869"/>
      <field name="email_addressline2" type="string" size="100" uuid="da4db680-802f-407d-9468-e9a9303785ed"/>
      <field name="email_zip" type="string" size="100" uuid="58675c87-9b4b-4efb-9ca1-d98f42ace903"/>
      <field name="email_city" type="string" size="100" uuid="d31303e4-85fc-4e82-9862-82a556c6e1f0"/>
      <field name="email_telephone" type="string" size="100" uuid="fe328f5a-7e79-4d30-ad6f-7d97933ecf6c"/>
      <field name="email_email_from" type="string" size="100" uuid="73fb5f78-ac6f-4013-9a64-492c443a9545"/>
      <field name="email_logo" type="string" size="120" uuid="ce378e1c-dc57-4795-980f-ab7f90deacbf"/>
      <field name="created_at" type="timestamp" uuid="6c22b62f-b310-4aa9-9b0c-111d97c6536a"/>
      <field name="updated_at" type="timestamp" uuid="766ae6d2-372d-479c-bbd6-771b1713b136"/>
      <orm-attributes>
        <attribute name="table">domains</attribute>
      </orm-attributes>
    </entity>
    <association from="\App\Models\Domain" to="\App\Models\Domaintype" owner-alias="domains" inverse-alias="domaintypes" many-owner="true" many-inverse="false" parent-required="true" uuid="7d5a7c17-1601-4417-9cde-faf6c11ae20f">
      <association-field from="domaintype_id" to="id" uuid="0ab8607e-176a-4ed5-be41-8420a92aa5ba"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <entity name="\App\Models\Domaintype" local-name="Domaintype" namespace="\App\Models" uuid="ae9b7531-7184-4c96-928a-8a354efb42ad">
      <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="a8eb6b6b-e0dd-496b-911a-1be1f75b74b4"/>
      <field name="description" type="string" size="255" required="true" uuid="7541f808-520a-43b4-b371-215db0276318"/>
      <field name="created_at" type="timestamp" uuid="a9d8be40-2168-495b-8434-4b2a68c2803f"/>
      <field name="updated_at" type="timestamp" uuid="a6542ef2-934c-4463-a955-938c2594d859"/>
      <orm-attributes>
        <attribute name="table">domaintypes</attribute>
      </orm-attributes>
    </entity>
    <entity name="\App\Models\socials" local-name="socials" namespace="\App\Models" uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f">
      <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="ede684d5-3c30-415f-a1c8-2ceffd7b1d02"/>
      <field name="domain_id" type="integer" required="true" unsigned="true" uuid="f3e9b42f-1c6b-41eb-9a13-3b0f749ba85f"/>
      <field name="display_name" type="string" size="25" required="true" uuid="a0ca0b95-63d9-4680-b4c8-b5a2c5c168a9"/>
      <field name="link" type="string" size="150" required="true" uuid="e5f4fa07-b532-4ea6-83d9-f1cdb1b17ac9"/>
      <field name="created_at" type="timestamp" uuid="cc663b6e-43b7-4aad-b404-9e0aa8c3f622"/>
      <field name="updated_at" type="timestamp" uuid="6d4388f7-a332-481a-9583-88c6b5c3e887"/>
    </entity>
    <association from="\App\Models\socials" to="\App\Models\Domain" owner-alias="socials" inverse-alias="domain" many-owner="true" many-inverse="false" parent-required="true" uuid="079c3434-7d6d-4469-868d-0dc423ef52a8">
      <association-field from="domain_id" to="id" uuid="119b4bfa-dbdd-4e7f-a796-e8be05e1d227"/>
    </association>
    <entity name="\App\Models\Emaillogentry" local-name="Emaillogentry" namespace="\App\Models" uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418">
      <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="4cd27654-88e7-4e2e-9919-1c121e562d62"/>
      <field name="domain_id" type="integer" required="true" unsigned="true" uuid="3800dd68-0a75-4bcb-a1f5-23a7154a025f"/>
      <field name="to" type="string" size="255" required="true" uuid="ccccf173-15df-4ab5-8604-e5a8da330623"/>
      <field name="cc" type="string" size="255" uuid="aa3dbfb6-6278-4293-95b5-5ef69d53b279"/>
      <field name="bcc" type="string" size="255" uuid="b67f51d4-2f8e-4a58-88fc-85978fa02c76"/>
      <field name="from" type="string" size="255" required="true" uuid="6b6a80c9-c24b-40c0-bb5a-0e7798466a96"/>
      <field name="reply_to" type="string" size="255" uuid="c97c50e9-40db-4a6b-a067-b9805cd48305"/>
      <field name="subject" type="string" size="255" required="true" uuid="08d4ee0c-4136-4aa5-a518-8facee535c46"/>
      <field name="body" type="text" required="true" uuid="142d3954-0906-41ae-b854-0631bbe296f6"/>
      <field name="attachments" type="text" uuid="fa157359-d572-44be-8874-18da91739c14"/>
      <field name="unique_token" type="string" size="255" uuid="382255b1-7fdc-41c1-92fd-6e2bea061a5d"/>
      <field name="status" description="one of queued, sent, failed, unknown" type="string" size="255" default="queued" uuid="df6d4587-efe7-4df2-bd68-23117b737905"/>
      <field name="log" type="text" uuid="314d938c-9276-46f2-8ed6-ec6937e1dc51"/>
      <field name="created_at" type="timestamp" uuid="*************-4730-af14-48003e8ec831"/>
      <field name="updated_at" type="timestamp" uuid="0e9cc8fc-ed87-414b-bbee-d39cc5e494df"/>
    </entity>
    <association from="\App\Models\Emaillogentry" to="\App\Models\Domain" owner-alias="emaillogentries" inverse-alias="domain" many-owner="true" many-inverse="false" parent-required="true" uuid="06b83a03-b836-4a89-97bc-86e6b9d09dad">
      <association-field from="domain_id" to="id" uuid="67f3ff3c-f01a-4906-a929-e25ef8d1bf4a"/>
    </association>
    <region namespace="\App\Models" caption="supporting tables" uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62">
      <entity name="\App\Models\Cms" local-name="Cms" namespace="\App\Models" uuid="81a60ca3-167f-4580-8eb5-205daa105ed1">
        <field name="id" type="integer" required="true" unique="true" primary="true" auto-increment="true" uuid="d9b66b85-e820-4884-9e93-1ebea2c19086"/>
        <field name="online" type="boolean" required="true" uuid="91b5568c-986d-4310-9416-97d1328b85d3"/>
        <orm-attributes>
          <attribute name="table">cms</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\FailedJob" local-name="FailedJob" namespace="\App\Models" uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="eedbe303-09eb-4dcd-a9cb-6b975793522a"/>
        <field name="connection" type="text" required="true" uuid="61acbcf3-0661-4ff7-8581-3d178e188c6d"/>
        <field name="queue" type="text" required="true" uuid="0a1bf19b-2f07-4e62-b343-51b5c15075a7"/>
        <field name="payload" type="longText" required="true" uuid="e5ba9a1e-49da-431a-a7cd-66a01d734093"/>
        <field name="exception" type="longText" required="true" uuid="90dc3d54-8a5e-42c9-a243-ee394fd385df"/>
        <field name="failed_at" type="timestamp" default="NOW()" required="true" uuid="5aca1050-93aa-4520-bec2-af226b272386"/>
        <orm-attributes>
          <attribute name="table">failed_jobs</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Job" local-name="Job" namespace="\App\Models" uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="f35be065-99a0-4a7b-bab6-6c53e808a99a"/>
        <field name="queue" type="string" size="255" required="true" uuid="cd2e9923-3422-469b-b3e3-b353fa214641"/>
        <field name="payload" type="longText" required="true" uuid="b5f23daa-d4ff-47c2-83e8-cb9440fec1e8"/>
        <field name="attempts" type="tinyInteger" required="true" unsigned="true" uuid="00d83886-a588-41c5-bbef-bdc6535b85cf"/>
        <field name="reserved_at" type="integer" unsigned="true" uuid="2593bfc3-bbc0-4731-876c-e89b5ef40d9c"/>
        <field name="available_at" type="integer" required="true" unsigned="true" uuid="ad12febf-8912-4114-b430-de68465a2bbd"/>
        <field name="created_at" type="integer" required="true" unsigned="true" uuid="7b04231e-0fd3-4c95-ab18-716d1f8312ea"/>
        <index name="jobs_queue_index" uuid="b6ac47a4-40ee-476c-9c44-79a6a95b707f">
          <index-field name="queue" uuid="1792e646-de97-4c75-b3de-bd9c526d7db3"/>
        </index>
        <orm-attributes>
          <attribute name="table">jobs</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthAccessToken" local-name="OauthAccessToken" namespace="\App\Models" uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="92649eac-983b-481d-a465-17bbc79871eb"/>
        <field name="user_id" type="integer" uuid="ee30cb83-7ec6-483b-a399-9b41267b2bab"/>
        <field name="client_id" type="integer" required="true" uuid="c5a7fe5e-c5bd-434a-a666-031e829e6a4b"/>
        <field name="name" type="string" size="255" uuid="e49d8225-71f7-463c-ba7e-5f08354ca8a0"/>
        <field name="scopes" type="text" uuid="0798681d-66f5-497c-ad46-fcb592f66c3e"/>
        <field name="revoked" type="boolean" required="true" uuid="84208c09-1aad-4d52-9e68-f99d8295397b"/>
        <field name="created_at" type="timestamp" uuid="0e34e722-9834-46f4-8246-00b60f61dc9a"/>
        <field name="updated_at" type="timestamp" uuid="b22aa0cd-471a-4a64-8f90-d8372bfadc71"/>
        <field name="expires_at" type="dateTime" uuid="de5fa9b4-7da9-42c5-8673-130fb1c57222"/>
        <index name="oauth_access_tokens_user_id_index" uuid="b3fd544a-089e-4bf1-ace0-69dfb54b446f">
          <index-field name="user_id" uuid="c04d4d90-16ea-4266-809d-4c590041a12b"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_access_tokens</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthAuthCode" local-name="OauthAuthCode" namespace="\App\Models" uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="8a2bf9ac-cbab-4b6b-beee-262e090ac2f6"/>
        <field name="user_id" type="integer" required="true" uuid="845305f2-3472-4d21-af08-79d99ec02107"/>
        <field name="client_id" type="integer" required="true" uuid="2ffc2131-1b09-4da7-8d21-96ef248eb6ce"/>
        <field name="scopes" type="text" uuid="e62cd9ba-4e27-4120-b5c4-bfeccb8e4fac"/>
        <field name="revoked" type="boolean" required="true" uuid="426c1db7-4faf-4f3c-b4dd-4e916db44591"/>
        <field name="expires_at" type="dateTime" uuid="a2db9618-a482-4703-9fb0-51d5bc5fc53c"/>
        <orm-attributes>
          <attribute name="table">oauth_auth_codes</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthClient" local-name="OauthClient" namespace="\App\Models" uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="f2dd5495-8ab7-4977-8990-e5e3b56999ce"/>
        <field name="user_id" type="integer" uuid="27f2240f-4d2a-4d99-9056-11a4896a697d"/>
        <field name="name" type="string" size="255" required="true" uuid="304b3468-774d-4fef-ad66-6f2139931dae"/>
        <field name="secret" type="string" size="100" required="true" uuid="5a45549a-3c9f-478d-abaa-32b3ee3eb4ef"/>
        <field name="redirect" type="text" required="true" uuid="0ff1aa4a-e3ec-467b-8333-b94d3730504b"/>
        <field name="personal_access_client" type="boolean" required="true" uuid="0a12844e-2a1b-4fd2-80ba-28d6d573c5ee"/>
        <field name="password_client" type="boolean" required="true" uuid="0e84c4b5-337a-46ba-8565-6627d68bcce8"/>
        <field name="revoked" type="boolean" required="true" uuid="594aa72f-a1e1-4f75-a55b-9aa36354fbbc"/>
        <field name="created_at" type="timestamp" uuid="d41e4de0-3766-46ba-897e-88d21a47a4a5"/>
        <field name="updated_at" type="timestamp" uuid="648ac18b-2107-484b-9441-4f986c743ff6"/>
        <index name="oauth_clients_user_id_index" uuid="ef541323-ef91-4a5d-af6b-a66e07bb1af5">
          <index-field name="user_id" uuid="e41cfd1c-7cfa-4227-8f06-b8c33a86a3d1"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_clients</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthPersonalAccessClient" local-name="OauthPersonalAccessClient" namespace="\App\Models" uuid="8e206929-1d19-46af-b6df-4c0df9b99050">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="059087ba-1946-4472-9dba-14141bd05834"/>
        <field name="client_id" type="integer" required="true" uuid="72d69579-776a-4726-b4c8-164b87f247dd"/>
        <field name="created_at" type="timestamp" uuid="a66ae9f0-1921-49ab-94b9-84225f834706"/>
        <field name="updated_at" type="timestamp" uuid="b110cd40-fb82-4887-93a6-e27317528d95"/>
        <index name="oauth_personal_access_clients_client_id_index" uuid="af91c313-763e-43bf-83c5-193823596fc1">
          <index-field name="client_id" uuid="871122ae-dd1c-4902-b6be-7b0665fdc450"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_personal_access_clients</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\OauthRefreshToken" local-name="OauthRefreshToken" namespace="\App\Models" uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c">
        <field name="id" type="string" size="100" required="true" unique="true" primary="true" uuid="586adc7b-90be-4faf-802d-4bb00204035e"/>
        <field name="access_token_id" type="string" size="100" required="true" uuid="d28debc7-ee24-4122-9840-c52f3a3ae6c0"/>
        <field name="revoked" type="boolean" required="true" uuid="92cfc78a-42d1-4123-b72c-375f188dd42c"/>
        <field name="expires_at" type="dateTime" uuid="55e6eecf-4d7a-4ed8-9b4e-0994d76faab3"/>
        <index name="oauth_refresh_tokens_access_token_id_index" uuid="c845f252-c907-4f5f-ad8a-f17618bf7141">
          <index-field name="access_token_id" uuid="0af9d6ce-f03f-4a24-a072-dd1763d474c4"/>
        </index>
        <orm-attributes>
          <attribute name="table">oauth_refresh_tokens</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\PasswordReset" local-name="PasswordReset" namespace="\App\Models" uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c">
        <field name="email" type="string" size="255" required="true" uuid="f3895319-254c-474a-a83d-8b06146c6345"/>
        <field name="token" type="string" size="255" required="true" uuid="a1ebbb9f-19f4-437f-b0ea-19e5c75f28e4"/>
        <field name="created_at" type="timestamp" uuid="abf08fe9-c35f-4d6f-968a-2624475a6e4c"/>
        <index name="password_resets_email_index" uuid="4f3b191b-31e8-428b-990c-b243e49ab1e3">
          <index-field name="email" uuid="0496c651-f18d-4b5a-b2d0-120c603ebf60"/>
        </index>
        <index name="password_resets_token_index" uuid="f0d3ed94-23a6-4dbf-9788-d45349a1317b">
          <index-field name="token" uuid="731bf748-096e-46e8-8133-d8f001518445"/>
        </index>
        <orm-attributes>
          <attribute name="table">password_resets</attribute>
        </orm-attributes>
      </entity>
    </region>
    <region namespace="\App\Models" caption="Tickets" uuid="6e68c994-ef99-40f3-addf-4c269530aead">
      <entity name="\App\Models\Reservationnumber" local-name="Reservationnumber" namespace="\App\Models" uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="2d80d1ae-4963-4b54-b85d-c06aaf1ea398"/>
        <field name="reservation_id" type="bigInteger" required="true" unsigned="true" uuid="46f8f5cb-5c2d-4f93-ac69-5e40e2e7d494"/>
        <field name="reservation_number" type="string" size="15" required="true" uuid="30737666-0afc-4889-b6c3-4f91aa16cd57"/>
        <field name="created_at" type="timestamp" uuid="ecaa3962-80df-46d5-b8e6-3961a2998c32"/>
        <field name="updated_at" type="timestamp" uuid="dccacf43-2391-4480-815f-2286ad6bb88d"/>
      </entity>
      <entity name="\App\Models\Reservation" local-name="Reservation" namespace="\App\Models" uuid="e41265a8-bb46-40a8-bf42-64e4b2782703">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="a4bea739-27da-4e2e-bd41-39a25c207cf0"/>
        <field name="email" type="string" size="50" required="true" uuid="f32a46ee-de30-4847-baa8-dbc89b8eda62"/>
        <field name="fullname" type="string" size="50" uuid="c5a08820-a082-46dd-9bc5-bb3d63798f58"/>
        <field name="status" type="enum" enum-values="'pending', 'confirmed', 'canceled'" uuid="c0a0f818-e341-44c8-a06f-9966b86dddb7"/>
        <field name="num_tickets" type="integer" required="true" uuid="8c220577-c0b9-4620-a998-09ea85e809e1"/>
        <field name="created_at" type="timestamp" uuid="82ba1226-065f-46fe-a643-f28ad6284ca4"/>
        <field name="updated_at" type="timestamp" uuid="*************-49af-a9b5-1e06ccbba353"/>
        <field name="event_id" type="integer" unsigned="true" uuid="b1c8913e-d913-49ce-b7c5-bf1935711aa9"/>
      </entity>
      <entity name="\App\Models\Event" local-name="Event" namespace="\App\Models" uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="17785a1b-92b8-493e-8302-a94460cbc48e"/>
        <field name="performance_id" type="bigInteger" unsigned="true" uuid="1877bd8b-c8a4-402f-b064-952cbc6f9fbf"/>
        <field name="calendar_id" type="integer" required="true" unsigned="true" uuid="1cfad8d3-1b1b-42cd-82c6-299e4035b9c4"/>
        <field name="title" type="string" size="100" uuid="ef8afd7e-ffc9-4f06-84e3-4c29d7596e56"/>
        <field name="description" type="text" uuid="98ea17f1-d69a-461b-a681-2912dff5a60a"/>
        <field name="start_datetime" type="dateTime" required="true" uuid="88843e88-af55-4888-b117-1a110e55c87b"/>
        <field name="max_tickets" type="integer" required="true" uuid="842675c2-0e96-423d-972d-53c18500a2d1"/>
        <field name="venue_desc_1" type="string" size="255" required="true" uuid="6ec71544-6e11-4104-9532-42eb053a1cbf"/>
        <field name="venue_desc_2" type="string" size="255" uuid="98a4f3d2-02b7-438c-80de-604edb761669"/>
        <field name="created_at" type="timestamp" uuid="9e2a9f0d-b76e-473a-98a9-c20a2d2182e7"/>
        <field name="updated_at" type="timestamp" uuid="e467ec47-0a87-42ce-9444-34b667acf08c"/>
        <orm-attributes>
          <attribute name="table">events</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Performance" local-name="Performance" namespace="\App\Models" uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="f1a3bcc8-f7db-4427-bcf8-3d8bffbe5338"/>
        <field name="title" type="string" size="50" required="true" uuid="8babf87e-b18c-44bb-bf29-eb8d66b7929e"/>
        <field name="description" type="text" uuid="32a62df3-60b1-47f6-a67e-ada49d9a256e"/>
        <field name="duration_minutes" type="integer" uuid="8e879eeb-cdc8-4377-973a-dd8149437dce"/>
        <field name="published_on_web" type="boolean" default="false" required="true" uuid="625da235-cb2e-4798-bc13-1fe3086a2edb"/>
        <field name="created_at" type="timestamp" uuid="32b30228-5e12-4336-a530-e4dc969df7ee"/>
        <field name="updated_at" type="timestamp" uuid="19f42fe7-1cee-4512-8824-609f82c25f1c"/>
      </entity>
      <entity name="\App\Models\TicketType" local-name="TicketType" namespace="\App\Models" uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="4227f7cb-85b2-414a-b79d-a6255320affe"/>
        <field name="name" description="e.g. child, oap, loge,...." type="string" size="50" default="standard" required="true" uuid="f9d617a1-d0f4-4623-8ab1-6a2fda1e6678"/>
        <field name="code" type="string" size="3" default="std" required="true" uuid="49933f4c-18d4-48c7-a844-2cc973227857"/>
        <field name="created_at" type="timestamp" uuid="92ca17e2-9eaa-4c04-8648-d2925fb7e080"/>
        <field name="updated_at" type="timestamp" uuid="6c4ff461-120b-4025-a7a9-81903b6d99ac"/>
      </entity>
      <entity name="\App\Models\EventTickettype" local-name="EventTickettype" namespace="\App\Models" uuid="bd194a83-8d1b-424b-9912-bda3a6064be6">
        <field name="event_id" type="integer" required="true" primary="true" unsigned="true" uuid="20906b66-4529-415f-82d8-35a412860d2b"/>
        <field name="ticket_type_id" type="bigInteger" required="true" primary="true" unsigned="true" uuid="9520214b-dc93-4784-996c-7ce59846e805"/>
      </entity>
      <comment caption="calendar_id" description="calendar_id is still here to be ablte to create a migration script" uuid="4508cea5-f461-4b61-9e62-399c62220d50"/>
    </region>
    <association from="\App\Models\Reservationnumber" to="\App\Models\Reservation" owner-alias="reservationnumbers" inverse-alias="reservation" many-owner="true" many-inverse="false" parent-required="true" uuid="01c12284-9ee2-45c6-9a77-4c72bb101e11">
      <association-field from="reservation_id" to="id" uuid="e6b5537f-5f8d-414e-8718-c963d83b07a0"/>
    </association>
    <association from="\App\Models\Reservation" to="\App\Models\Event" owner-alias="reservations" inverse-alias="event" many-owner="true" many-inverse="false" uuid="681da3ff-60a7-46dd-b7d7-0deecf6baecb">
      <association-field from="event_id" to="id" uuid="e9057c76-fefe-4c51-9554-5cfb468648be"/>
    </association>
    <association from="\App\Models\Event" to="\App\Models\Performance" owner-alias="events" inverse-alias="performance" many-owner="true" many-inverse="false" uuid="24ad61b4-a3e1-4c96-ae7e-25a1a920a17f">
      <association-field from="performance_id" to="id" uuid="156c5bb4-9dcc-41ac-974e-79f8d07e419c"/>
    </association>
    <many-to-many mn-entity="\App\Models\EventTickettype" uuid="15095fdc-1c49-4ab9-b218-0d393cb4f877">
      <many-to-many-entity name="\App\Models\Event" owning-side="true" alias="events" uuid="f872f1ba-9c05-47e7-a93b-8afccdd92365">
        <many-to-many-field from="event_id" to="id" uuid="5f049e8e-3dd3-4cd5-bfad-0c0b8b3beee8"/>
      </many-to-many-entity>
      <many-to-many-entity name="\App\Models\TicketType" owning-side="false" alias="ticketTypes" uuid="069c10e6-0043-4ca3-a7b1-8b54ac5defb7">
        <many-to-many-field from="ticket_type_id" to="id" uuid="ae2b19f6-75d6-4940-a979-e321baeeeee0"/>
      </many-to-many-entity>
    </many-to-many>
    <region namespace="\App\Models" caption="Repertoire" uuid="e3e823bc-dbef-4de8-bb49-f489359471a1">
      <entity name="\App\Models\Setlist" local-name="Setlist" namespace="\App\Models" uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f">
        <field name="id" type="integer" required="true" unique="true" primary="true" auto-increment="true" uuid="ec0d0b28-eb8a-4c99-8ef3-831876311b43"/>
        <field name="naam" type="string" size="50" required="true" uuid="3e64ffc0-057f-4e5d-8884-edd396799abe"/>
        <field name="datum" type="date" required="true" uuid="e1c5f276-e67c-46a3-b40b-938bbc5f6c71"/>
        <field name="songs" type="string" size="75" uuid="f9f94f9b-dcf8-4823-9f1b-fba95993bca0"/>
        <field name="id_domein" type="integer" required="true" uuid="07169843-a02b-4043-8679-061c264bf682"/>
        <field name="omschrijving" type="string" size="256" uuid="d2823d6e-1f4e-4e18-ba1d-e1caf50df664"/>
        <field name="timestamp" type="timestamp" default="NOW()" required="true" uuid="79d01b2f-d68c-48f8-b4d6-7c07dcb93383"/>
        <orm-attributes>
          <attribute name="table">setlist</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Songitemfile" local-name="Songitemfile" namespace="\App\Models" uuid="79f657f0-**************-473b038ae0b6">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="9f4da929-63c5-4dfe-afe6-ae49ec87e89a"/>
        <field name="songitem_id" type="integer" required="true" unsigned="true" uuid="c3585b2a-f2a6-4866-9427-a1571bd3f7fc"/>
        <field name="url" type="string" size="255" required="true" uuid="52620195-20d0-4aa9-9df3-2ea86773fee5"/>
        <field name="original_name" type="string" size="255" required="true" uuid="59494aec-7cc2-4491-ac6d-53f401169ee2"/>
        <field name="history_date" type="date" uuid="c8db2f7a-3cf5-4633-84a5-1b2b0e5288ad"/>
        <field name="created_at" type="timestamp" uuid="f112fa5c-d374-4d70-88b0-1e68f1108073"/>
        <field name="updated_at" type="timestamp" uuid="4304db42-4efb-4a0c-9432-1775971f1e2d"/>
        <orm-attributes>
          <attribute name="table">songitemfiles</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Songitem" local-name="Songitem" namespace="\App\Models" uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="390a9503-bfb6-44d4-9116-98454406fef4"/>
        <field name="song_id" type="integer" unsigned="true" uuid="2f0d2b9e-a94e-4bd0-ab93-47a47289f507"/>
        <field name="name" type="string" size="50" required="true" uuid="1bf81c64-291b-44a6-94e5-b9912d85f0ed"/>
        <field name="linktype" type="string" size="25" required="true" uuid="a75ff409-5cbd-4ab1-a6a8-2d053992ea8c"/>
        <field name="own_recording" type="smallInteger" default="0" required="true" uuid="17f77cb4-4b29-49d1-aecf-0628bfa49ca8"/>
        <field name="reference" type="string" size="50" uuid="d0345917-92b3-4af0-82e9-bc46f3f3818b"/>
        <field name="created_at" type="timestamp" uuid="03874a9f-c51a-4540-a57d-3ebf642ec567"/>
        <field name="updated_at" type="timestamp" uuid="cb3c34a6-1301-43aa-9b95-1b679cf1b7ed"/>
        <orm-attributes>
          <attribute name="table">songitems</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Song" local-name="Song" namespace="\App\Models" uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="c3505b76-322e-43a6-89fa-6ced1d1c9fc0"/>
        <field name="domain_id" type="integer" unsigned="true" uuid="414a1131-a324-469b-a8f6-6c0dcdd425f7"/>
        <field name="title" type="string" size="75" required="true" uuid="cf23d073-4477-43e7-a2b3-fcc63c40427b"/>
        <field name="year" type="string" size="15" uuid="b37fe35e-e8a3-4bb9-b822-cabe9da8e2e5"/>
        <field name="tags" type="string" size="50" uuid="2afdf8e0-a780-4c68-8f89-e28a85fc0b57"/>
        <field name="time_in_setlist" type="string" size="50" uuid="5522a9d3-b123-40c4-8297-20a663aeed2e"/>
        <field name="date_in_repertoire" type="date" uuid="d70c6a69-01fd-46f3-a37f-37809c492898"/>
        <field name="archive_number" type="string" size="25" uuid="19d4f448-56c2-4984-84d4-f03d4d42ae4c"/>
        <field name="composer1" type="string" size="50" uuid="8be731ef-5ad3-42b5-9674-258bfbed9c5d"/>
        <field name="composer2" type="string" size="50" uuid="696df5e3-201e-42f7-a111-150c1d2abda2"/>
        <field name="remarks" type="text" uuid="e96f9f42-8c6b-4e50-8488-5dff10e61461"/>
        <field name="created_at" type="timestamp" uuid="d1202b40-3f1d-4d0a-9090-1dd606c9efa7"/>
        <field name="updated_at" type="timestamp" uuid="f983fb7d-1b00-47af-94b3-3ffda95b138a"/>
        <orm-attributes>
          <attribute name="table">songs</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\Songitemfile" to="\App\Models\Songitem" owner-alias="songitemfiles" inverse-alias="songitems" many-owner="true" many-inverse="false" parent-required="true" uuid="64f95f2e-f94d-4606-b0d6-13eada0bcd44">
      <association-field from="songitem_id" to="id" uuid="65dfaf01-a633-48c8-b1e6-8b4d308a1e84"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Songitem" to="\App\Models\Song" owner-alias="songitems" inverse-alias="songs" many-owner="true" many-inverse="false" uuid="e5c31f2b-c548-4f47-83ae-0e365dbcd0ff">
      <association-field from="song_id" to="id" uuid="93f2258a-ad10-496c-a70c-b2a86f46cc8f"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Song" to="\App\Models\Domain" owner-alias="songs" inverse-alias="domains" many-owner="true" many-inverse="false" uuid="d77acbf8-d163-428a-b6c3-5039f27fbb5e">
      <association-field from="domain_id" to="id" uuid="211726f3-03b2-4892-872d-0d685537b04b"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <region namespace="\App\Models" caption="Admin" uuid="446ff82b-059f-4102-978b-842d4d5f3844">
      <entity name="\App\Models\RoleUser" local-name="RoleUser" namespace="\App\Models" uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62">
        <field name="user_id" type="integer" unsigned="true" uuid="2dd02f08-5e00-4729-9102-a481acc039ab"/>
        <field name="role_id" type="integer" unsigned="true" uuid="52d08e17-89e3-44e5-a889-1af37d0b4b92"/>
        <field name="created_at" type="timestamp" uuid="d426799e-8a78-485c-8cd9-235fad38c29f"/>
        <field name="updated_at" type="timestamp" uuid="db2a29b9-a1a1-4d34-8442-1b7c01af6b5f"/>
        <index name="role_user_role_id_foreign" uuid="89ac27aa-77cd-4163-b731-1d8646b24ccb">
          <index-field name="role_id" uuid="87a28503-175d-49a7-a13d-d0dca0875a4b"/>
        </index>
        <index name="role_user_user_id_foreign" uuid="6a455bfb-372b-4631-a905-21f99913bebe">
          <index-field name="user_id" uuid="95541da1-e867-4c5f-9141-5354170a1ebc"/>
        </index>
        <orm-attributes>
          <attribute name="table">role_user</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Role" local-name="Role" namespace="\App\Models" uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="1feacec2-ae83-49b0-961d-2072abeb7cd5"/>
        <field name="rolename" type="string" size="15" required="true" uuid="d0e78452-0043-4e40-974b-7d46d9132df5"/>
        <field name="created_at" type="timestamp" uuid="975af035-dd7f-4f9d-9732-d48be3e4389f"/>
        <field name="updated_at" type="timestamp" uuid="f85d6785-5050-489c-aea3-24cd0c87afbf"/>
        <orm-attributes>
          <attribute name="table">roles</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\User" local-name="User" namespace="\App\Models" uuid="441d93ef-93f8-431b-aead-7c85754ed18c">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="7fdb21ea-560f-429b-b0fa-2dbfebfa5e6b"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="81708b7f-c073-456b-bcbf-b6e772c96c5f"/>
        <field name="name" type="string" size="255" required="true" uuid="8fba5447-d309-48b9-a519-48315f53ad90"/>
        <field name="email" type="string" size="255" required="true" uuid="0e0aa7c7-1b61-40d3-ad09-8b939685ab80"/>
        <field name="password" type="string" size="255" required="true" uuid="33d0a092-8b20-4f7d-8884-41df3d1269a3"/>
        <field name="remember_token" type="string" size="100" uuid="f2376a5a-4be8-4ef0-a90a-63026c94af9c"/>
        <field name="api_token" type="string" size="60" uuid="149a799e-b71a-4db4-b5d5-aae55f63a027"/>
        <field name="created_at" type="timestamp" uuid="0a1e50d6-67be-4a57-8602-4ef5d2d4dc78"/>
        <field name="updated_at" type="timestamp" uuid="1767ab5a-c906-4bf2-9b94-f754e06ce787"/>
        <index name="user_domain_id_foreign" uuid="d37369e2-08a4-4cfa-8c48-a51b4b11c9b9">
          <index-field name="domain_id" uuid="a1486e86-25f9-4e29-a6f1-539485be67c1"/>
        </index>
        <index name="users_api_token_unique" unique="true" uuid="78a61b7c-b338-47e4-8658-015759ff7675">
          <index-field name="api_token" uuid="b8e8f39f-c4eb-45da-aadd-90497debb6ae"/>
        </index>
        <index name="users_email_unique" unique="true" uuid="d93260ca-a629-4a74-8d1e-948c3bd75e90">
          <index-field name="email" uuid="8d99ddd4-cf66-4dfa-a461-29ce3649b250"/>
        </index>
        <orm-attributes>
          <attribute name="table">users</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\RoleUser" to="\App\Models\User" owner-alias="roleUsers" inverse-alias="user" many-owner="true" many-inverse="false" uuid="ebd5a700-d190-4cdd-bdfb-059d27e405b8">
      <association-field from="user_id" to="id" uuid="13d41ce2-06af-43b6-89fd-27415273caa4"/>
    </association>
    <association from="\App\Models\RoleUser" to="\App\Models\Role" owner-alias="roleUsers" inverse-alias="role" many-owner="true" many-inverse="false" uuid="f0be962e-c320-4801-b408-3c27334358ee">
      <association-field from="role_id" to="id" uuid="7d526b93-4f46-4b08-ae65-f373a0650fc3"/>
    </association>
    <association from="\App\Models\User" to="\App\Models\Domain" owner-alias="users" inverse-alias="domain" many-owner="true" many-inverse="false" parent-required="true" uuid="be6234fd-73d7-4597-9e7e-39a09a680f5e">
      <association-field from="domain_id" to="id" uuid="0df1c7c6-289f-4969-8432-1739993fe797"/>
    </association>
    <region namespace="\App\Models" caption="Calendar" uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b">
      <entity name="\App\Models\Calendar" local-name="Calendar" namespace="\App\Models" uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="d48e9931-3168-4f5a-aaa7-5af5d8a6fc31"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="524433c6-5087-4df1-9b17-a1bb3bf61301"/>
        <field name="name" type="string" size="100" uuid="9c89a6fc-e4aa-47c6-a269-ecc7ef044120"/>
        <field name="description" type="text" uuid="e123f43a-e9e2-4f74-b72a-e331760d3481"/>
        <field name="is_public" type="tinyInteger" uuid="52faa212-7c8a-49fe-bd87-e08f7bc3c503"/>
        <field name="created_at" type="timestamp" uuid="5a99be28-feac-48c1-a8a2-34dcd9622313"/>
        <field name="updated_at" type="timestamp" uuid="12303e6b-dccc-49e7-839c-5d17224a6618"/>
        <index name="events_domain_id_foreign" uuid="46571efb-e12c-4aea-9eda-745d5061e0fb">
          <index-field name="domain_id" uuid="e6c40051-c774-4527-a650-2e40a7e4d166"/>
        </index>
        <orm-attributes>
          <attribute name="table">calendars</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\CalendarMemberfunction" local-name="CalendarMemberfunction" namespace="\App\Models" uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7">
        <field name="calendar_id" type="integer" required="true" primary="true" unsigned="true" uuid="bad42bc1-039a-413e-b2a2-535a85de24cb"/>
        <field name="memberfunction_id" type="integer" required="true" primary="true" unsigned="true" uuid="9584accf-c83f-449d-85ac-ef59c051ea7e"/>
      </entity>
      <entity name="\App\Models\calendar_event" local-name="calendar_event" namespace="\App\Models" uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478">
        <field name="id" type="bigInteger" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="a07d2622-6b59-4746-ad76-a587ed1e1f9b"/>
        <field name="calendar_id" type="integer" unsigned="true" uuid="e3a5ad69-19f7-48b3-9537-d80560d7fb6c"/>
        <field name="event_id" type="integer" unsigned="true" uuid="4b3ba359-cbe9-4bc9-a6e0-2c9bcc37f51c"/>
        <field name="created_at" type="timestamp" uuid="dd0679f0-238b-44ef-9d6d-67e65e3b41b8"/>
        <field name="updated_at" type="timestamp" uuid="cda2b7b4-1ce8-47ba-af70-dbdf4d8b91a0"/>
      </entity>
      <comment caption="limitation" description="only non-public calendars can be limitted to specific memberfunctions" uuid="a3c5959d-3824-4cd8-b0d6-74d204485b94"/>
    </region>
    <association from="\App\Models\Calendar" to="\App\Models\Domain" owner-alias="calendars" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="617802d2-a8dc-4c51-8e05-6e668183351b">
      <association-field from="domain_id" to="id" uuid="c4de38a7-413b-4a40-8d82-8ce45ff94bc8"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <many-to-many mn-entity="\App\Models\CalendarMemberfunction" uuid="7ee267a8-958f-4b7c-98be-90d6956af2dc">
      <many-to-many-entity name="\App\Models\Calendar" owning-side="true" alias="calendars" uuid="615d9315-8baf-45af-9131-db36712014d8">
        <many-to-many-field from="calendar_id" to="id" uuid="1defd779-a7f9-490c-a68f-571f1da9e73b"/>
      </many-to-many-entity>
      <many-to-many-entity name="\App\Models\Memberfunction" owning-side="false" alias="memberfunctions" uuid="f29e2705-d23f-40fb-b296-ae37e13face2">
        <many-to-many-field from="memberfunction_id" to="id" uuid="00cfd129-6fb1-450d-96bf-8b0f26a0a5d6"/>
      </many-to-many-entity>
    </many-to-many>
    <association from="\App\Models\calendar_event" to="\App\Models\Calendar" owner-alias="calendarEvents" inverse-alias="calendar" many-owner="true" many-inverse="false" uuid="7523d9e9-2d8a-4dd2-8f28-5308e2768d8f">
      <association-field from="calendar_id" to="id" uuid="e9acc54b-da10-4a98-9dd5-8ecbfb438bee"/>
    </association>
    <association from="\App\Models\calendar_event" to="\App\Models\Event" owner-alias="calendarEvents" inverse-alias="event" many-owner="true" many-inverse="false" uuid="59934611-12c7-43e4-8a31-57fb1be3e8af">
      <association-field from="event_id" to="id" uuid="4320c5ff-b766-46df-bd90-62c83b478242"/>
    </association>
    <region namespace="\App\Models" caption="Members" uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600">
      <entity name="\App\Models\MemberMemberfunction" local-name="MemberMemberfunction" namespace="\App\Models" uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1">
        <field name="memberfunction_id" type="integer" required="true" primary="true" unsigned="true" uuid="72f8d58c-fa0b-4130-9ef2-484bcef0e041"/>
        <field name="member_id" type="integer" required="true" primary="true" unsigned="true" uuid="95a9855f-84d8-4fc7-a4aa-5fd6e3367705"/>
        <field name="created_at" type="timestamp" uuid="73f8520e-f511-4a3a-b5f0-57b398a71625"/>
        <field name="updated_at" type="timestamp" uuid="256d416a-5c98-4035-8274-eb0b187b6fc6"/>
        <index name="member_memberfunction_function_id_foreign" uuid="1369ea87-542b-468b-8939-c3ecdde46d8e">
          <index-field name="memberfunction_id" uuid="48883a3d-d7fe-450a-b549-2ceabbe104d1"/>
        </index>
        <index name="member_memberfunction_member_id_foreign" uuid="28f2824f-1f41-48f0-94fa-eaa9f90efa1c">
          <index-field name="member_id" uuid="9ffa4797-500e-4997-8321-515675c8a923"/>
        </index>
        <orm-attributes>
          <attribute name="table">member_memberfunction</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Memberfunction" local-name="Memberfunction" namespace="\App\Models" uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="0c7ad6a7-e3e8-42f5-a7cd-cbdc6980103a"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="9a098640-3b52-47ba-8fdd-3e92040747af"/>
        <field name="description" type="string" size="45" required="true" uuid="4073d354-6607-44b9-87cd-cfb9a2d3269a"/>
        <field name="created_at" type="timestamp" uuid="a42753ca-21f8-4688-aa0d-f74a56a1e793"/>
        <field name="updated_at" type="timestamp" uuid="0f985c29-09de-46ff-83bd-620b92797514"/>
        <orm-attributes>
          <attribute name="table">memberfunctions</attribute>
        </orm-attributes>
      </entity>
      <entity name="\App\Models\Member" local-name="Member" namespace="\App\Models" uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8">
        <field name="id" type="integer" required="true" unique="true" primary="true" unsigned="true" auto-increment="true" uuid="193b01ca-0b08-460e-8de3-facd12bdd767"/>
        <field name="domain_id" type="integer" required="true" unsigned="true" uuid="8d54451d-c9df-4959-a33a-1bbaba2956c5"/>
        <field name="first_name" type="string" size="45" required="true" uuid="b8dff271-3190-450d-8e30-89cf1b431ce0"/>
        <field name="infix" type="string" size="45" uuid="1193cea6-2e0e-417b-8550-968252359fe7"/>
        <field name="last_name" type="string" size="45" required="true" uuid="5d4aa437-e9d0-479d-a712-438665e45d56"/>
        <field name="streetname" type="string" size="45" uuid="4803c598-59a6-460c-aba4-e65b6a0b510d"/>
        <field name="house_number" type="string" size="10" uuid="946bae31-37c9-4966-9818-7ab939497242"/>
        <field name="zipcode" type="string" size="10" uuid="cfca6a62-962c-4d50-8efe-fbabde878675"/>
        <field name="city" type="string" size="45" uuid="aa71101f-3ce3-49e9-abd5-e54449f9731a"/>
        <field name="birth_date" type="string" size="5" uuid="46aa16cf-4be0-4a39-805e-577c8831b7e4"/>
        <field name="birth_year" type="integer" uuid="b1b2cdeb-f570-4ca2-b0e2-804dadf0c351"/>
        <field name="telephone1" type="string" size="100" uuid="43ef9dcf-43fd-4f54-bd2d-7a871b629327"/>
        <field name="telephone2" type="string" size="100" uuid="a9725276-5d62-4004-b506-db74e1fd1585"/>
        <field name="email" type="string" size="100" uuid="088ff2c2-1bb2-4768-9b5d-cc71ea23e5d0"/>
        <field name="email_subscription" type="boolean" uuid="5655e6a5-ab52-4f7f-9762-2f4d33808213"/>
        <field name="membership_start_date" type="date" uuid="f60ba56c-0124-4998-8f35-7219b7eebe01"/>
        <field name="membership_end_date" type="date" uuid="9245051f-ece7-459a-ab78-62067996cd77"/>
        <field name="photo" type="string" size="45" uuid="3965fd84-0e41-4eb5-97a5-c8f843fe935b"/>
        <field name="description" type="text" uuid="2d5c62b0-2f9e-4b25-a24b-ebdaafe76f2f"/>
        <field name="remarks" type="text" uuid="65a85059-f743-48fe-ba60-7562e7e9a095"/>
        <field name="username" type="string" size="255" uuid="268f9172-131c-4538-a76f-6fd81be267ef"/>
        <field name="passwd" type="string" size="255" uuid="19ba3c34-a62f-4603-9019-f4941daa3c47"/>
        <field name="password_reset_code" type="string" size="150" uuid="ac82e3c7-bc0a-4361-9311-347eeb39f82f"/>
        <field name="pw_reset_valid_until" type="dateTime" uuid="a17d0c8f-f1da-4f19-8a38-8b6b9c3d47f6"/>
        <field name="created_at" type="timestamp" uuid="8a0c9364-5bed-45ca-bfcc-1081150a6b93"/>
        <field name="updated_at" type="timestamp" uuid="3bc1c4a0-51d2-44a1-aef6-ce0196e98639"/>
        <index name="members_domain_id_foreign_old" uuid="e7d8473b-8bf3-4986-8f3d-733b1910d6a2">
          <index-field name="domain_id" uuid="7ddc9dac-7f6c-413d-a93c-52280ddb4f1f"/>
        </index>
        <index name="members_username_unique" unique="true" uuid="6c2d7818-14f1-4292-9494-1ec21744bc10">
          <index-field name="username" uuid="b43f371c-e1b0-44a1-a2e6-7ac37b0316df"/>
        </index>
        <orm-attributes>
          <attribute name="table">members</attribute>
        </orm-attributes>
      </entity>
    </region>
    <association from="\App\Models\MemberMemberfunction" to="\App\Models\Memberfunction" owner-alias="memberMemberfunctions" inverse-alias="memberfunction" many-owner="true" many-inverse="false" parent-required="true" uuid="f2ec6c96-e24c-4217-8c74-c556936e2da9">
      <association-field from="memberfunction_id" to="id" uuid="bfa05c00-236c-48b6-8484-8bbcefc118e4"/>
    </association>
    <association from="\App\Models\MemberMemberfunction" to="\App\Models\Member" owner-alias="memberMemberfunctions" inverse-alias="member" many-owner="true" many-inverse="false" parent-required="true" uuid="e8b0c164-e449-4de9-98ee-9228ec88e844">
      <association-field from="member_id" to="id" uuid="b57f5dc3-abe9-4b7f-ad9c-de08f03ac803"/>
    </association>
    <association from="\App\Models\Memberfunction" to="\App\Models\Domain" owner-alias="memberfunctions" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="26ed87af-afdb-4d88-b4ba-72688be3780b">
      <association-field from="domain_id" to="id" uuid="1d453e76-6561-427b-bd2c-ecfcf6becb06"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <association from="\App\Models\Member" to="\App\Models\Domain" owner-alias="members" inverse-alias="domains" many-owner="true" many-inverse="false" parent-required="true" uuid="4d340f46-2f02-44b0-b63f-0f8574fbb320">
      <association-field from="domain_id" to="id" uuid="8b5f752a-a44b-458f-bfc3-6677bf200763"/>
      <orm-attributes>
        <attribute name="onDelete">CASCADE</attribute>
        <attribute name="onUpdate">NO ACTION</attribute>
      </orm-attributes>
    </association>
    <orm-attributes>
      <attribute name="migrations-path">migrations</attribute>
      <attribute name="models-disabled">true</attribute>
    </orm-attributes>
    <migrations version="1.0">
      <revision uuid="252353b2-8ebe-43f6-ad39-af86c07adea4" date="2024-01-12 12:02:56.789891" exportable="ignored">
        <element action="add" uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="module">
          <property name="export-format" value="Laravel"/>
          <property name="export-path" value="."/>
          <property name="local-name" value="cms2017"/>
          <property name="local-namespace" value="App\Models"/>
          <property name="migrations-path" value="database/migrations"/>
          <property name="name" value="\cms2017"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Basisbedrag"/>
          <property name="name" value="\App\Models\Basisbedrag"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="basisbedrag"/>
        </element>
        <element action="add" uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="CalculatedField"/>
          <property name="name" value="\App\Models\CalculatedField"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="calculated_field"/>
        </element>
        <element action="add" uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Calendar"/>
          <property name="name" value="\App\Models\Calendar"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="calendars"/>
        </element>
        <element action="add" uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Cm"/>
          <property name="name" value="\App\Models\Cm"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="cms"/>
        </element>
        <element action="add" uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Content"/>
          <property name="name" value="\App\Models\Content"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="content"/>
        </element>
        <element action="add" uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="ContentHistorie"/>
          <property name="name" value="\App\Models\ContentHistorie"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="content_historie"/>
        </element>
        <element action="add" uuid="51fb4335-8a24-4360-a32d-acd8a2433244" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Contributie"/>
          <property name="name" value="\App\Models\Contributie"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="contributie"/>
        </element>
        <element action="add" uuid="3486427d-73f9-4133-96f8-4645ba654331" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Contributieperiode"/>
          <property name="name" value="\App\Models\Contributieperiode"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="contributieperiode"/>
        </element>
        <element action="add" uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Document"/>
          <property name="name" value="\App\Models\Document"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="document"/>
        </element>
        <element action="add" uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Domain"/>
          <property name="name" value="\App\Models\Domain"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domains"/>
        </element>
        <element action="add" uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Domaintype"/>
          <property name="name" value="\App\Models\Domaintype"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domaintypes"/>
        </element>
        <element action="add" uuid="6585548f-164a-4056-bb5e-685545f2d882" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Domein"/>
          <property name="name" value="\App\Models\Domein"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domein"/>
        </element>
        <element action="add" uuid="454be441-0c95-4305-b051-390e970926c3" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="DomeinRekenregel"/>
          <property name="name" value="\App\Models\DomeinRekenregel"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domein_rekenregel"/>
        </element>
        <element action="add" uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Domeintype"/>
          <property name="name" value="\App\Models\Domeintype"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="domeintype"/>
        </element>
        <element action="add" uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Emailqueue"/>
          <property name="name" value="\App\Models\Emailqueue"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="emailqueue"/>
        </element>
        <element action="add" uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Event"/>
          <property name="name" value="\App\Models\Event"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="events"/>
        </element>
        <element action="add" uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="FailedJob"/>
          <property name="name" value="\App\Models\FailedJob"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="failed_jobs"/>
        </element>
        <element action="add" uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Foto"/>
          <property name="name" value="\App\Models\Foto"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="foto"/>
        </element>
        <element action="add" uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Fotoverzameling"/>
          <property name="name" value="\App\Models\Fotoverzameling"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="fotoverzameling"/>
        </element>
        <element action="add" uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Functie"/>
          <property name="name" value="\App\Models\Functie"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="functie"/>
        </element>
        <element action="add" uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="HerhalingUitzondering"/>
          <property name="name" value="\App\Models\HerhalingUitzondering"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="herhaling_uitzondering"/>
        </element>
        <element action="add" uuid="3e835401-9d64-4134-ac69-80d9791dcd96" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Herhalingtype"/>
          <property name="name" value="\App\Models\Herhalingtype"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="herhalingtype"/>
        </element>
        <element action="add" uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Job"/>
          <property name="name" value="\App\Models\Job"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="jobs"/>
        </element>
        <element action="add" uuid="10dd906c-e63a-4959-8018-348d97ada592" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Kalender"/>
          <property name="name" value="\App\Models\Kalender"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="kalender"/>
        </element>
        <element action="add" uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="KalenderItem"/>
          <property name="name" value="\App\Models\KalenderItem"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="kalender_item"/>
        </element>
        <element action="add" uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Kleurcode"/>
          <property name="name" value="\App\Models\Kleurcode"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="kleurcode"/>
        </element>
        <element action="add" uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Laatstenieuw"/>
          <property name="name" value="\App\Models\Laatstenieuw"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="laatstenieuws"/>
        </element>
        <element action="add" uuid="829ce0e0-6079-426f-9097-508acae5d0c8" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Lid"/>
          <property name="name" value="\App\Models\Lid"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="lid"/>
        </element>
        <element action="add" uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Log"/>
          <property name="name" value="\App\Models\Log"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="log"/>
        </element>
        <element action="add" uuid="1712273f-edb8-4272-a266-71bf382ae475" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Login"/>
          <property name="name" value="\App\Models\Login"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="login"/>
        </element>
        <element action="add" uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="MemberMemberfunction"/>
          <property name="name" value="\App\Models\MemberMemberfunction"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="member_memberfunction"/>
        </element>
        <element action="add" uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Memberfunction"/>
          <property name="name" value="\App\Models\Memberfunction"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="memberfunctions"/>
        </element>
        <element action="add" uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Member"/>
          <property name="name" value="\App\Models\Member"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="members"/>
        </element>
        <element action="add" uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Muziekwerk"/>
          <property name="name" value="\App\Models\Muziekwerk"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="muziekwerk"/>
        </element>
        <element action="add" uuid="7ae11b79-658c-4be9-bb30-909d106ed581" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Muziekwerklink"/>
          <property name="name" value="\App\Models\Muziekwerklink"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="muziekwerklink"/>
        </element>
        <element action="add" uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="OauthAccessToken"/>
          <property name="name" value="\App\Models\OauthAccessToken"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_access_tokens"/>
        </element>
        <element action="add" uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="OauthAuthCode"/>
          <property name="name" value="\App\Models\OauthAuthCode"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_auth_codes"/>
        </element>
        <element action="add" uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="OauthClient"/>
          <property name="name" value="\App\Models\OauthClient"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_clients"/>
        </element>
        <element action="add" uuid="8e206929-1d19-46af-b6df-4c0df9b99050" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="OauthPersonalAccessClient"/>
          <property name="name" value="\App\Models\OauthPersonalAccessClient"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_personal_access_clients"/>
        </element>
        <element action="add" uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="OauthRefreshToken"/>
          <property name="name" value="\App\Models\OauthRefreshToken"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="oauth_refresh_tokens"/>
        </element>
        <element action="add" uuid="ab568cc9-259d-4657-9554-972d9374935e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Pagina"/>
          <property name="name" value="\App\Models\Pagina"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="pagina"/>
        </element>
        <element action="add" uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="PasswordReset"/>
          <property name="name" value="\App\Models\PasswordReset"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="password_resets"/>
        </element>
        <element action="add" uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Rekenregel"/>
          <property name="name" value="\App\Models\Rekenregel"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="rekenregel"/>
        </element>
        <element action="add" uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Responsetekst"/>
          <property name="name" value="\App\Models\Responsetekst"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="responsetekst"/>
        </element>
        <element action="add" uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="RoleUser"/>
          <property name="name" value="\App\Models\RoleUser"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="role_user"/>
        </element>
        <element action="add" uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Role"/>
          <property name="name" value="\App\Models\Role"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="roles"/>
        </element>
        <element action="add" uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Setlist"/>
          <property name="name" value="\App\Models\Setlist"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="setlist"/>
        </element>
        <element action="add" uuid="79f657f0-**************-473b038ae0b6" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Songitemfile"/>
          <property name="name" value="\App\Models\Songitemfile"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="songitemfiles"/>
        </element>
        <element action="add" uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Songitem"/>
          <property name="name" value="\App\Models\Songitem"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="songitems"/>
        </element>
        <element action="add" uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Song"/>
          <property name="name" value="\App\Models\Song"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="songs"/>
        </element>
        <element action="add" uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Sponsor"/>
          <property name="name" value="\App\Models\Sponsor"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="sponsor"/>
        </element>
        <element action="add" uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Temperatuur"/>
          <property name="name" value="\App\Models\Temperatuur"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="temperatuur"/>
        </element>
        <element action="add" uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="TemperatuurServer"/>
          <property name="name" value="\App\Models\TemperatuurServer"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="temperatuur_servers"/>
        </element>
        <element action="add" uuid="46682376-f61c-4572-a9e3-168c0e4c9610" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Twitter"/>
          <property name="name" value="\App\Models\Twitter"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="twitter"/>
        </element>
        <element action="add" uuid="441d93ef-93f8-431b-aead-7c85754ed18c" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="User"/>
          <property name="name" value="\App\Models\User"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="users"/>
        </element>
        <element action="add" uuid="a1794938-ae91-4a9c-934c-be9182e9b308" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="VerstuurdeFormulieren"/>
          <property name="name" value="\App\Models\VerstuurdeFormulieren"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="verstuurde_formulieren"/>
        </element>
        <element action="add" uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Visit"/>
          <property name="name" value="\App\Models\Visit"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="visits"/>
        </element>
        <element action="add" uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="VisitsBot"/>
          <property name="name" value="\App\Models\VisitsBot"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="visits_bots"/>
        </element>
        <element action="add" uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Webform"/>
          <property name="name" value="\App\Models\Webform"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="webform"/>
        </element>
        <element action="add" uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Webformveld"/>
          <property name="name" value="\App\Models\Webformveld"/>
          <property name="namespace" value="\App\Models"/>
          <property name="table" value="webformveld"/>
        </element>
        <element action="add" uuid="7bb1b684-8679-409d-90bb-b2ba28fc1431" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="628f99a4-3679-4c58-8af2-eb11a4a858ea" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="7bb1b684-8679-409d-90bb-b2ba28fc1431" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="9183264b-3ac7-445a-b520-e7bc331acd7a" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="628f99a4-3679-4c58-8af2-eb11a4a858ea" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d61f4ba6-11ac-468b-b261-bf7d6363f01c" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="9183264b-3ac7-445a-b520-e7bc331acd7a" type="field">
          <property name="name" value="bedrag"/>
          <property name="required" value="true"/>
          <property name="type" value="float"/>
        </element>
        <element action="add" uuid="c253a5b9-e315-4d13-8452-f42a2f39a03c" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="d61f4ba6-11ac-468b-b261-bf7d6363f01c" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f3dee74f-e5a8-42c0-962f-7bbce1f37551" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="0311b3b2-ce4e-4e50-87d7-06048b1443cf" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="f3dee74f-e5a8-42c0-962f-7bbce1f37551" type="field">
          <property name="name" value="id_responsetekst"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="d4d43aef-2ed8-4f57-a35c-b9c1462b052e" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="0311b3b2-ce4e-4e50-87d7-06048b1443cf" type="field">
          <property name="name" value="id_webformveld"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e919f76f-f7c0-4238-8654-330cc5cb1bf0" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="d4d43aef-2ed8-4f57-a35c-b9c1462b052e" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="326179eb-96c4-48cd-97d2-31ff14f44705" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="e919f76f-f7c0-4238-8654-330cc5cb1bf0" type="field">
          <property name="name" value="constante"/>
          <property name="required" value="true"/>
          <property name="type" value="float"/>
        </element>
        <element action="add" uuid="b855ff7a-1176-4555-9ff7-3ca4baf38782" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="326179eb-96c4-48cd-97d2-31ff14f44705" type="field">
          <property name="name" value="bewerking"/>
          <property name="required" value="true"/>
          <property name="size" value="1"/>
          <property name="type" value="char"/>
        </element>
        <element action="add" uuid="d48e9931-3168-4f5a-aaa7-5af5d8a6fc31" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="524433c6-5087-4df1-9b17-a1bb3bf61301" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="d48e9931-3168-4f5a-aaa7-5af5d8a6fc31" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9c89a6fc-e4aa-47c6-a269-ecc7ef044120" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="524433c6-5087-4df1-9b17-a1bb3bf61301" type="field">
          <property name="name" value="name"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e123f43a-e9e2-4f74-b72a-e331760d3481" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="9c89a6fc-e4aa-47c6-a269-ecc7ef044120" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="52faa212-7c8a-49fe-bd87-e08f7bc3c503" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="e123f43a-e9e2-4f74-b72a-e331760d3481" type="field">
          <property name="name" value="is_public"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="5a99be28-feac-48c1-a8a2-34dcd9622313" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="52faa212-7c8a-49fe-bd87-e08f7bc3c503" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="12303e6b-dccc-49e7-839c-5d17224a6618" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" sibling-uuid="5a99be28-feac-48c1-a8a2-34dcd9622313" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="d9b66b85-e820-4884-9e93-1ebea2c19086" parent-uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="91b5568c-986d-4310-9416-97d1328b85d3" parent-uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" sibling-uuid="d9b66b85-e820-4884-9e93-1ebea2c19086" type="field">
          <property name="name" value="online"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="3ce85e5f-9848-49fe-8564-62e1b04f916f" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="e6fe0054-4cd1-4be4-807f-02c83a232356" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="3ce85e5f-9848-49fe-8564-62e1b04f916f" type="field">
          <property name="name" value="id_pagina"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="68ea8057-fe7f-469f-bf05-4c5ebf7e4bd8" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="e6fe0054-4cd1-4be4-807f-02c83a232356" type="field">
          <property name="name" value="content"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="e9df6346-0a9a-4264-be8a-89c96f014a2d" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="68ea8057-fe7f-469f-bf05-4c5ebf7e4bd8" type="field">
          <property name="name" value="tonen_vanaf_datum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="569443ec-ac17-443c-80d6-915ab1e3c2f2" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="e9df6346-0a9a-4264-be8a-89c96f014a2d" type="field">
          <property name="name" value="tonen_tot_datum"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="cc685353-b5b8-4a64-988c-7861c7ebca27" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="569443ec-ac17-443c-80d6-915ab1e3c2f2" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="bc7b3e12-9f63-46d1-9ed2-63b282be53a3" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="cc685353-b5b8-4a64-988c-7861c7ebca27" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="84c16b8d-9bc3-4cf2-bc5a-4ed61891e5f7" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="bc7b3e12-9f63-46d1-9ed2-63b282be53a3" type="field">
          <property name="name" value="keywords"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d8a72553-f1e1-42f7-94d0-c64647bc3b4f" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="c18a8e55-606d-481b-ac23-0966cc489069" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="d8a72553-f1e1-42f7-94d0-c64647bc3b4f" type="field">
          <property name="name" value="id_content"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="db4cfb77-adcb-408b-b5de-d9486ebd3431" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="c18a8e55-606d-481b-ac23-0966cc489069" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4892631b-30c8-4d65-999b-a851b0cb5727" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="db4cfb77-adcb-408b-b5de-d9486ebd3431" type="field">
          <property name="name" value="content"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="80cb3bd3-0623-4cd7-828e-1d5c444ae89f" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="a2ed8495-de9a-4195-a948-524c9d11c6f2" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="80cb3bd3-0623-4cd7-828e-1d5c444ae89f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="id_lid"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="0328fa1e-39ef-4e56-ba28-b77a7b7cb437" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="a2ed8495-de9a-4195-a948-524c9d11c6f2" type="field">
          <property name="default" value="0"/>
          <property name="name" value="id_contributieperiode"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="928f69d1-0ee8-4ee4-a363-5a8b4935a19b" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="0328fa1e-39ef-4e56-ba28-b77a7b7cb437" type="field">
          <property name="name" value="datum_betaald"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="a4d81cca-4d7e-417e-9a7c-994cd4dd8136" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="928f69d1-0ee8-4ee4-a363-5a8b4935a19b" type="field">
          <property name="name" value="bedrag"/>
          <property name="required" value="true"/>
          <property name="type" value="float"/>
        </element>
        <element action="add" uuid="a94f405c-8cbe-42d7-8490-b13878e7b479" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="a4d81cca-4d7e-417e-9a7c-994cd4dd8136" type="field">
          <property name="name" value="toegepaste_rekenregel"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ebbe3748-b246-4a22-a5eb-6babe3f737b9" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="f80cc17d-ae05-4d52-8f3a-9cc33b38cab3" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="ebbe3748-b246-4a22-a5eb-6babe3f737b9" type="field">
          <property name="name" value="id_domein"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e63f890d-044e-475e-87b4-2de79e6aaca0" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="f80cc17d-ae05-4d52-8f3a-9cc33b38cab3" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="35"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7e3e564b-6644-4f8e-b3e5-b638e301a88f" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="e63f890d-044e-475e-87b4-2de79e6aaca0" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="datum_berekend"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4bff53f1-2e43-48d7-881c-953aa139697e" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="7e3e564b-6644-4f8e-b3e5-b638e301a88f" type="field">
          <property name="default" value="0000-00-00 00:00:00"/>
          <property name="name" value="datum_goedgekeurd"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="42a0afd0-6349-4141-950f-011b702d2377" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="4bff53f1-2e43-48d7-881c-953aa139697e" type="field">
          <property name="name" value="goedgekeurd_door"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="608b3579-fd41-4627-9e5f-aa67b5212358" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="42a0afd0-6349-4141-950f-011b702d2377" type="field">
          <property name="default" value="0000-00-00"/>
          <property name="name" value="datum_verstuurd"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="5492b961-60ff-44fe-a212-109e01b4642b" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="104f29a4-46b6-404f-a29f-336d8a718107" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="5492b961-60ff-44fe-a212-109e01b4642b" type="field">
          <property name="name" value="datum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="84a8841d-45ec-4e40-8b9f-51140b8520b8" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="104f29a4-46b6-404f-a29f-336d8a718107" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5b4aeb56-4b34-4fb2-9079-49114e65a893" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="84a8841d-45ec-4e40-8b9f-51140b8520b8" type="field">
          <property name="name" value="filename"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="04e18988-b772-4288-a51a-3976ef24e172" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="5b4aeb56-4b34-4fb2-9079-49114e65a893" type="field">
          <property name="name" value="tags"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b4296a32-ce2a-47c6-b9dd-e62c48364812" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="04e18988-b772-4288-a51a-3976ef24e172" type="field">
          <property name="name" value="doctype"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6774f50c-96a7-460a-a075-4791b970afcd" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="b4296a32-ce2a-47c6-b9dd-e62c48364812" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="cf2cc489-8758-4270-b5b0-95b2acd8b450" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="6774f50c-96a7-460a-a075-4791b970afcd" type="field">
          <property name="name" value="omschrijving"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="741c8d44-f7b8-4901-8681-e3ff68c69e64" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="5fa43199-09bd-48b4-bc7f-e5f13d85cec0" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="741c8d44-f7b8-4901-8681-e3ff68c69e64" type="field">
          <property name="name" value="domaintype_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="471007d8-710e-4750-aa56-93bac3fd01e8" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="5fa43199-09bd-48b4-bc7f-e5f13d85cec0" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7720f564-9cfa-4e5f-9fe4-0681edfb14b2" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="471007d8-710e-4750-aa56-93bac3fd01e8" type="field">
          <property name="name" value="url"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b4f9e998-8e9b-4fc1-97ae-682d898bace3" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="7720f564-9cfa-4e5f-9fe4-0681edfb14b2" type="field">
          <property name="name" value="lookup_url"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="6a93c0d5-e4c5-45b6-81cb-b913c3d81c65" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="b4f9e998-8e9b-4fc1-97ae-682d898bace3" type="field">
          <property name="name" value="lookup_directory"/>
          <property name="required" value="true"/>
          <property name="size" value="20"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c766eb66-09b5-4dd3-ba86-157891b7997f" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="6a93c0d5-e4c5-45b6-81cb-b913c3d81c65" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_admin"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a29c5ff4-4f93-4fb0-bde5-cd2675379495" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="c766eb66-09b5-4dd3-ba86-157891b7997f" type="field">
          <property name="name" value="mod_repertoire"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f3bd606b-0e93-4a87-94f6-1b3028ad3d23" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="a29c5ff4-4f93-4fb0-bde5-cd2675379495" type="field">
          <property name="name" value="mod_members"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fb91d9e2-3dd3-4c85-9f9a-fd2b6c7ee6e8" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="f3bd606b-0e93-4a87-94f6-1b3028ad3d23" type="field">
          <property name="name" value="mod_sponsors"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ddbfbd2f-501d-48b7-9ae5-5723939fab82" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="fb91d9e2-3dd3-4c85-9f9a-fd2b6c7ee6e8" type="field">
          <property name="name" value="mod_setlist"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="fa68ef39-6be9-4f03-af9b-22a429515400" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="ddbfbd2f-501d-48b7-9ae5-5723939fab82" type="field">
          <property name="name" value="mod_membershipfee"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7e8e58a3-f02f-4602-a487-078a4e92d709" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="fa68ef39-6be9-4f03-af9b-22a429515400" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_calendar"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="b8a5dc83-**************-5b3c1b43eff3" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="7e8e58a3-f02f-4602-a487-078a4e92d709" type="field">
          <property name="default" value="nl"/>
          <property name="name" value="default_language"/>
          <property name="required" value="true"/>
          <property name="size" value="2"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7b6f2592-4632-4f9c-8ca7-5458e20d1aa1" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="b8a5dc83-**************-5b3c1b43eff3" type="field">
          <property name="name" value="default_passwd"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="46d65fec-4359-49af-ac49-4cf6ebbb9b90" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="7b6f2592-4632-4f9c-8ca7-5458e20d1aa1" type="field">
          <property name="name" value="email_name"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f0cc8c66-5164-4f39-b678-8c5958751869" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="46d65fec-4359-49af-ac49-4cf6ebbb9b90" type="field">
          <property name="name" value="email_addressline1"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="da4db680-802f-407d-9468-e9a9303785ed" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="f0cc8c66-5164-4f39-b678-8c5958751869" type="field">
          <property name="name" value="email_addressline2"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="58675c87-9b4b-4efb-9ca1-d98f42ace903" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="da4db680-802f-407d-9468-e9a9303785ed" type="field">
          <property name="name" value="email_zip"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d31303e4-85fc-4e82-9862-82a556c6e1f0" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="58675c87-9b4b-4efb-9ca1-d98f42ace903" type="field">
          <property name="name" value="email_city"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fe328f5a-7e79-4d30-ad6f-7d97933ecf6c" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="d31303e4-85fc-4e82-9862-82a556c6e1f0" type="field">
          <property name="name" value="email_telephone"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="73fb5f78-ac6f-4013-9a64-492c443a9545" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="fe328f5a-7e79-4d30-ad6f-7d97933ecf6c" type="field">
          <property name="name" value="email_email_from"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ce378e1c-dc57-4795-980f-ab7f90deacbf" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="73fb5f78-ac6f-4013-9a64-492c443a9545" type="field">
          <property name="name" value="email_logo"/>
          <property name="size" value="120"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6c22b62f-b310-4aa9-9b0c-111d97c6536a" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="ce378e1c-dc57-4795-980f-ab7f90deacbf" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="766ae6d2-372d-479c-bbd6-771b1713b136" parent-uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" sibling-uuid="6c22b62f-b310-4aa9-9b0c-111d97c6536a" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a8eb6b6b-e0dd-496b-911a-1be1f75b74b4" parent-uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7541f808-520a-43b4-b371-215db0276318" parent-uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" sibling-uuid="a8eb6b6b-e0dd-496b-911a-1be1f75b74b4" type="field">
          <property name="name" value="description"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a9d8be40-2168-495b-8434-4b2a68c2803f" parent-uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" sibling-uuid="7541f808-520a-43b4-b371-215db0276318" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a6542ef2-934c-4463-a955-938c2594d859" parent-uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" sibling-uuid="a9d8be40-2168-495b-8434-4b2a68c2803f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="7940edcc-683c-4c4f-86dc-6f20509ef9ce" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="de1449fc-d4d0-4621-a221-f46feb331a91" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="7940edcc-683c-4c4f-86dc-6f20509ef9ce" type="field">
          <property name="default" value="0"/>
          <property name="name" value="id_domeintype"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="50b49183-fc8d-4a5f-ba18-7f604db19f23" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="de1449fc-d4d0-4621-a221-f46feb331a91" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0c6e4be8-ec32-4aca-997d-fa149cef7943" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="50b49183-fc8d-4a5f-ba18-7f604db19f23" type="field">
          <property name="name" value="url"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ccf39bbd-1b44-4762-bea6-c4c6fd7fb7d4" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="0c6e4be8-ec32-4aca-997d-fa149cef7943" type="field">
          <property name="name" value="lookup_url"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="5f7e7927-c385-4d5f-bd7d-311ddf424baf" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ccf39bbd-1b44-4762-bea6-c4c6fd7fb7d4" type="field">
          <property name="name" value="lookup_map"/>
          <property name="required" value="true"/>
          <property name="size" value="20"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="19d6ce20-c9ec-4e07-b673-3121dbcf2667" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="5f7e7927-c385-4d5f-bd7d-311ddf424baf" type="field">
          <property name="name" value="cp_naam"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="33f1f07f-9325-4ee0-a5c6-09b73cb46cdb" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="19d6ce20-c9ec-4e07-b673-3121dbcf2667" type="field">
          <property name="name" value="cp_email"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6fbe3f00-3b9f-4601-9311-d0ae4af81236" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="33f1f07f-9325-4ee0-a5c6-09b73cb46cdb" type="field">
          <property name="name" value="cp_telefoon"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8c352d49-cba7-464d-9a1b-f5877b1bd51f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="6fbe3f00-3b9f-4601-9311-d0ae4af81236" type="field">
          <property name="name" value="std_email"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="bebd4fe5-ac36-4443-94fe-acab3c348bdb" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8c352d49-cba7-464d-9a1b-f5877b1bd51f" type="field">
          <property name="name" value="std_naam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e3333b36-fe2c-4eed-a167-b56c1d1f045b" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bebd4fe5-ac36-4443-94fe-acab3c348bdb" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_pagina"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="8581eec8-ae4d-40b0-9ecd-5231d764b538" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="e3333b36-fe2c-4eed-a167-b56c1d1f045b" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_webform"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="a7606f18-a34c-47cb-a313-ef783da8273f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8581eec8-ae4d-40b0-9ecd-5231d764b538" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_fotoboek"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="fd52fae7-8f5b-42cb-b76f-b349c840b30d" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="a7606f18-a34c-47cb-a313-ef783da8273f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_kalender"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="82ecfa60-2cba-4281-b444-6f64d64ca914" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="fd52fae7-8f5b-42cb-b76f-b349c840b30d" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_repertoire"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="17a2c52b-3b0d-4b90-85e3-191072bb75c1" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="82ecfa60-2cba-4281-b444-6f64d64ca914" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_leden"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="c546897a-ce94-481b-9d4e-7c50c23b5eb0" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="17a2c52b-3b0d-4b90-85e3-191072bb75c1" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_sponsoren"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="07f0ab2c-961f-48b8-a774-8652871472b2" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="c546897a-ce94-481b-9d4e-7c50c23b5eb0" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_documenten"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="81dbf7b6-01a6-4f26-b845-e018a8ed2b66" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="07f0ab2c-961f-48b8-a774-8652871472b2" type="field">
          <property name="default" value="0"/>
          <property name="name" value="mod_contributie"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="c1f25a1a-666f-4c57-a3ec-2fdbf314f810" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="81dbf7b6-01a6-4f26-b845-e018a8ed2b66" type="field">
          <property name="name" value="mod_setlist"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="cd451d81-6498-4039-8d3b-e5ce440c487c" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="c1f25a1a-666f-4c57-a3ec-2fdbf314f810" type="field">
          <property name="default" value="0"/>
          <property name="name" value="online"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="3380eca0-59d2-4fe7-8e5c-28c63224507f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="cd451d81-6498-4039-8d3b-e5ce440c487c" type="field">
          <property name="name" value="offline_tekst"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="ea1d551a-6564-41ee-ace1-e00fa18022cd" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="3380eca0-59d2-4fe7-8e5c-28c63224507f" type="field">
          <property name="name" value="notes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="e25d41f4-7d02-4d7c-afeb-76c9c99bd72b" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ea1d551a-6564-41ee-ace1-e00fa18022cd" type="field">
          <property name="default" value="0"/>
          <property name="name" value="useshorturl"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="07ad5a90-8b47-4804-a6ea-662401dc99a9" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="e25d41f4-7d02-4d7c-afeb-76c9c99bd72b" type="field">
          <property name="default" value="nl"/>
          <property name="name" value="basistaal"/>
          <property name="required" value="true"/>
          <property name="size" value="3"/>
          <property name="type" value="char"/>
        </element>
        <element action="add" uuid="fb1f4e7e-4605-48f4-bc50-8508efadba03" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="07ad5a90-8b47-4804-a6ea-662401dc99a9" type="field">
          <property name="name" value="related_domains"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="90636ac7-170e-4c54-bfdf-826c103d62d7" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="fb1f4e7e-4605-48f4-bc50-8508efadba03" type="field">
          <property name="default" value="0"/>
          <property name="name" value="heeft_profielpagina"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="ec3af3bb-e422-46cc-866b-72c0fe1f2ed8" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="90636ac7-170e-4c54-bfdf-826c103d62d7" type="field">
          <property name="name" value="GA_ID"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="d6ea16d3-803a-4ef3-a4b2-b93c713f26c8" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ec3af3bb-e422-46cc-866b-72c0fe1f2ed8" type="field">
          <property name="name" value="std_homepath"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d41ff824-64b2-41b7-8c90-9fe1f93ef813" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="d6ea16d3-803a-4ef3-a4b2-b93c713f26c8" type="field">
          <property name="name" value="yamlversion"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="bd024f07-15a9-442d-a1a1-3231c50d2bce" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="d41ff824-64b2-41b7-8c90-9fe1f93ef813" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="bce2b4c4-7d9c-417f-84e7-a59842f34b88" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bd024f07-15a9-442d-a1a1-3231c50d2bce" type="field">
          <property name="name" value="keywords"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8d6c0c2f-0ac4-41ab-a353-053f9c9dcb63" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bce2b4c4-7d9c-417f-84e7-a59842f34b88" type="field">
          <property name="default" value="jaar"/>
          <property name="name" value="betaalfreq"/>
          <property name="required" value="true"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3cd6617a-e163-4662-8c09-83349ce56511" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8d6c0c2f-0ac4-41ab-a353-053f9c9dcb63" type="field">
          <property name="name" value="contributie_introtekst"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="6b0edb54-6b3e-4478-a729-c5c4c53bdc06" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="3cd6617a-e163-4662-8c09-83349ce56511" type="field">
          <property name="name" value="contributie_slottekst"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="54bb3383-60e9-421a-9a0c-d511657de874" parent-uuid="454be441-0c95-4305-b051-390e970926c3" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="4ba25a1e-cc63-4ce8-a53d-0ba8ea494efc" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="54bb3383-60e9-421a-9a0c-d511657de874" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="c3d3ffca-0e15-4221-8d5b-838df495db64" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="4ba25a1e-cc63-4ce8-a53d-0ba8ea494efc" type="field">
          <property name="name" value="id_rekenregel"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="6f481e01-44f5-47f9-99f8-29a299a107ed" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="c3d3ffca-0e15-4221-8d5b-838df495db64" type="field">
          <property name="name" value="id_basisbedrag"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="0702ed73-a688-4d89-961f-bf9ffcb1c65b" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="6f481e01-44f5-47f9-99f8-29a299a107ed" type="field">
          <property name="name" value="operand"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="011b8f1f-fd97-4323-91be-68dd6781ab64" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="0702ed73-a688-4d89-961f-bf9ffcb1c65b" type="field">
          <property name="name" value="factor"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="b4399f62-8c1b-4860-b3d2-991a80bd673f" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="011b8f1f-fd97-4323-91be-68dd6781ab64" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="md"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="9b124997-6e1c-4ed4-bb3e-94829f2a45e5" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="b4399f62-8c1b-4860-b3d2-991a80bd673f" type="field">
          <property name="name" value="mu"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b85bc84a-e0a3-41ed-8967-b9b6e46f76f1" parent-uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="f636dd97-ccbe-485b-893a-9808fadb6415" parent-uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" sibling-uuid="b85bc84a-e0a3-41ed-8967-b9b6e46f76f1" type="field">
          <property name="name" value="omschrijving"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="694bc95c-d288-43e2-a81e-b08ac0c36ecb" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="1ce1e2af-0a0a-4cd4-b6ae-3dc352a56980" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="694bc95c-d288-43e2-a81e-b08ac0c36ecb" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="93a4671e-69ac-4102-8878-cabea1d3d277" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="1ce1e2af-0a0a-4cd4-b6ae-3dc352a56980" type="field">
          <property name="name" value="sender"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="496706ae-b3ff-4b33-a450-6029ac7d690a" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="93a4671e-69ac-4102-8878-cabea1d3d277" type="field">
          <property name="name" value="recepient"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="822b7121-1d86-40f5-bda2-5d5280254712" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="496706ae-b3ff-4b33-a450-6029ac7d690a" type="field">
          <property name="name" value="carboncopy"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4510b25b-6ed1-49b4-9ee8-769654d181db" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="822b7121-1d86-40f5-bda2-5d5280254712" type="field">
          <property name="name" value="blindcarboncopy"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c377e116-f3ed-4492-bc8e-6258222d9bed" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="4510b25b-6ed1-49b4-9ee8-769654d181db" type="field">
          <property name="name" value="subject"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="87867dc5-1bd8-4afa-b949-1002c86a3aa6" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="c377e116-f3ed-4492-bc8e-6258222d9bed" type="field">
          <property name="name" value="body"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="62079b9b-0e75-466d-ba1d-9678ec70a3b0" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="87867dc5-1bd8-4afa-b949-1002c86a3aa6" type="field">
          <property name="name" value="attachment"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b33497df-cec0-46bd-bd80-aef12ab8de8b" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="62079b9b-0e75-466d-ba1d-9678ec70a3b0" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="datum_aangemaakt"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="88454642-12dc-4a11-9020-9dcd0940db1a" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="b33497df-cec0-46bd-bd80-aef12ab8de8b" type="field">
          <property name="name" value="datum_verzonden"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="17785a1b-92b8-493e-8302-a94460cbc48e" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1cfad8d3-1b1b-42cd-82c6-299e4035b9c4" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="17785a1b-92b8-493e-8302-a94460cbc48e" type="field">
          <property name="name" value="calendar_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ef8afd7e-ffc9-4f06-84e3-4c29d7596e56" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="1cfad8d3-1b1b-42cd-82c6-299e4035b9c4" type="field">
          <property name="name" value="title"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="98ea17f1-d69a-461b-a681-2912dff5a60a" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="ef8afd7e-ffc9-4f06-84e3-4c29d7596e56" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="88843e88-af55-4888-b117-1a110e55c87b" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="98ea17f1-d69a-461b-a681-2912dff5a60a" type="field">
          <property name="name" value="start_datetime"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="842675c2-0e96-423d-972d-53c18500a2d1" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="88843e88-af55-4888-b117-1a110e55c87b" type="field">
          <property name="name" value="duration"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="9e2a9f0d-b76e-473a-98a9-c20a2d2182e7" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="842675c2-0e96-423d-972d-53c18500a2d1" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e467ec47-0a87-42ce-9444-34b667acf08c" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="9e2a9f0d-b76e-473a-98a9-c20a2d2182e7" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="eedbe303-09eb-4dcd-a9cb-6b975793522a" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="61acbcf3-0661-4ff7-8581-3d178e188c6d" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" sibling-uuid="eedbe303-09eb-4dcd-a9cb-6b975793522a" type="field">
          <property name="name" value="connection"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="0a1bf19b-2f07-4e62-b343-51b5c15075a7" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" sibling-uuid="61acbcf3-0661-4ff7-8581-3d178e188c6d" type="field">
          <property name="name" value="queue"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="e5ba9a1e-49da-431a-a7cd-66a01d734093" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" sibling-uuid="0a1bf19b-2f07-4e62-b343-51b5c15075a7" type="field">
          <property name="name" value="payload"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="90dc3d54-8a5e-42c9-a243-ee394fd385df" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" sibling-uuid="e5ba9a1e-49da-431a-a7cd-66a01d734093" type="field">
          <property name="name" value="exception"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="5aca1050-93aa-4520-bec2-af226b272386" parent-uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" sibling-uuid="90dc3d54-8a5e-42c9-a243-ee394fd385df" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="failed_at"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ee0a8553-2b7e-4a58-adf4-411d3440ecd9" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="7cd91459-4dc1-49a0-9a0b-badf80f1c921" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="ee0a8553-2b7e-4a58-adf4-411d3440ecd9" type="field">
          <property name="name" value="id_fotoverzameling"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="ba00be97-4268-41f9-af70-5aa0e7f69ca8" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="7cd91459-4dc1-49a0-9a0b-badf80f1c921" type="field">
          <property name="name" value="source"/>
          <property name="required" value="true"/>
          <property name="size" value="35"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c28f6f3a-f16d-40a5-8487-f0d93ac29b6b" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="ba00be97-4268-41f9-af70-5aa0e7f69ca8" type="field">
          <property name="name" value="caption"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="aea1d2c2-97b0-478a-8ed4-e5197af6fdd0" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="0d1f8f2b-55ed-4f78-9bca-5c97a36cfdab" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="aea1d2c2-97b0-478a-8ed4-e5197af6fdd0" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="8997d9c4-6df4-4fbd-9212-70dc163edd7f" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="0d1f8f2b-55ed-4f78-9bca-5c97a36cfdab" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d1b90088-9ec0-449c-ae7a-3b881fb19f61" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="8997d9c4-6df4-4fbd-9212-70dc163edd7f" type="field">
          <property name="name" value="foto_volgorde"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="f3124eb2-d4d7-4a1d-bb52-39efb6feb67c" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="d1b90088-9ec0-449c-ae7a-3b881fb19f61" type="field">
          <property name="name" value="datum"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="1ae2624e-8a4f-486e-87a1-efa211373a36" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="52a94217-3251-4ace-a9e4-ddda9cdf3792" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="1ae2624e-8a4f-486e-87a1-efa211373a36" type="field">
          <property name="name" value="omschrijving"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="72057204-4d8c-4772-bb78-50bd38aca301" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="52a94217-3251-4ace-a9e4-ddda9cdf3792" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="1d8ac20d-6253-4833-ba11-cfdd2fd03cfc" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="72057204-4d8c-4772-bb78-50bd38aca301" type="field">
          <property name="default" value="0"/>
          <property name="name" value="volgnummer"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="09585620-d9f5-4751-802e-b60da0ebaeca" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="a458dd59-049b-46d8-9915-28e00b27bdae" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="09585620-d9f5-4751-802e-b60da0ebaeca" type="field">
          <property name="name" value="opdatum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="0fb1dff5-9a42-43e3-aba4-c939148b6425" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="a458dd59-049b-46d8-9915-28e00b27bdae" type="field">
          <property name="name" value="id_kalenderitem"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e39f954d-c47a-4784-9cd3-840e2ce9aa3f" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="0fb1dff5-9a42-43e3-aba4-c939148b6425" type="field">
          <property name="name" value="incidenteel_niet"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="e3ef5648-6d32-42c9-8636-c61f8e1759fb" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="e39f954d-c47a-4784-9cd3-840e2ce9aa3f" type="field">
          <property name="name" value="extra_opmerking"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="657eff54-80b6-4bf7-96c4-3dfa869a7bcf" parent-uuid="3e835401-9d64-4134-ac69-80d9791dcd96" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="7d088907-19d7-4d9f-b026-e531c904dc2c" parent-uuid="3e835401-9d64-4134-ac69-80d9791dcd96" sibling-uuid="657eff54-80b6-4bf7-96c4-3dfa869a7bcf" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f35be065-99a0-4a7b-bab6-6c53e808a99a" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cd2e9923-3422-469b-b3e3-b353fa214641" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="f35be065-99a0-4a7b-bab6-6c53e808a99a" type="field">
          <property name="name" value="queue"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b5f23daa-d4ff-47c2-83e8-cb9440fec1e8" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="cd2e9923-3422-469b-b3e3-b353fa214641" type="field">
          <property name="name" value="payload"/>
          <property name="required" value="true"/>
          <property name="type" value="longText"/>
        </element>
        <element action="add" uuid="00d83886-a588-41c5-bbef-bdc6535b85cf" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="b5f23daa-d4ff-47c2-83e8-cb9440fec1e8" type="field">
          <property name="name" value="attempts"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2593bfc3-bbc0-4731-876c-e89b5ef40d9c" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="00d83886-a588-41c5-bbef-bdc6535b85cf" type="field">
          <property name="name" value="reserved_at"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ad12febf-8912-4114-b430-de68465a2bbd" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="2593bfc3-bbc0-4731-876c-e89b5ef40d9c" type="field">
          <property name="name" value="available_at"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7b04231e-0fd3-4c95-ab18-716d1f8312ea" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" sibling-uuid="ad12febf-8912-4114-b430-de68465a2bbd" type="field">
          <property name="name" value="created_at"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="00465a63-e2e3-48dd-b539-fd82d84b7332" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="b34ad2e0-a3f6-4182-88e8-96139db3e1ae" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" sibling-uuid="00465a63-e2e3-48dd-b539-fd82d84b7332" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="6365d17e-b0d2-46fa-90c9-7f2be9b71614" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" sibling-uuid="b34ad2e0-a3f6-4182-88e8-96139db3e1ae" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="22a30273-5e14-45fe-81a2-bbc15477073d" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="7758d7d6-7b6d-4de1-adb6-82ee0e067adb" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="22a30273-5e14-45fe-81a2-bbc15477073d" type="field">
          <property name="name" value="datumtijd"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="0661bbed-8ee7-4809-8748-36c50ccede55" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="7758d7d6-7b6d-4de1-adb6-82ee0e067adb" type="field">
          <property name="name" value="id_kalender"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="06e16532-734d-4aac-9900-b6ba49fec65e" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="0661bbed-8ee7-4809-8748-36c50ccede55" type="field">
          <property name="name" value="omschrijving"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="96536e9d-a2e7-4c1f-bcb5-bb6b8fd28b9d" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="06e16532-734d-4aac-9900-b6ba49fec65e" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="59be2e1e-5e16-4988-8eeb-f01265dc87f6" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="96536e9d-a2e7-4c1f-bcb5-bb6b8fd28b9d" type="field">
          <property name="name" value="eindtijd"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="353cb2ed-9a22-4bba-bbe3-a41972eca2a1" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="59be2e1e-5e16-4988-8eeb-f01265dc87f6" type="field">
          <property name="name" value="id_kleurcode"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="6a53f38b-4032-4877-b1aa-0d77448a4e26" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="353cb2ed-9a22-4bba-bbe3-a41972eca2a1" type="field">
          <property name="name" value="id_herhalingtype"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e6521f9c-dcdf-4d68-8d8c-5144228814ff" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="6a53f38b-4032-4877-b1aa-0d77448a4e26" type="field">
          <property name="name" value="herhaling_einddatum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="2c573faf-4fe6-411a-bf78-49bf79aefb4c" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="0a7c407b-668d-4700-b04c-c227876b6b48" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="2c573faf-4fe6-411a-bf78-49bf79aefb4c" type="field">
          <property name="name" value="kleur_html"/>
          <property name="required" value="true"/>
          <property name="size" value="7"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2f27dfff-f480-43e6-b2f3-9f260a4b369a" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="0a7c407b-668d-4700-b04c-c227876b6b48" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="401fb08e-edd2-477b-96f5-de2fa1bea961" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="2f27dfff-f480-43e6-b2f3-9f260a4b369a" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="50b73174-94fc-4ebd-9962-9d8f7c932136" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="401fb08e-edd2-477b-96f5-de2fa1bea961" type="field">
          <property name="default" value="0"/>
          <property name="name" value="volgnummer"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="8819b19b-de40-4fe3-a60d-5a4f9275fda4" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="50b73174-94fc-4ebd-9962-9d8f7c932136" type="field">
          <property name="default" value="0"/>
          <property name="name" value="inevenementenkalender"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="463b6889-26b8-44f0-823e-f287b4ad11a9" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="d7b61f35-0a6c-4572-8d25-4fc8aca0cb37" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="463b6889-26b8-44f0-823e-f287b4ad11a9" type="field">
          <property name="name" value="datum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="78a98c94-45d4-4fb2-834f-5e502959093b" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="d7b61f35-0a6c-4572-8d25-4fc8aca0cb37" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7df816f7-f569-461b-bbfc-a16bc48232e9" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="78a98c94-45d4-4fb2-834f-5e502959093b" type="field">
          <property name="name" value="bericht"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="5b84f8ac-bba2-49de-a137-efbbf1dd3751" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="dc2a4aba-51e7-4273-a40e-dc7d273f398e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="5b84f8ac-bba2-49de-a137-efbbf1dd3751" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="33d12be4-44d7-4eb5-92a3-66b2c6961bde" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="dc2a4aba-51e7-4273-a40e-dc7d273f398e" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1271a38e-b93a-4117-afb9-81b297a7da28" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="33d12be4-44d7-4eb5-92a3-66b2c6961bde" type="field">
          <property name="name" value="adres"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="286676b3-9782-449c-90ea-68b93e1bd22e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="1271a38e-b93a-4117-afb9-81b297a7da28" type="field">
          <property name="name" value="straatnaam"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="14e31992-07f3-4fe3-a4f9-03718087074e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="286676b3-9782-449c-90ea-68b93e1bd22e" type="field">
          <property name="name" value="huisnummer"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="63d9e738-46e3-461d-a40e-496e805458c2" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="14e31992-07f3-4fe3-a4f9-03718087074e" type="field">
          <property name="name" value="postcode"/>
          <property name="size" value="7"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b755137a-5d46-41ea-8682-cb3317ec6673" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="63d9e738-46e3-461d-a40e-496e805458c2" type="field">
          <property name="name" value="woonplaats"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="36911513-e69f-4d6d-96a3-fffbb304f5b1" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="b755137a-5d46-41ea-8682-cb3317ec6673" type="field">
          <property name="name" value="verjaardag"/>
          <property name="size" value="5"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0cc44858-c422-445f-b899-bbb962c65020" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="36911513-e69f-4d6d-96a3-fffbb304f5b1" type="field">
          <property name="name" value="geboortejaar"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="0552d56f-ff13-4142-ab21-a6e423512a68" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0cc44858-c422-445f-b899-bbb962c65020" type="field">
          <property name="name" value="telefoon1"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="cdb3c559-ae3c-4070-b2b1-56b7d52a1795" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0552d56f-ff13-4142-ab21-a6e423512a68" type="field">
          <property name="name" value="telefoon2"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="81de3cfc-5fd0-4d6d-bfe2-cd9f7aa6ecb2" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="cdb3c559-ae3c-4070-b2b1-56b7d52a1795" type="field">
          <property name="name" value="email"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6be3df92-eaa3-49d8-8230-46f528a2977a" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="81de3cfc-5fd0-4d6d-bfe2-cd9f7aa6ecb2" type="field">
          <property name="name" value="id_functie"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="4f23e376-395a-4360-b78b-1fc19cabebad" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="6be3df92-eaa3-49d8-8230-46f528a2977a" type="field">
          <property name="name" value="lid_sinds"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="92888e7f-16e2-42fb-9e84-941f6e2ef3d0" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="4f23e376-395a-4360-b78b-1fc19cabebad" type="field">
          <property name="name" value="einde_lidmaatschap"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="488ccc61-c0f4-44da-b35f-2c26ac91a3cd" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="92888e7f-16e2-42fb-9e84-941f6e2ef3d0" type="field">
          <property name="name" value="pasfoto"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="bb1ce1df-63b6-45d4-a73e-f77a5d61aea3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="488ccc61-c0f4-44da-b35f-2c26ac91a3cd" type="field">
          <property name="name" value="beschrijving"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="32dbc17d-1eb0-43bd-8571-7f64b203a2a0" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="bb1ce1df-63b6-45d4-a73e-f77a5d61aea3" type="field">
          <property name="name" value="opmerking"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="f8252c86-9b02-4336-8ebc-01972d43cebf" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="32dbc17d-1eb0-43bd-8571-7f64b203a2a0" type="field">
          <property name="name" value="login_naam"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="902de522-a0be-4457-b3e0-803e94a3e7b4" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="f8252c86-9b02-4336-8ebc-01972d43cebf" type="field">
          <property name="name" value="password"/>
          <property name="size" value="35"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2467a5c8-a8e2-4a9d-baac-6a8501d74812" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="902de522-a0be-4457-b3e0-803e94a3e7b4" type="field">
          <property name="name" value="login_hash"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="eab3a2fd-c787-4992-b3f5-6150493f02bd" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="2467a5c8-a8e2-4a9d-baac-6a8501d74812" type="field">
          <property name="name" value="login_hash_ip"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0b09e87d-62ff-4b8e-a175-fc4dd7bd5fb3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="eab3a2fd-c787-4992-b3f5-6150493f02bd" type="field">
          <property name="name" value="login_hash_expire"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="f1b6b41d-9089-4d17-a2a8-d292939c9d8e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0b09e87d-62ff-4b8e-a175-fc4dd7bd5fb3" type="field">
          <property name="name" value="id_functie2"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="24fe7619-b59d-410b-9de0-8c8bf2f20d6c" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="f1b6b41d-9089-4d17-a2a8-d292939c9d8e" type="field">
          <property name="name" value="id_functie3"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="69486b7b-b9a6-4fbf-be0c-9877ab403e3a" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="24fe7619-b59d-410b-9de0-8c8bf2f20d6c" type="field">
          <property name="name" value="anaam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c3c9c2e1-e2a5-480e-80d7-1cdb0823e9a3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="69486b7b-b9a6-4fbf-be0c-9877ab403e3a" type="field">
          <property name="name" value="tvnaam"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0e01c6c0-d87b-49a3-81e2-c4a7bbfd1de3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="c3c9c2e1-e2a5-480e-80d7-1cdb0823e9a3" type="field">
          <property name="name" value="vnaam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="973d7774-21ec-4ff5-bd98-789c2b90ae64" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0e01c6c0-d87b-49a3-81e2-c4a7bbfd1de3" type="field">
          <property name="default" value="0"/>
          <property name="name" value="extern_contact"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="3aff6f14-8a42-43fe-80d3-63c3a0829e46" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="973d7774-21ec-4ff5-bd98-789c2b90ae64" type="field">
          <property name="default" value="0"/>
          <property name="name" value="suspend"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="f0a3d21e-6f1a-4f76-a6cf-ebc3a5553e03" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="3aff6f14-8a42-43fe-80d3-63c3a0829e46" type="field">
          <property name="default" value="0"/>
          <property name="name" value="is_erelid"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="caad7105-10bc-4333-993d-6eefddaf68a1" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="8010da92-d54b-4297-9874-e8bf12c7cb1e" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="caad7105-10bc-4333-993d-6eefddaf68a1" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b8be86a6-f803-4c7d-8756-208f68ba0cd3" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="8010da92-d54b-4297-9874-e8bf12c7cb1e" type="field">
          <property name="name" value="entrytype"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1126ccd2-91c2-4925-8162-8a8f0eecb9e7" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="b8be86a6-f803-4c7d-8756-208f68ba0cd3" type="field">
          <property name="name" value="waarde"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="180b9aea-4aa2-404a-b299-141ef1f5b89f" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="1126ccd2-91c2-4925-8162-8a8f0eecb9e7" type="field">
          <property name="default" value="0"/>
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="75bbabeb-943d-4644-9b54-1783bc354aec" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="180b9aea-4aa2-404a-b299-141ef1f5b89f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="ip-address"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0151e606-390e-4919-9eb5-ad31163d2e77" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="a608d944-19c1-4b9d-8620-e76a80b61470" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="0151e606-390e-4919-9eb5-ad31163d2e77" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="fba6c013-6bab-4a35-98fe-56225eec1e05" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="a608d944-19c1-4b9d-8620-e76a80b61470" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="25d4041f-1249-47f5-b306-09dbd822365f" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="fba6c013-6bab-4a35-98fe-56225eec1e05" type="field">
          <property name="name" value="telefoon"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="dd9da9de-bf7e-49f4-9844-99286bb250f1" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="25d4041f-1249-47f5-b306-09dbd822365f" type="field">
          <property name="name" value="email"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ba6077d4-b15d-4b4d-9158-ec6db9e2e44e" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="dd9da9de-bf7e-49f4-9844-99286bb250f1" type="field">
          <property name="name" value="login_naam"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ba888559-82f0-4b12-842c-0580fdc448a2" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="ba6077d4-b15d-4b4d-9158-ec6db9e2e44e" type="field">
          <property name="name" value="password"/>
          <property name="required" value="true"/>
          <property name="size" value="35"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="52ef3e6c-96a3-4b6e-b7c1-6e12b458838d" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="ba888559-82f0-4b12-842c-0580fdc448a2" type="field">
          <property name="default" value="0"/>
          <property name="name" value="rechten"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="72f8d58c-fa0b-4130-9ef2-484bcef0e041" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" type="field">
          <property name="name" value="memberfunction_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="95a9855f-84d8-4fc7-a4aa-5fd6e3367705" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" sibling-uuid="72f8d58c-fa0b-4130-9ef2-484bcef0e041" type="field">
          <property name="name" value="member_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="73f8520e-f511-4a3a-b5f0-57b398a71625" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" sibling-uuid="95a9855f-84d8-4fc7-a4aa-5fd6e3367705" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="256d416a-5c98-4035-8274-eb0b187b6fc6" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" sibling-uuid="73f8520e-f511-4a3a-b5f0-57b398a71625" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0c7ad6a7-e3e8-42f5-a7cd-cbdc6980103a" parent-uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9a098640-3b52-47ba-8fdd-3e92040747af" parent-uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" sibling-uuid="0c7ad6a7-e3e8-42f5-a7cd-cbdc6980103a" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="4073d354-6607-44b9-87cd-cfb9a2d3269a" parent-uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" sibling-uuid="9a098640-3b52-47ba-8fdd-3e92040747af" type="field">
          <property name="name" value="description"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a42753ca-21f8-4688-aa0d-f74a56a1e793" parent-uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" sibling-uuid="4073d354-6607-44b9-87cd-cfb9a2d3269a" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0f985c29-09de-46ff-83bd-620b92797514" parent-uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" sibling-uuid="a42753ca-21f8-4688-aa0d-f74a56a1e793" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="193b01ca-0b08-460e-8de3-facd12bdd767" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="8d54451d-c9df-4959-a33a-1bbaba2956c5" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="193b01ca-0b08-460e-8de3-facd12bdd767" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="b8dff271-3190-450d-8e30-89cf1b431ce0" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="8d54451d-c9df-4959-a33a-1bbaba2956c5" type="field">
          <property name="name" value="first_name"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1193cea6-2e0e-417b-8550-968252359fe7" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="b8dff271-3190-450d-8e30-89cf1b431ce0" type="field">
          <property name="name" value="infix"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5d4aa437-e9d0-479d-a712-438665e45d56" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="1193cea6-2e0e-417b-8550-968252359fe7" type="field">
          <property name="name" value="last_name"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4803c598-59a6-460c-aba4-e65b6a0b510d" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="5d4aa437-e9d0-479d-a712-438665e45d56" type="field">
          <property name="name" value="streetname"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="946bae31-37c9-4966-9818-7ab939497242" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="4803c598-59a6-460c-aba4-e65b6a0b510d" type="field">
          <property name="name" value="house_number"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="cfca6a62-962c-4d50-8efe-fbabde878675" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="946bae31-37c9-4966-9818-7ab939497242" type="field">
          <property name="name" value="zipcode"/>
          <property name="size" value="10"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="aa71101f-3ce3-49e9-abd5-e54449f9731a" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="cfca6a62-962c-4d50-8efe-fbabde878675" type="field">
          <property name="name" value="city"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="46aa16cf-4be0-4a39-805e-577c8831b7e4" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="aa71101f-3ce3-49e9-abd5-e54449f9731a" type="field">
          <property name="name" value="birth_date"/>
          <property name="size" value="5"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b1b2cdeb-f570-4ca2-b0e2-804dadf0c351" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="46aa16cf-4be0-4a39-805e-577c8831b7e4" type="field">
          <property name="name" value="birth_year"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="43ef9dcf-43fd-4f54-bd2d-7a871b629327" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="b1b2cdeb-f570-4ca2-b0e2-804dadf0c351" type="field">
          <property name="name" value="telephone1"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a9725276-5d62-4004-b506-db74e1fd1585" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="43ef9dcf-43fd-4f54-bd2d-7a871b629327" type="field">
          <property name="name" value="telephone2"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="088ff2c2-1bb2-4768-9b5d-cc71ea23e5d0" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="a9725276-5d62-4004-b506-db74e1fd1585" type="field">
          <property name="name" value="email"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5655e6a5-ab52-4f7f-9762-2f4d33808213" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="088ff2c2-1bb2-4768-9b5d-cc71ea23e5d0" type="field">
          <property name="name" value="email_subscription"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="f60ba56c-0124-4998-8f35-7219b7eebe01" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="5655e6a5-ab52-4f7f-9762-2f4d33808213" type="field">
          <property name="name" value="membership_start_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="9245051f-ece7-459a-ab78-62067996cd77" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="f60ba56c-0124-4998-8f35-7219b7eebe01" type="field">
          <property name="name" value="membership_end_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="3965fd84-0e41-4eb5-97a5-c8f843fe935b" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="9245051f-ece7-459a-ab78-62067996cd77" type="field">
          <property name="name" value="photo"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2d5c62b0-2f9e-4b25-a24b-ebdaafe76f2f" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="3965fd84-0e41-4eb5-97a5-c8f843fe935b" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="65a85059-f743-48fe-ba60-7562e7e9a095" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="2d5c62b0-2f9e-4b25-a24b-ebdaafe76f2f" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="268f9172-131c-4538-a76f-6fd81be267ef" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="65a85059-f743-48fe-ba60-7562e7e9a095" type="field">
          <property name="name" value="username"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="19ba3c34-a62f-4603-9019-f4941daa3c47" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="268f9172-131c-4538-a76f-6fd81be267ef" type="field">
          <property name="name" value="passwd"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ac82e3c7-bc0a-4361-9311-347eeb39f82f" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="19ba3c34-a62f-4603-9019-f4941daa3c47" type="field">
          <property name="name" value="password_reset_code"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a17d0c8f-f1da-4f19-8a38-8b6b9c3d47f6" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="ac82e3c7-bc0a-4361-9311-347eeb39f82f" type="field">
          <property name="name" value="pw_reset_valid_until"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="8a0c9364-5bed-45ca-bfcc-1081150a6b93" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="a17d0c8f-f1da-4f19-8a38-8b6b9c3d47f6" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="3bc1c4a0-51d2-44a1-aef6-ce0196e98639" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" sibling-uuid="8a0c9364-5bed-45ca-bfcc-1081150a6b93" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a0fb6758-6db3-4414-998d-0f9b51623e84" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="3afeb924-2065-495b-9cd2-025a91ff1beb" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="a0fb6758-6db3-4414-998d-0f9b51623e84" type="field">
          <property name="name" value="titel"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ddd59297-e4b5-41ce-8e56-10c23de6b7db" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="3afeb924-2065-495b-9cd2-025a91ff1beb" type="field">
          <property name="name" value="jaar"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b0576c88-d7b1-4896-be2c-34b45cfa17b7" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="ddd59297-e4b5-41ce-8e56-10c23de6b7db" type="field">
          <property name="name" value="tags"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1afee284-ce46-4ca3-af0c-11210ff41009" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="b0576c88-d7b1-4896-be2c-34b45cfa17b7" type="field">
          <property name="name" value="duur_uitvoering"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f0d71e96-e6df-4673-8153-9fd91d66fb69" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="1afee284-ce46-4ca3-af0c-11210ff41009" type="field">
          <property name="name" value="datum_in_repertoire"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="3724896b-66b1-4fbf-8a2a-e1180b677b90" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="f0d71e96-e6df-4673-8153-9fd91d66fb69" type="field">
          <property name="name" value="bestaand_nummer"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="83e1bd08-8336-4e76-a435-abe057beef5a" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="3724896b-66b1-4fbf-8a2a-e1180b677b90" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="38099a04-afac-4cbd-9139-198574bf19ec" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="83e1bd08-8336-4e76-a435-abe057beef5a" type="field">
          <property name="name" value="componist1"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="cf78edea-76b7-4916-9bf5-9d1c28af01db" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="38099a04-afac-4cbd-9139-198574bf19ec" type="field">
          <property name="name" value="componist2"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="202a2d4d-8cf4-40b0-956f-6e93bb2c0a21" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="cf78edea-76b7-4916-9bf5-9d1c28af01db" type="field">
          <property name="name" value="opmerkingen"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="45f9630c-28fe-4994-891e-e88cbbf05ac5" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="2f1da3a2-61cd-4060-bf63-1488ac8faecd" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="45f9630c-28fe-4994-891e-e88cbbf05ac5" type="field">
          <property name="name" value="url"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f069f0e1-e1a5-45d3-b7a5-a5ca993ff0d8" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="2f1da3a2-61cd-4060-bf63-1488ac8faecd" type="field">
          <property name="name" value="linktype"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="da27ed38-be00-4b43-9643-3de303dfaa33" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="f069f0e1-e1a5-45d3-b7a5-a5ca993ff0d8" type="field">
          <property name="default" value="0"/>
          <property name="name" value="eigen_uitvoering"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="71f8bc99-e020-4d4d-ba18-b2f25c3fe1c2" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="da27ed38-be00-4b43-9643-3de303dfaa33" type="field">
          <property name="name" value="id_muziekwerk"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="38d54a11-26bd-4b1d-85bf-54de87a351e6" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="71f8bc99-e020-4d4d-ba18-b2f25c3fe1c2" type="field">
          <property name="name" value="referentie"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c0371173-e603-4133-880f-61c8562acf64" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="38d54a11-26bd-4b1d-85bf-54de87a351e6" type="field">
          <property name="name" value="naam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="92649eac-983b-481d-a465-17bbc79871eb" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="ee30cb83-7ec6-483b-a399-9b41267b2bab" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="92649eac-983b-481d-a465-17bbc79871eb" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="c5a7fe5e-c5bd-434a-a666-031e829e6a4b" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="ee30cb83-7ec6-483b-a399-9b41267b2bab" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e49d8225-71f7-463c-ba7e-5f08354ca8a0" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="c5a7fe5e-c5bd-434a-a666-031e829e6a4b" type="field">
          <property name="name" value="name"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0798681d-66f5-497c-ad46-fcb592f66c3e" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="e49d8225-71f7-463c-ba7e-5f08354ca8a0" type="field">
          <property name="name" value="scopes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="84208c09-1aad-4d52-9e68-f99d8295397b" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="0798681d-66f5-497c-ad46-fcb592f66c3e" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="0e34e722-9834-46f4-8246-00b60f61dc9a" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="84208c09-1aad-4d52-9e68-f99d8295397b" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b22aa0cd-471a-4a64-8f90-d8372bfadc71" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="0e34e722-9834-46f4-8246-00b60f61dc9a" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="de5fa9b4-7da9-42c5-8673-130fb1c57222" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" sibling-uuid="b22aa0cd-471a-4a64-8f90-d8372bfadc71" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="8a2bf9ac-cbab-4b6b-beee-262e090ac2f6" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="845305f2-3472-4d21-af08-79d99ec02107" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" sibling-uuid="8a2bf9ac-cbab-4b6b-beee-262e090ac2f6" type="field">
          <property name="name" value="user_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2ffc2131-1b09-4da7-8d21-96ef248eb6ce" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" sibling-uuid="845305f2-3472-4d21-af08-79d99ec02107" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e62cd9ba-4e27-4120-b5c4-bfeccb8e4fac" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" sibling-uuid="2ffc2131-1b09-4da7-8d21-96ef248eb6ce" type="field">
          <property name="name" value="scopes"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="426c1db7-4faf-4f3c-b4dd-4e916db44591" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" sibling-uuid="e62cd9ba-4e27-4120-b5c4-bfeccb8e4fac" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="a2db9618-a482-4703-9fb0-51d5bc5fc53c" parent-uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" sibling-uuid="426c1db7-4faf-4f3c-b4dd-4e916db44591" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="f2dd5495-8ab7-4977-8990-e5e3b56999ce" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="27f2240f-4d2a-4d99-9056-11a4896a697d" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="f2dd5495-8ab7-4977-8990-e5e3b56999ce" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="304b3468-774d-4fef-ad66-6f2139931dae" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="27f2240f-4d2a-4d99-9056-11a4896a697d" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5a45549a-3c9f-478d-abaa-32b3ee3eb4ef" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="304b3468-774d-4fef-ad66-6f2139931dae" type="field">
          <property name="name" value="secret"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0ff1aa4a-e3ec-467b-8333-b94d3730504b" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="5a45549a-3c9f-478d-abaa-32b3ee3eb4ef" type="field">
          <property name="name" value="redirect"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="0a12844e-2a1b-4fd2-80ba-28d6d573c5ee" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="0ff1aa4a-e3ec-467b-8333-b94d3730504b" type="field">
          <property name="name" value="personal_access_client"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="0e84c4b5-337a-46ba-8565-6627d68bcce8" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="0a12844e-2a1b-4fd2-80ba-28d6d573c5ee" type="field">
          <property name="name" value="password_client"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="594aa72f-a1e1-4f75-a55b-9aa36354fbbc" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="0e84c4b5-337a-46ba-8565-6627d68bcce8" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="d41e4de0-3766-46ba-897e-88d21a47a4a5" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="594aa72f-a1e1-4f75-a55b-9aa36354fbbc" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="648ac18b-2107-484b-9441-4f986c743ff6" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" sibling-uuid="d41e4de0-3766-46ba-897e-88d21a47a4a5" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="059087ba-1946-4472-9dba-14141bd05834" parent-uuid="8e206929-1d19-46af-b6df-4c0df9b99050" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="72d69579-776a-4726-b4c8-164b87f247dd" parent-uuid="8e206929-1d19-46af-b6df-4c0df9b99050" sibling-uuid="059087ba-1946-4472-9dba-14141bd05834" type="field">
          <property name="name" value="client_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="a66ae9f0-1921-49ab-94b9-84225f834706" parent-uuid="8e206929-1d19-46af-b6df-4c0df9b99050" sibling-uuid="72d69579-776a-4726-b4c8-164b87f247dd" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b110cd40-fb82-4887-93a6-e27317528d95" parent-uuid="8e206929-1d19-46af-b6df-4c0df9b99050" sibling-uuid="a66ae9f0-1921-49ab-94b9-84225f834706" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="586adc7b-90be-4faf-802d-4bb00204035e" parent-uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" type="field">
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="d28debc7-ee24-4122-9840-c52f3a3ae6c0" parent-uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" sibling-uuid="586adc7b-90be-4faf-802d-4bb00204035e" type="field">
          <property name="name" value="access_token_id"/>
          <property name="required" value="true"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="92cfc78a-42d1-4123-b72c-375f188dd42c" parent-uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" sibling-uuid="d28debc7-ee24-4122-9840-c52f3a3ae6c0" type="field">
          <property name="name" value="revoked"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="55e6eecf-4d7a-4ed8-9b4e-0994d76faab3" parent-uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" sibling-uuid="92cfc78a-42d1-4123-b72c-375f188dd42c" type="field">
          <property name="name" value="expires_at"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="21a750ca-6ded-4933-8175-6e530a6e4708" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="6848b2d1-62eb-449a-9270-************" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="21a750ca-6ded-4933-8175-6e530a6e4708" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2d908445-4e70-4381-ad4b-15b3cbf86b24" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="6848b2d1-62eb-449a-9270-************" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="59db3129-4b61-4ebf-bd36-7c94063e443a" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="2d908445-4e70-4381-ad4b-15b3cbf86b24" type="field">
          <property name="name" value="content_volgorde"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="1691dae9-6c3a-4b4f-8ef0-a175989d74d5" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="59db3129-4b61-4ebf-bd36-7c94063e443a" type="field">
          <property name="default" value="1"/>
          <property name="name" value="doorzoekbaar"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="a918c3ed-e07e-40de-b256-78a69011375f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="1691dae9-6c3a-4b4f-8ef0-a175989d74d5" type="field">
          <property name="default" value="0"/>
          <property name="name" value="is_default"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="41591aa9-7352-47e1-881e-0b825a4eaa49" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="a918c3ed-e07e-40de-b256-78a69011375f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="protected"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="8d64b0b1-5efb-42d9-b2c6-4b8a7d677e0f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="41591aa9-7352-47e1-881e-0b825a4eaa49" type="field">
          <property name="default" value="1"/>
          <property name="name" value="maxaantalberichten"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="42e26651-0dd9-4962-bc25-1fe912422578" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="8d64b0b1-5efb-42d9-b2c6-4b8a7d677e0f" type="field">
          <property name="default" value="0"/>
          <property name="name" value="maxberichtlength"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="42bce263-ef64-4cd7-9692-16a43f156871" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="42e26651-0dd9-4962-bc25-1fe912422578" type="field">
          <property name="default" value="0"/>
          <property name="name" value="volgnummer"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="99e98933-e92e-4ab4-8da0-ff68dfb6300f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="42bce263-ef64-4cd7-9692-16a43f156871" type="field">
          <property name="name" value="controller_path"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="1b6e4a86-e9e3-4ec0-a650-aa3c34ef8a91" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="99e98933-e92e-4ab4-8da0-ff68dfb6300f" type="field">
          <property name="name" value="controller"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="239b72be-342a-413b-8f32-b252a7ee7c38" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="1b6e4a86-e9e3-4ec0-a650-aa3c34ef8a91" type="field">
          <property name="default" value="1"/>
          <property name="name" value="toon_in_cms"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="6751ab47-1a80-4749-837e-879b35e2f461" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="239b72be-342a-413b-8f32-b252a7ee7c38" type="field">
          <property name="default" value="1"/>
          <property name="name" value="add_to_menu"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="809f701b-4b39-4474-be86-a1233e1ed207" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="6751ab47-1a80-4749-837e-879b35e2f461" type="field">
          <property name="default" value="0"/>
          <property name="name" value="isblogpagina"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="ed79f728-cfa4-46b8-bd3d-6ebdf7663cb4" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="809f701b-4b39-4474-be86-a1233e1ed207" type="field">
          <property name="default" value="0"/>
          <property name="name" value="islaatstenieuwspagina"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="cb4b8b08-5a97-4808-b093-29f15152b65b" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="ed79f728-cfa4-46b8-bd3d-6ebdf7663cb4" type="field">
          <property name="default" value="0"/>
          <property name="name" value="iskalenderpagina"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="f3895319-254c-474a-a83d-8b06146c6345" parent-uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a1ebbb9f-19f4-437f-b0ea-19e5c75f28e4" parent-uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" sibling-uuid="f3895319-254c-474a-a83d-8b06146c6345" type="field">
          <property name="name" value="token"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="abf08fe9-c35f-4d6f-968a-2624475a6e4c" parent-uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" sibling-uuid="a1ebbb9f-19f4-437f-b0ea-19e5c75f28e4" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="21520d9f-17e5-4b40-a56b-c5718b7c4c8a" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="fa414586-644b-49ea-8bb3-d3e663d01184" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="21520d9f-17e5-4b40-a56b-c5718b7c4c8a" type="field">
          <property name="name" value="operator"/>
          <property name="required" value="true"/>
          <property name="size" value="45"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="43ab29dc-ea6a-4d7b-97f6-41ca7bf542c7" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="fa414586-644b-49ea-8bb3-d3e663d01184" type="field">
          <property name="name" value="label"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fa806905-166f-41f0-a945-7e23631a5734" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="43ab29dc-ea6a-4d7b-97f6-41ca7bf542c7" type="field">
          <property name="name" value="heeft_operand"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="d51484ff-e64b-41c8-b37b-288cf819de5e" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="70b545d4-1f81-4499-b4f0-da64802bce8e" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="d51484ff-e64b-41c8-b37b-288cf819de5e" type="field">
          <property name="name" value="id_webform"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="250e2f7e-6236-4078-a405-c6f7b4e47c2f" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="70b545d4-1f81-4499-b4f0-da64802bce8e" type="field">
          <property name="name" value="starttekst"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="425cb305-64ad-4eab-97a5-4d4cd147db57" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="250e2f7e-6236-4078-a405-c6f7b4e47c2f" type="field">
          <property name="name" value="sommatielabel"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6046088e-a8c4-4d11-9d5c-83a2f0bd4583" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="425cb305-64ad-4eab-97a5-4d4cd147db57" type="field">
          <property name="name" value="slottekst"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="ff1775d2-1e87-4b27-abc6-8e9fc576c90f" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="6046088e-a8c4-4d11-9d5c-83a2f0bd4583" type="field">
          <property name="default" value="0"/>
          <property name="name" value="id_emailveld"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="2dd02f08-5e00-4729-9102-a481acc039ab" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" type="field">
          <property name="name" value="user_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="52d08e17-89e3-44e5-a889-1af37d0b4b92" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" sibling-uuid="2dd02f08-5e00-4729-9102-a481acc039ab" type="field">
          <property name="name" value="role_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d426799e-8a78-485c-8cd9-235fad38c29f" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" sibling-uuid="52d08e17-89e3-44e5-a889-1af37d0b4b92" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="db2a29b9-a1a1-4d34-8442-1b7c01af6b5f" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" sibling-uuid="d426799e-8a78-485c-8cd9-235fad38c29f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1feacec2-ae83-49b0-961d-2072abeb7cd5" parent-uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="d0e78452-0043-4e40-974b-7d46d9132df5" parent-uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" sibling-uuid="1feacec2-ae83-49b0-961d-2072abeb7cd5" type="field">
          <property name="name" value="rolename"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="975af035-dd7f-4f9d-9732-d48be3e4389f" parent-uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" sibling-uuid="d0e78452-0043-4e40-974b-7d46d9132df5" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f85d6785-5050-489c-aea3-24cd0c87afbf" parent-uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" sibling-uuid="975af035-dd7f-4f9d-9732-d48be3e4389f" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="ec0d0b28-eb8a-4c99-8ef3-831876311b43" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="3e64ffc0-057f-4e5d-8884-edd396799abe" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="ec0d0b28-eb8a-4c99-8ef3-831876311b43" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e1c5f276-e67c-46a3-b40b-938bbc5f6c71" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="3e64ffc0-057f-4e5d-8884-edd396799abe" type="field">
          <property name="name" value="datum"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="f9f94f9b-dcf8-4823-9f1b-fba95993bca0" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="e1c5f276-e67c-46a3-b40b-938bbc5f6c71" type="field">
          <property name="name" value="songs"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="07169843-a02b-4043-8679-061c264bf682" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="f9f94f9b-dcf8-4823-9f1b-fba95993bca0" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="d2823d6e-1f4e-4e18-ba1d-e1caf50df664" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="07169843-a02b-4043-8679-061c264bf682" type="field">
          <property name="name" value="omschrijving"/>
          <property name="size" value="256"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="79d01b2f-d68c-48f8-b4d6-7c07dcb93383" parent-uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" sibling-uuid="d2823d6e-1f4e-4e18-ba1d-e1caf50df664" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="9f4da929-63c5-4dfe-afe6-ae49ec87e89a" parent-uuid="79f657f0-**************-473b038ae0b6" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="c3585b2a-f2a6-4866-9427-a1571bd3f7fc" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="9f4da929-63c5-4dfe-afe6-ae49ec87e89a" type="field">
          <property name="name" value="songitem_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="52620195-20d0-4aa9-9df3-2ea86773fee5" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="c3585b2a-f2a6-4866-9427-a1571bd3f7fc" type="field">
          <property name="name" value="url"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="59494aec-7cc2-4491-ac6d-53f401169ee2" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="52620195-20d0-4aa9-9df3-2ea86773fee5" type="field">
          <property name="name" value="original_name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c8db2f7a-3cf5-4633-84a5-1b2b0e5288ad" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="59494aec-7cc2-4491-ac6d-53f401169ee2" type="field">
          <property name="name" value="history_date"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="f112fa5c-d374-4d70-88b0-1e68f1108073" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="c8db2f7a-3cf5-4633-84a5-1b2b0e5288ad" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4304db42-4efb-4a0c-9432-1775971f1e2d" parent-uuid="79f657f0-**************-473b038ae0b6" sibling-uuid="f112fa5c-d374-4d70-88b0-1e68f1108073" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="390a9503-bfb6-44d4-9116-98454406fef4" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="2f0d2b9e-a94e-4bd0-ab93-47a47289f507" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="390a9503-bfb6-44d4-9116-98454406fef4" type="field">
          <property name="name" value="song_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1bf81c64-291b-44a6-94e5-b9912d85f0ed" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="2f0d2b9e-a94e-4bd0-ab93-47a47289f507" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a75ff409-5cbd-4ab1-a6a8-2d053992ea8c" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="1bf81c64-291b-44a6-94e5-b9912d85f0ed" type="field">
          <property name="name" value="linktype"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="17f77cb4-4b29-49d1-aecf-0628bfa49ca8" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="a75ff409-5cbd-4ab1-a6a8-2d053992ea8c" type="field">
          <property name="default" value="0"/>
          <property name="name" value="own_recording"/>
          <property name="required" value="true"/>
          <property name="type" value="smallInteger"/>
        </element>
        <element action="add" uuid="d0345917-92b3-4af0-82e9-bc46f3f3818b" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="17f77cb4-4b29-49d1-aecf-0628bfa49ca8" type="field">
          <property name="name" value="reference"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="03874a9f-c51a-4540-a57d-3ebf642ec567" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="d0345917-92b3-4af0-82e9-bc46f3f3818b" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="cb3c34a6-1301-43aa-9b95-1b679cf1b7ed" parent-uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" sibling-uuid="03874a9f-c51a-4540-a57d-3ebf642ec567" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="c3505b76-322e-43a6-89fa-6ced1d1c9fc0" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="414a1131-a324-469b-a8f6-6c0dcdd425f7" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="c3505b76-322e-43a6-89fa-6ced1d1c9fc0" type="field">
          <property name="name" value="domain_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="cf23d073-4477-43e7-a2b3-fcc63c40427b" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="414a1131-a324-469b-a8f6-6c0dcdd425f7" type="field">
          <property name="name" value="title"/>
          <property name="required" value="true"/>
          <property name="size" value="75"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b37fe35e-e8a3-4bb9-b822-cabe9da8e2e5" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="cf23d073-4477-43e7-a2b3-fcc63c40427b" type="field">
          <property name="name" value="year"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="2afdf8e0-a780-4c68-8f89-e28a85fc0b57" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="b37fe35e-e8a3-4bb9-b822-cabe9da8e2e5" type="field">
          <property name="name" value="tags"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="5522a9d3-b123-40c4-8297-20a663aeed2e" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="2afdf8e0-a780-4c68-8f89-e28a85fc0b57" type="field">
          <property name="name" value="time_in_setlist"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="d70c6a69-01fd-46f3-a37f-37809c492898" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="5522a9d3-b123-40c4-8297-20a663aeed2e" type="field">
          <property name="name" value="date_in_repertoire"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="19d4f448-56c2-4984-84d4-f03d4d42ae4c" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="d70c6a69-01fd-46f3-a37f-37809c492898" type="field">
          <property name="name" value="archive_number"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="8be731ef-5ad3-42b5-9674-258bfbed9c5d" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="19d4f448-56c2-4984-84d4-f03d4d42ae4c" type="field">
          <property name="name" value="composer1"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="696df5e3-201e-42f7-a111-150c1d2abda2" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="8be731ef-5ad3-42b5-9674-258bfbed9c5d" type="field">
          <property name="name" value="composer2"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e96f9f42-8c6b-4e50-8488-5dff10e61461" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="696df5e3-201e-42f7-a111-150c1d2abda2" type="field">
          <property name="name" value="remarks"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="d1202b40-3f1d-4d0a-9090-1dd606c9efa7" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="e96f9f42-8c6b-4e50-8488-5dff10e61461" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="f983fb7d-1b00-47af-94b3-3ffda95b138a" parent-uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" sibling-uuid="d1202b40-3f1d-4d0a-9090-1dd606c9efa7" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="b5bffab3-45b0-4c3b-8805-f0e00c682897" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="5b8cef40-c92a-4062-bfd1-ec2584c403f8" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="b5bffab3-45b0-4c3b-8805-f0e00c682897" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="cab35cee-c59b-42cd-9421-c91f6f19d2f3" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="5b8cef40-c92a-4062-bfd1-ec2584c403f8" type="field">
          <property name="name" value="naam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f766f99c-3930-469e-9469-11bbc5c54a97" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="cab35cee-c59b-42cd-9421-c91f6f19d2f3" type="field">
          <property name="name" value="adres"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="de3a6236-fa5a-49b1-8585-f55a5b27eb9c" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="f766f99c-3930-469e-9469-11bbc5c54a97" type="field">
          <property name="name" value="postcode"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="43264548-6aa2-4c03-b0d8-857e7fcbe417" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="de3a6236-fa5a-49b1-8585-f55a5b27eb9c" type="field">
          <property name="name" value="woonplaats"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ede11297-8efc-4e78-a74d-d67275180e9f" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="43264548-6aa2-4c03-b0d8-857e7fcbe417" type="field">
          <property name="name" value="logo"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e22553ca-2ff1-4181-8bcd-9bb036a76c98" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="ede11297-8efc-4e78-a74d-d67275180e9f" type="field">
          <property name="name" value="onderschrift"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="db6dd027-a35b-4777-a905-f4e6e889015a" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="e22553ca-2ff1-4181-8bcd-9bb036a76c98" type="field">
          <property name="name" value="website"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="91fc8cb0-ae78-4922-8aaa-f826ddfd42bb" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="db6dd027-a35b-4777-a905-f4e6e889015a" type="field">
          <property name="default" value="0"/>
          <property name="name" value="tijdtonen"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="d005fcc3-3a1b-4c7c-afd9-11fb79cc303a" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="91fc8cb0-ae78-4922-8aaa-f826ddfd42bb" type="field">
          <property name="name" value="cp_naam"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="fb936629-fc7e-44f2-a51d-72fd2d71cdc2" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="d005fcc3-3a1b-4c7c-afd9-11fb79cc303a" type="field">
          <property name="name" value="cp_telefoon"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ec04e58f-9811-4799-ac3c-c19387fd32ad" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="5a0e65cb-c358-4860-a8d2-9d34eb4357fd" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="ec04e58f-9811-4799-ac3c-c19387fd32ad" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="dfd1faed-cca6-490c-8e43-23c5db4144bf" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="5a0e65cb-c358-4860-a8d2-9d34eb4357fd" type="field">
          <property name="name" value="date"/>
          <property name="required" value="true"/>
          <property name="type" value="date"/>
        </element>
        <element action="add" uuid="af3f0bfa-b53c-443f-807b-ae80b670893d" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="dfd1faed-cca6-490c-8e43-23c5db4144bf" type="field">
          <property name="name" value="time"/>
          <property name="required" value="true"/>
          <property name="type" value="time"/>
        </element>
        <element action="add" uuid="028a8746-4392-4b1c-9af4-ce8a67cdb663" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="af3f0bfa-b53c-443f-807b-ae80b670893d" type="field">
          <property name="name" value="dt"/>
          <property name="required" value="true"/>
          <property name="type" value="dateTime"/>
        </element>
        <element action="add" uuid="be6dee56-c2a6-4e89-b388-58f0abb43e84" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="028a8746-4392-4b1c-9af4-ce8a67cdb663" type="field">
          <property name="name" value="probeid"/>
          <property name="required" value="true"/>
          <property name="size" value="20"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a2c6817e-d06e-4ff3-951e-bab7c56dacdf" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="be6dee56-c2a6-4e89-b388-58f0abb43e84" type="field">
          <property name="name" value="temp"/>
          <property name="required" value="true"/>
          <property name="type" value="float"/>
        </element>
        <element action="add" uuid="0e6d55ef-38e2-43af-9b2c-501383cf3084" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="6941571c-105d-433e-a2f1-09e0dc5f6d33" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="0e6d55ef-38e2-43af-9b2c-501383cf3084" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="e3a9d02c-3ac9-4ff1-86dc-a2fba003f869" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="6941571c-105d-433e-a2f1-09e0dc5f6d33" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e8c607d6-6971-44de-83c7-5c6569f3a9c3" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="e3a9d02c-3ac9-4ff1-86dc-a2fba003f869" type="field">
          <property name="name" value="ip"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="97d8c3a8-bfda-4053-a715-1b726708a852" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="e8c607d6-6971-44de-83c7-5c6569f3a9c3" type="field">
          <property name="name" value="probe"/>
          <property name="required" value="true"/>
          <property name="size" value="20"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="622fb3b6-070e-45ef-8974-51ef403a3d84" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="28d30705-5def-482e-a115-995034a298b5" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="622fb3b6-070e-45ef-8974-51ef403a3d84" type="field">
          <property name="name" value="tekst"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="3071faca-8c26-4db8-8ed7-b882604c1776" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="28d30705-5def-482e-a115-995034a298b5" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="timestamp"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a6c13841-57cb-4307-8cfb-98ad9e4cee19" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="3071faca-8c26-4db8-8ed7-b882604c1776" type="field">
          <property name="name" value="publication"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="dc218146-d079-4c87-8e44-b82bbb34589c" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="a6c13841-57cb-4307-8cfb-98ad9e4cee19" type="field">
          <property name="name" value="whosays"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="7fdb21ea-560f-429b-b0fa-2dbfebfa5e6b" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="81708b7f-c073-456b-bcbf-b6e772c96c5f" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="7fdb21ea-560f-429b-b0fa-2dbfebfa5e6b" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="8fba5447-d309-48b9-a519-48315f53ad90" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="81708b7f-c073-456b-bcbf-b6e772c96c5f" type="field">
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0e0aa7c7-1b61-40d3-ad09-8b939685ab80" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="8fba5447-d309-48b9-a519-48315f53ad90" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="33d0a092-8b20-4f7d-8884-41df3d1269a3" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="0e0aa7c7-1b61-40d3-ad09-8b939685ab80" type="field">
          <property name="name" value="password"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f2376a5a-4be8-4ef0-a90a-63026c94af9c" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="33d0a092-8b20-4f7d-8884-41df3d1269a3" type="field">
          <property name="name" value="remember_token"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="149a799e-b71a-4db4-b5d5-aae55f63a027" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="f2376a5a-4be8-4ef0-a90a-63026c94af9c" type="field">
          <property name="name" value="api_token"/>
          <property name="size" value="60"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0a1e50d6-67be-4a57-8602-4ef5d2d4dc78" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="149a799e-b71a-4db4-b5d5-aae55f63a027" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="1767ab5a-c906-4bf2-9b94-f754e06ce787" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" sibling-uuid="0a1e50d6-67be-4a57-8602-4ef5d2d4dc78" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="e60e041b-e8bc-4b56-95de-f5fd24ad5462" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="0eaabf16-56a1-46ea-baa9-2c7321b1844e" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="e60e041b-e8bc-4b56-95de-f5fd24ad5462" type="field">
          <property name="name" value="id_webform"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="b335630d-7db0-4b82-be9f-912d1a839d8c" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="0eaabf16-56a1-46ea-baa9-2c7321b1844e" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="datetime_send"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="39156b81-0371-4f1b-bf28-83043b1fcb6c" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="b335630d-7db0-4b82-be9f-912d1a839d8c" type="field">
          <property name="name" value="content"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="e9e7c2bc-57a8-4101-957a-7597ed4bcf3a" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="74a8f1f2-e1a1-475f-9e2b-ab47a4fcbc3d" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="e9e7c2bc-57a8-4101-957a-7597ed4bcf3a" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="78b40280-72a9-488a-b253-5a12e9654b34" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="74a8f1f2-e1a1-475f-9e2b-ab47a4fcbc3d" type="field">
          <property name="name" value="id_page"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="db67dc47-eb6e-45c7-88af-3baa96d50043" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="78b40280-72a9-488a-b253-5a12e9654b34" type="field">
          <property name="name" value="ipaddress"/>
          <property name="required" value="true"/>
          <property name="size" value="40"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e6df4719-1580-4536-9fa3-3053472dd1c2" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="db67dc47-eb6e-45c7-88af-3baa96d50043" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="dt"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="9be13c91-3788-4dd7-a24d-b5b2e3122b02" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="e6df4719-1580-4536-9fa3-3053472dd1c2" type="field">
          <property name="name" value="useragent"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="0c648c68-6695-4ac4-89e0-2df413e0617b" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="9be13c91-3788-4dd7-a24d-b5b2e3122b02" type="field">
          <property name="name" value="opmerking"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="141c9c92-95aa-4f19-aa10-2af43c13de22" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="09120c0c-a148-403f-91f9-a9e1cdc9f51a" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="141c9c92-95aa-4f19-aa10-2af43c13de22" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="26abed5c-6a9a-4b66-b399-059bfd46f424" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="09120c0c-a148-403f-91f9-a9e1cdc9f51a" type="field">
          <property name="name" value="id_page"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="c7ae4cca-766b-4983-8edb-b7f9ef898bbb" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="26abed5c-6a9a-4b66-b399-059bfd46f424" type="field">
          <property name="name" value="ipaddress"/>
          <property name="required" value="true"/>
          <property name="size" value="40"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="4896c4e9-41b5-4bf1-acdd-cd688fb5cac5" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="c7ae4cca-766b-4983-8edb-b7f9ef898bbb" type="field">
          <property name="default" value="NOW()"/>
          <property name="name" value="dt"/>
          <property name="required" value="true"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0c039ccf-6b1b-4e8f-b45c-5c3d0b0e3bf1" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="4896c4e9-41b5-4bf1-acdd-cd688fb5cac5" type="field">
          <property name="name" value="useragent"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e6d143ae-5ae7-44fe-b7e2-2fb48ba5395f" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="0c039ccf-6b1b-4e8f-b45c-5c3d0b0e3bf1" type="field">
          <property name="name" value="opmerking"/>
          <property name="size" value="100"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ddc047d7-3f8f-482e-bb03-64060574a0c4" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="8c0d45a8-7c30-49f8-9488-02288167a3a6" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="ddc047d7-3f8f-482e-bb03-64060574a0c4" type="field">
          <property name="name" value="titel"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="cfdda226-a6fe-46f7-894b-cfc048fc9272" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="8c0d45a8-7c30-49f8-9488-02288167a3a6" type="field">
          <property name="name" value="id_domein"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="197993d1-491d-4a8d-a875-455221357f02" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="cfdda226-a6fe-46f7-894b-cfc048fc9272" type="field">
          <property name="name" value="emailtarget"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e24a5704-3710-4eaa-951b-b78f0bbd3daa" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="197993d1-491d-4a8d-a875-455221357f02" type="field">
          <property name="default" value="verzenden"/>
          <property name="name" value="tekstopverzendenknop"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="a4f28047-5a75-4c3e-8582-af988626e81f" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="e24a5704-3710-4eaa-951b-b78f0bbd3daa" type="field">
          <property name="default" value="0"/>
          <property name="name" value="usecaptcha"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="e423d4ed-1c28-4062-ac94-f74dfd934f53" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="a4f28047-5a75-4c3e-8582-af988626e81f" type="field">
          <property name="name" value="veld_volgorde"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="ce38c599-f87a-43b6-bb6d-7b17c84389ab" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="e423d4ed-1c28-4062-ac94-f74dfd934f53" type="field">
          <property name="default" value="20"/>
          <property name="name" value="breedte_labelkolom"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="cdf5f0e0-ade1-4e1b-b12a-ad311564ed4e" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="ce38c599-f87a-43b6-bb6d-7b17c84389ab" type="field">
          <property name="name" value="breedte_tabel"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="f7463670-db71-4c81-a5de-b26f098b44dc" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="25d60f10-b398-4aa0-8767-d656d23cfd4b" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="f7463670-db71-4c81-a5de-b26f098b44dc" type="field">
          <property name="name" value="id_webform"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="195100f6-b1ad-4ca4-9ca9-51d9b37f1954" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="25d60f10-b398-4aa0-8767-d656d23cfd4b" type="field">
          <property name="name" value="naam"/>
          <property name="required" value="true"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ddd985d7-aa8b-4f2c-95f6-52dbf747e69a" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="195100f6-b1ad-4ca4-9ca9-51d9b37f1954" type="field">
          <property name="name" value="veldtype"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="68030ff3-7fec-4055-9544-38d84cc12422" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="ddd985d7-aa8b-4f2c-95f6-52dbf747e69a" type="field">
          <property name="name" value="veldspec"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="10c09743-3a7b-44e5-9a8f-fc1f74b1c53c" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="68030ff3-7fec-4055-9544-38d84cc12422" type="field">
          <property name="default" value="0"/>
          <property name="name" value="verplicht"/>
          <property name="required" value="true"/>
          <property name="type" value="tinyInteger"/>
        </element>
        <element action="add" uuid="46571efb-e12c-4aea-9eda-745d5061e0fb" parent-uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" type="index">
          <property name="name" value="events_domain_id_foreign"/>
        </element>
        <element action="add" uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="index">
          <property name="name" value="fulltextindex"/>
        </element>
        <element action="add" uuid="611bbdc1-de3d-407e-ba99-2f17f9de5e3a" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="index">
          <property name="name" value="idx_content"/>
        </element>
        <element action="add" uuid="b6ac47a4-40ee-476c-9c44-79a6a95b707f" parent-uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" type="index">
          <property name="name" value="jobs_queue_index"/>
        </element>
        <element action="add" uuid="f6ce6ee8-74ec-4d43-87ff-3f55bd6ff55c" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" type="index">
          <property name="name" value="idx_login"/>
        </element>
        <element action="add" uuid="1369ea87-542b-468b-8939-c3ecdde46d8e" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" type="index">
          <property name="name" value="member_memberfunction_function_id_foreign"/>
        </element>
        <element action="add" uuid="28f2824f-1f41-48f0-94fa-eaa9f90efa1c" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" type="index">
          <property name="name" value="member_memberfunction_member_id_foreign"/>
        </element>
        <element action="add" uuid="e7d8473b-8bf3-4986-8f3d-733b1910d6a2" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" type="index">
          <property name="name" value="members_domain_id_foreign_old"/>
        </element>
        <element action="add" uuid="6c2d7818-14f1-4292-9494-1ec21744bc10" parent-uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" type="index">
          <property name="name" value="members_username_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="b3fd544a-089e-4bf1-ace0-69dfb54b446f" parent-uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" type="index">
          <property name="name" value="oauth_access_tokens_user_id_index"/>
        </element>
        <element action="add" uuid="ef541323-ef91-4a5d-af6b-a66e07bb1af5" parent-uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" type="index">
          <property name="name" value="oauth_clients_user_id_index"/>
        </element>
        <element action="add" uuid="af91c313-763e-43bf-83c5-193823596fc1" parent-uuid="8e206929-1d19-46af-b6df-4c0df9b99050" type="index">
          <property name="name" value="oauth_personal_access_clients_client_id_index"/>
        </element>
        <element action="add" uuid="c845f252-c907-4f5f-ad8a-f17618bf7141" parent-uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" type="index">
          <property name="name" value="oauth_refresh_tokens_access_token_id_index"/>
        </element>
        <element action="add" uuid="3fe3f1d1-0d83-4389-91b7-cb09cb750931" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" type="index">
          <property name="name" value="idx_pagina"/>
        </element>
        <element action="add" uuid="4f3b191b-31e8-428b-990c-b243e49ab1e3" parent-uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" type="index">
          <property name="name" value="password_resets_email_index"/>
        </element>
        <element action="add" uuid="f0d3ed94-23a6-4dbf-9788-d45349a1317b" parent-uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" type="index">
          <property name="name" value="password_resets_token_index"/>
        </element>
        <element action="add" uuid="89ac27aa-77cd-4163-b731-1d8646b24ccb" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" type="index">
          <property name="name" value="role_user_role_id_foreign"/>
        </element>
        <element action="add" uuid="6a455bfb-372b-4631-a905-21f99913bebe" parent-uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" type="index">
          <property name="name" value="role_user_user_id_foreign"/>
        </element>
        <element action="add" uuid="abd43fa9-9d63-42c8-8266-3ad580c85b8c" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" type="index">
          <property name="name" value="idx_twitter"/>
        </element>
        <element action="add" uuid="d37369e2-08a4-4cfa-8c48-a51b4b11c9b9" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" type="index">
          <property name="name" value="user_domain_id_foreign"/>
        </element>
        <element action="add" uuid="78a61b7c-b338-47e4-8658-015759ff7675" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" type="index">
          <property name="name" value="users_api_token_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="d93260ca-a629-4a74-8d1e-948c3bd75e90" parent-uuid="441d93ef-93f8-431b-aead-7c85754ed18c" type="index">
          <property name="name" value="users_email_unique"/>
          <property name="unique" value="true"/>
        </element>
        <element action="add" uuid="287ed4bf-2b5a-4dba-932a-d5e12750f1ad" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" type="index">
          <property name="name" value="idx_verstuurde_formulieren"/>
        </element>
        <element action="add" uuid="23956d18-cf87-41fd-a695-395d8e64e00d" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" type="index">
          <property name="name" value="idx_webform"/>
        </element>
        <element action="add" uuid="57e0b604-cdbc-4ec8-82e0-eb93f0bc98ae" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" type="index">
          <property name="name" value="idx_webformveld"/>
        </element>
        <element action="add" uuid="e6c40051-c774-4527-a650-2e40a7e4d166" parent-uuid="46571efb-e12c-4aea-9eda-745d5061e0fb" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="f52d51af-2ff8-4a2d-964f-f08d504a5b73" parent-uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" type="index-field">
          <property name="name" value="content"/>
        </element>
        <element action="add" uuid="54ecf6a3-a285-4bbe-9dfb-2d8a319ef711" parent-uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" type="index-field">
          <property name="name" value="titel"/>
        </element>
        <element action="add" uuid="1a3b34a2-b16b-4feb-b8b3-331349b73f78" parent-uuid="611bbdc1-de3d-407e-ba99-2f17f9de5e3a" type="index-field">
          <property name="name" value="id_pagina"/>
        </element>
        <element action="add" uuid="1792e646-de97-4c75-b3de-bd9c526d7db3" parent-uuid="b6ac47a4-40ee-476c-9c44-79a6a95b707f" type="index-field">
          <property name="name" value="queue"/>
        </element>
        <element action="add" uuid="4cbefa87-8a94-4360-85ae-76cba044477f" parent-uuid="f6ce6ee8-74ec-4d43-87ff-3f55bd6ff55c" type="index-field">
          <property name="name" value="id_domein"/>
        </element>
        <element action="add" uuid="48883a3d-d7fe-450a-b549-2ceabbe104d1" parent-uuid="1369ea87-542b-468b-8939-c3ecdde46d8e" type="index-field">
          <property name="name" value="memberfunction_id"/>
        </element>
        <element action="add" uuid="9ffa4797-500e-4997-8321-515675c8a923" parent-uuid="28f2824f-1f41-48f0-94fa-eaa9f90efa1c" type="index-field">
          <property name="name" value="member_id"/>
        </element>
        <element action="add" uuid="7ddc9dac-7f6c-413d-a93c-52280ddb4f1f" parent-uuid="e7d8473b-8bf3-4986-8f3d-733b1910d6a2" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="b43f371c-e1b0-44a1-a2e6-7ac37b0316df" parent-uuid="6c2d7818-14f1-4292-9494-1ec21744bc10" type="index-field">
          <property name="name" value="username"/>
        </element>
        <element action="add" uuid="c04d4d90-16ea-4266-809d-4c590041a12b" parent-uuid="b3fd544a-089e-4bf1-ace0-69dfb54b446f" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="e41cfd1c-7cfa-4227-8f06-b8c33a86a3d1" parent-uuid="ef541323-ef91-4a5d-af6b-a66e07bb1af5" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="871122ae-dd1c-4902-b6be-7b0665fdc450" parent-uuid="af91c313-763e-43bf-83c5-193823596fc1" type="index-field">
          <property name="name" value="client_id"/>
        </element>
        <element action="add" uuid="0af9d6ce-f03f-4a24-a072-dd1763d474c4" parent-uuid="c845f252-c907-4f5f-ad8a-f17618bf7141" type="index-field">
          <property name="name" value="access_token_id"/>
        </element>
        <element action="add" uuid="18e9ffb2-4fb2-4dcb-8403-cc4b13376e6b" parent-uuid="3fe3f1d1-0d83-4389-91b7-cb09cb750931" type="index-field">
          <property name="name" value="id_domein"/>
        </element>
        <element action="add" uuid="0496c651-f18d-4b5a-b2d0-120c603ebf60" parent-uuid="4f3b191b-31e8-428b-990c-b243e49ab1e3" type="index-field">
          <property name="name" value="email"/>
        </element>
        <element action="add" uuid="731bf748-096e-46e8-8133-d8f001518445" parent-uuid="f0d3ed94-23a6-4dbf-9788-d45349a1317b" type="index-field">
          <property name="name" value="token"/>
        </element>
        <element action="add" uuid="87a28503-175d-49a7-a13d-d0dca0875a4b" parent-uuid="89ac27aa-77cd-4163-b731-1d8646b24ccb" type="index-field">
          <property name="name" value="role_id"/>
        </element>
        <element action="add" uuid="95541da1-e867-4c5f-9141-5354170a1ebc" parent-uuid="6a455bfb-372b-4631-a905-21f99913bebe" type="index-field">
          <property name="name" value="user_id"/>
        </element>
        <element action="add" uuid="c5d45ee2-0988-48af-a712-06f090aae9ca" parent-uuid="abd43fa9-9d63-42c8-8266-3ad580c85b8c" type="index-field">
          <property name="name" value="id_domein"/>
        </element>
        <element action="add" uuid="a1486e86-25f9-4e29-a6f1-539485be67c1" parent-uuid="d37369e2-08a4-4cfa-8c48-a51b4b11c9b9" type="index-field">
          <property name="name" value="domain_id"/>
        </element>
        <element action="add" uuid="b8e8f39f-c4eb-45da-aadd-90497debb6ae" parent-uuid="78a61b7c-b338-47e4-8658-015759ff7675" type="index-field">
          <property name="name" value="api_token"/>
        </element>
        <element action="add" uuid="8d99ddd4-cf66-4dfa-a461-29ce3649b250" parent-uuid="d93260ca-a629-4a74-8d1e-948c3bd75e90" type="index-field">
          <property name="name" value="email"/>
        </element>
        <element action="add" uuid="92e1df3b-8923-45ac-b876-ab14b830b97f" parent-uuid="287ed4bf-2b5a-4dba-932a-d5e12750f1ad" type="index-field">
          <property name="name" value="id_webform"/>
        </element>
        <element action="add" uuid="5f30b504-f5d7-4dbd-909b-8604c0bd6fce" parent-uuid="23956d18-cf87-41fd-a695-395d8e64e00d" type="index-field">
          <property name="name" value="id_domein"/>
        </element>
        <element action="add" uuid="e3972147-8627-4bca-966f-826c8cf63894" parent-uuid="57e0b604-cdbc-4ec8-82e0-eb93f0bc98ae" type="index-field">
          <property name="name" value="id_webform"/>
        </element>
        <element action="add" uuid="617802d2-a8dc-4c51-8e05-6e668183351b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Calendar"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="calendars"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="7d5a7c17-1601-4417-9cde-faf6c11ae20f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Domain"/>
          <property name="inverse-alias" value="domaintypes"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="domains"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domaintype"/>
        </element>
        <element action="add" uuid="bd3d3ecd-7847-4d8c-bf40-8c1decfb4a79" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Event"/>
          <property name="inverse-alias" value="calendars"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="events"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Calendar"/>
        </element>
        <element action="add" uuid="26ed87af-afdb-4d88-b4ba-72688be3780b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Memberfunction"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="memberfunctions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="4d340f46-2f02-44b0-b63f-0f8574fbb320" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Member"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="members"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="64f95f2e-f94d-4606-b0d6-13eada0bcd44" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Songitemfile"/>
          <property name="inverse-alias" value="songitems"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="songitemfiles"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Songitem"/>
        </element>
        <element action="add" uuid="e5c31f2b-c548-4f47-83ae-0e365dbcd0ff" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Songitem"/>
          <property name="inverse-alias" value="songs"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="songitems"/>
          <property name="to" value="\App\Models\Song"/>
        </element>
        <element action="add" uuid="d77acbf8-d163-428a-b6c3-5039f27fbb5e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Song"/>
          <property name="inverse-alias" value="domains"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="onDelete" value="CASCADE"/>
          <property name="onUpdate" value="NO ACTION"/>
          <property name="owner-alias" value="songs"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="c4de38a7-413b-4a40-8d82-8ce45ff94bc8" parent-uuid="617802d2-a8dc-4c51-8e05-6e668183351b" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="0ab8607e-176a-4ed5-be41-8420a92aa5ba" parent-uuid="7d5a7c17-1601-4417-9cde-faf6c11ae20f" type="association-field">
          <property name="from" value="domaintype_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b00f3d18-6d34-4149-90f7-2fc8553becc4" parent-uuid="bd3d3ecd-7847-4d8c-bf40-8c1decfb4a79" type="association-field">
          <property name="from" value="calendar_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="1d453e76-6561-427b-bd2c-ecfcf6becb06" parent-uuid="26ed87af-afdb-4d88-b4ba-72688be3780b" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="8b5f752a-a44b-458f-bfc3-6677bf200763" parent-uuid="4d340f46-2f02-44b0-b63f-0f8574fbb320" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="65dfaf01-a633-48c8-b1e6-8b4d308a1e84" parent-uuid="64f95f2e-f94d-4606-b0d6-13eada0bcd44" type="association-field">
          <property name="from" value="songitem_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="93f2258a-ad10-496c-a70c-b2a86f46cc8f" parent-uuid="e5c31f2b-c548-4f47-83ae-0e365dbcd0ff" type="association-field">
          <property name="from" value="song_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="211726f3-03b2-4892-872d-0d685537b04b" parent-uuid="d77acbf8-d163-428a-b6c3-5039f27fbb5e" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
      </revision>
      <revision uuid="f30e9c34-fcf8-4bbd-8b45-f07957c87d38" date="2024-01-14 19:43:36.727098" exportable="ignored">
        <element action="delete" uuid="f52d51af-2ff8-4a2d-964f-f08d504a5b73" parent-uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" type="index-field"/>
        <element action="delete" uuid="54ecf6a3-a285-4bbe-9dfb-2d8a319ef711" parent-uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" type="index-field"/>
        <element action="delete" uuid="1a3b34a2-b16b-4feb-b8b3-331349b73f78" parent-uuid="611bbdc1-de3d-407e-ba99-2f17f9de5e3a" type="index-field"/>
        <element action="delete" uuid="4cbefa87-8a94-4360-85ae-76cba044477f" parent-uuid="f6ce6ee8-74ec-4d43-87ff-3f55bd6ff55c" type="index-field"/>
        <element action="delete" uuid="18e9ffb2-4fb2-4dcb-8403-cc4b13376e6b" parent-uuid="3fe3f1d1-0d83-4389-91b7-cb09cb750931" type="index-field"/>
        <element action="delete" uuid="c5d45ee2-0988-48af-a712-06f090aae9ca" parent-uuid="abd43fa9-9d63-42c8-8266-3ad580c85b8c" type="index-field"/>
        <element action="delete" uuid="92e1df3b-8923-45ac-b876-ab14b830b97f" parent-uuid="287ed4bf-2b5a-4dba-932a-d5e12750f1ad" type="index-field"/>
        <element action="delete" uuid="5f30b504-f5d7-4dbd-909b-8604c0bd6fce" parent-uuid="23956d18-cf87-41fd-a695-395d8e64e00d" type="index-field"/>
        <element action="delete" uuid="e3972147-8627-4bca-966f-826c8cf63894" parent-uuid="57e0b604-cdbc-4ec8-82e0-eb93f0bc98ae" type="index-field"/>
        <element action="delete" uuid="4288267b-9b9e-4ff0-a93c-55b4ea6e4e6c" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="index"/>
        <element action="delete" uuid="611bbdc1-de3d-407e-ba99-2f17f9de5e3a" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="index"/>
        <element action="delete" uuid="f6ce6ee8-74ec-4d43-87ff-3f55bd6ff55c" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" type="index"/>
        <element action="delete" uuid="3fe3f1d1-0d83-4389-91b7-cb09cb750931" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" type="index"/>
        <element action="delete" uuid="abd43fa9-9d63-42c8-8266-3ad580c85b8c" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" type="index"/>
        <element action="delete" uuid="287ed4bf-2b5a-4dba-932a-d5e12750f1ad" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" type="index"/>
        <element action="delete" uuid="23956d18-cf87-41fd-a695-395d8e64e00d" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" type="index"/>
        <element action="delete" uuid="57e0b604-cdbc-4ec8-82e0-eb93f0bc98ae" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" type="index"/>
        <element action="delete" uuid="7bb1b684-8679-409d-90bb-b2ba28fc1431" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" type="field"/>
        <element action="delete" uuid="628f99a4-3679-4c58-8af2-eb11a4a858ea" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="7bb1b684-8679-409d-90bb-b2ba28fc1431" type="field"/>
        <element action="delete" uuid="9183264b-3ac7-445a-b520-e7bc331acd7a" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="628f99a4-3679-4c58-8af2-eb11a4a858ea" type="field"/>
        <element action="delete" uuid="d61f4ba6-11ac-468b-b261-bf7d6363f01c" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="9183264b-3ac7-445a-b520-e7bc331acd7a" type="field"/>
        <element action="delete" uuid="c253a5b9-e315-4d13-8452-f42a2f39a03c" parent-uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" sibling-uuid="d61f4ba6-11ac-468b-b261-bf7d6363f01c" type="field"/>
        <element action="delete" uuid="f3dee74f-e5a8-42c0-962f-7bbce1f37551" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" type="field"/>
        <element action="delete" uuid="0311b3b2-ce4e-4e50-87d7-06048b1443cf" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="f3dee74f-e5a8-42c0-962f-7bbce1f37551" type="field"/>
        <element action="delete" uuid="d4d43aef-2ed8-4f57-a35c-b9c1462b052e" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="0311b3b2-ce4e-4e50-87d7-06048b1443cf" type="field"/>
        <element action="delete" uuid="e919f76f-f7c0-4238-8654-330cc5cb1bf0" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="d4d43aef-2ed8-4f57-a35c-b9c1462b052e" type="field"/>
        <element action="delete" uuid="326179eb-96c4-48cd-97d2-31ff14f44705" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="e919f76f-f7c0-4238-8654-330cc5cb1bf0" type="field"/>
        <element action="delete" uuid="b855ff7a-1176-4555-9ff7-3ca4baf38782" parent-uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" sibling-uuid="326179eb-96c4-48cd-97d2-31ff14f44705" type="field"/>
        <element action="delete" uuid="3ce85e5f-9848-49fe-8564-62e1b04f916f" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" type="field"/>
        <element action="delete" uuid="e6fe0054-4cd1-4be4-807f-02c83a232356" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="3ce85e5f-9848-49fe-8564-62e1b04f916f" type="field"/>
        <element action="delete" uuid="68ea8057-fe7f-469f-bf05-4c5ebf7e4bd8" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="e6fe0054-4cd1-4be4-807f-02c83a232356" type="field"/>
        <element action="delete" uuid="e9df6346-0a9a-4264-be8a-89c96f014a2d" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="68ea8057-fe7f-469f-bf05-4c5ebf7e4bd8" type="field"/>
        <element action="delete" uuid="569443ec-ac17-443c-80d6-915ab1e3c2f2" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="e9df6346-0a9a-4264-be8a-89c96f014a2d" type="field"/>
        <element action="delete" uuid="cc685353-b5b8-4a64-988c-7861c7ebca27" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="569443ec-ac17-443c-80d6-915ab1e3c2f2" type="field"/>
        <element action="delete" uuid="bc7b3e12-9f63-46d1-9ed2-63b282be53a3" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="cc685353-b5b8-4a64-988c-7861c7ebca27" type="field"/>
        <element action="delete" uuid="84c16b8d-9bc3-4cf2-bc5a-4ed61891e5f7" parent-uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" sibling-uuid="bc7b3e12-9f63-46d1-9ed2-63b282be53a3" type="field"/>
        <element action="delete" uuid="d8a72553-f1e1-42f7-94d0-c64647bc3b4f" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" type="field"/>
        <element action="delete" uuid="c18a8e55-606d-481b-ac23-0966cc489069" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="d8a72553-f1e1-42f7-94d0-c64647bc3b4f" type="field"/>
        <element action="delete" uuid="db4cfb77-adcb-408b-b5de-d9486ebd3431" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="c18a8e55-606d-481b-ac23-0966cc489069" type="field"/>
        <element action="delete" uuid="4892631b-30c8-4d65-999b-a851b0cb5727" parent-uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" sibling-uuid="db4cfb77-adcb-408b-b5de-d9486ebd3431" type="field"/>
        <element action="delete" uuid="80cb3bd3-0623-4cd7-828e-1d5c444ae89f" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" type="field"/>
        <element action="delete" uuid="a2ed8495-de9a-4195-a948-524c9d11c6f2" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="80cb3bd3-0623-4cd7-828e-1d5c444ae89f" type="field"/>
        <element action="delete" uuid="0328fa1e-39ef-4e56-ba28-b77a7b7cb437" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="a2ed8495-de9a-4195-a948-524c9d11c6f2" type="field"/>
        <element action="delete" uuid="928f69d1-0ee8-4ee4-a363-5a8b4935a19b" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="0328fa1e-39ef-4e56-ba28-b77a7b7cb437" type="field"/>
        <element action="delete" uuid="a4d81cca-4d7e-417e-9a7c-994cd4dd8136" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="928f69d1-0ee8-4ee4-a363-5a8b4935a19b" type="field"/>
        <element action="delete" uuid="a94f405c-8cbe-42d7-8490-b13878e7b479" parent-uuid="51fb4335-8a24-4360-a32d-acd8a2433244" sibling-uuid="a4d81cca-4d7e-417e-9a7c-994cd4dd8136" type="field"/>
        <element action="delete" uuid="ebbe3748-b246-4a22-a5eb-6babe3f737b9" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" type="field"/>
        <element action="delete" uuid="f80cc17d-ae05-4d52-8f3a-9cc33b38cab3" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="ebbe3748-b246-4a22-a5eb-6babe3f737b9" type="field"/>
        <element action="delete" uuid="e63f890d-044e-475e-87b4-2de79e6aaca0" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="f80cc17d-ae05-4d52-8f3a-9cc33b38cab3" type="field"/>
        <element action="delete" uuid="7e3e564b-6644-4f8e-b3e5-b638e301a88f" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="e63f890d-044e-475e-87b4-2de79e6aaca0" type="field"/>
        <element action="delete" uuid="4bff53f1-2e43-48d7-881c-953aa139697e" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="7e3e564b-6644-4f8e-b3e5-b638e301a88f" type="field"/>
        <element action="delete" uuid="42a0afd0-6349-4141-950f-011b702d2377" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="4bff53f1-2e43-48d7-881c-953aa139697e" type="field"/>
        <element action="delete" uuid="608b3579-fd41-4627-9e5f-aa67b5212358" parent-uuid="3486427d-73f9-4133-96f8-4645ba654331" sibling-uuid="42a0afd0-6349-4141-950f-011b702d2377" type="field"/>
        <element action="delete" uuid="5492b961-60ff-44fe-a212-109e01b4642b" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" type="field"/>
        <element action="delete" uuid="104f29a4-46b6-404f-a29f-336d8a718107" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="5492b961-60ff-44fe-a212-109e01b4642b" type="field"/>
        <element action="delete" uuid="84a8841d-45ec-4e40-8b9f-51140b8520b8" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="104f29a4-46b6-404f-a29f-336d8a718107" type="field"/>
        <element action="delete" uuid="5b4aeb56-4b34-4fb2-9079-49114e65a893" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="84a8841d-45ec-4e40-8b9f-51140b8520b8" type="field"/>
        <element action="delete" uuid="04e18988-b772-4288-a51a-3976ef24e172" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="5b4aeb56-4b34-4fb2-9079-49114e65a893" type="field"/>
        <element action="delete" uuid="b4296a32-ce2a-47c6-b9dd-e62c48364812" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="04e18988-b772-4288-a51a-3976ef24e172" type="field"/>
        <element action="delete" uuid="6774f50c-96a7-460a-a075-4791b970afcd" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="b4296a32-ce2a-47c6-b9dd-e62c48364812" type="field"/>
        <element action="delete" uuid="cf2cc489-8758-4270-b5b0-95b2acd8b450" parent-uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" sibling-uuid="6774f50c-96a7-460a-a075-4791b970afcd" type="field"/>
        <element action="delete" uuid="7940edcc-683c-4c4f-86dc-6f20509ef9ce" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" type="field"/>
        <element action="delete" uuid="de1449fc-d4d0-4621-a221-f46feb331a91" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="7940edcc-683c-4c4f-86dc-6f20509ef9ce" type="field"/>
        <element action="delete" uuid="50b49183-fc8d-4a5f-ba18-7f604db19f23" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="de1449fc-d4d0-4621-a221-f46feb331a91" type="field"/>
        <element action="delete" uuid="0c6e4be8-ec32-4aca-997d-fa149cef7943" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="50b49183-fc8d-4a5f-ba18-7f604db19f23" type="field"/>
        <element action="delete" uuid="ccf39bbd-1b44-4762-bea6-c4c6fd7fb7d4" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="0c6e4be8-ec32-4aca-997d-fa149cef7943" type="field"/>
        <element action="delete" uuid="5f7e7927-c385-4d5f-bd7d-311ddf424baf" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ccf39bbd-1b44-4762-bea6-c4c6fd7fb7d4" type="field"/>
        <element action="delete" uuid="19d6ce20-c9ec-4e07-b673-3121dbcf2667" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="5f7e7927-c385-4d5f-bd7d-311ddf424baf" type="field"/>
        <element action="delete" uuid="33f1f07f-9325-4ee0-a5c6-09b73cb46cdb" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="19d6ce20-c9ec-4e07-b673-3121dbcf2667" type="field"/>
        <element action="delete" uuid="6fbe3f00-3b9f-4601-9311-d0ae4af81236" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="33f1f07f-9325-4ee0-a5c6-09b73cb46cdb" type="field"/>
        <element action="delete" uuid="8c352d49-cba7-464d-9a1b-f5877b1bd51f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="6fbe3f00-3b9f-4601-9311-d0ae4af81236" type="field"/>
        <element action="delete" uuid="bebd4fe5-ac36-4443-94fe-acab3c348bdb" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8c352d49-cba7-464d-9a1b-f5877b1bd51f" type="field"/>
        <element action="delete" uuid="e3333b36-fe2c-4eed-a167-b56c1d1f045b" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bebd4fe5-ac36-4443-94fe-acab3c348bdb" type="field"/>
        <element action="delete" uuid="8581eec8-ae4d-40b0-9ecd-5231d764b538" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="e3333b36-fe2c-4eed-a167-b56c1d1f045b" type="field"/>
        <element action="delete" uuid="a7606f18-a34c-47cb-a313-ef783da8273f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8581eec8-ae4d-40b0-9ecd-5231d764b538" type="field"/>
        <element action="delete" uuid="fd52fae7-8f5b-42cb-b76f-b349c840b30d" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="a7606f18-a34c-47cb-a313-ef783da8273f" type="field"/>
        <element action="delete" uuid="82ecfa60-2cba-4281-b444-6f64d64ca914" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="fd52fae7-8f5b-42cb-b76f-b349c840b30d" type="field"/>
        <element action="delete" uuid="17a2c52b-3b0d-4b90-85e3-191072bb75c1" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="82ecfa60-2cba-4281-b444-6f64d64ca914" type="field"/>
        <element action="delete" uuid="c546897a-ce94-481b-9d4e-7c50c23b5eb0" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="17a2c52b-3b0d-4b90-85e3-191072bb75c1" type="field"/>
        <element action="delete" uuid="07f0ab2c-961f-48b8-a774-8652871472b2" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="c546897a-ce94-481b-9d4e-7c50c23b5eb0" type="field"/>
        <element action="delete" uuid="81dbf7b6-01a6-4f26-b845-e018a8ed2b66" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="07f0ab2c-961f-48b8-a774-8652871472b2" type="field"/>
        <element action="delete" uuid="c1f25a1a-666f-4c57-a3ec-2fdbf314f810" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="81dbf7b6-01a6-4f26-b845-e018a8ed2b66" type="field"/>
        <element action="delete" uuid="cd451d81-6498-4039-8d3b-e5ce440c487c" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="c1f25a1a-666f-4c57-a3ec-2fdbf314f810" type="field"/>
        <element action="delete" uuid="3380eca0-59d2-4fe7-8e5c-28c63224507f" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="cd451d81-6498-4039-8d3b-e5ce440c487c" type="field"/>
        <element action="delete" uuid="ea1d551a-6564-41ee-ace1-e00fa18022cd" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="3380eca0-59d2-4fe7-8e5c-28c63224507f" type="field"/>
        <element action="delete" uuid="e25d41f4-7d02-4d7c-afeb-76c9c99bd72b" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ea1d551a-6564-41ee-ace1-e00fa18022cd" type="field"/>
        <element action="delete" uuid="07ad5a90-8b47-4804-a6ea-662401dc99a9" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="e25d41f4-7d02-4d7c-afeb-76c9c99bd72b" type="field"/>
        <element action="delete" uuid="fb1f4e7e-4605-48f4-bc50-8508efadba03" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="07ad5a90-8b47-4804-a6ea-662401dc99a9" type="field"/>
        <element action="delete" uuid="90636ac7-170e-4c54-bfdf-826c103d62d7" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="fb1f4e7e-4605-48f4-bc50-8508efadba03" type="field"/>
        <element action="delete" uuid="ec3af3bb-e422-46cc-866b-72c0fe1f2ed8" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="90636ac7-170e-4c54-bfdf-826c103d62d7" type="field"/>
        <element action="delete" uuid="d6ea16d3-803a-4ef3-a4b2-b93c713f26c8" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="ec3af3bb-e422-46cc-866b-72c0fe1f2ed8" type="field"/>
        <element action="delete" uuid="d41ff824-64b2-41b7-8c90-9fe1f93ef813" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="d6ea16d3-803a-4ef3-a4b2-b93c713f26c8" type="field"/>
        <element action="delete" uuid="bd024f07-15a9-442d-a1a1-3231c50d2bce" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="d41ff824-64b2-41b7-8c90-9fe1f93ef813" type="field"/>
        <element action="delete" uuid="bce2b4c4-7d9c-417f-84e7-a59842f34b88" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bd024f07-15a9-442d-a1a1-3231c50d2bce" type="field"/>
        <element action="delete" uuid="8d6c0c2f-0ac4-41ab-a353-053f9c9dcb63" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="bce2b4c4-7d9c-417f-84e7-a59842f34b88" type="field"/>
        <element action="delete" uuid="3cd6617a-e163-4662-8c09-83349ce56511" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="8d6c0c2f-0ac4-41ab-a353-053f9c9dcb63" type="field"/>
        <element action="delete" uuid="6b0edb54-6b3e-4478-a729-c5c4c53bdc06" parent-uuid="6585548f-164a-4056-bb5e-685545f2d882" sibling-uuid="3cd6617a-e163-4662-8c09-83349ce56511" type="field"/>
        <element action="delete" uuid="54bb3383-60e9-421a-9a0c-d511657de874" parent-uuid="454be441-0c95-4305-b051-390e970926c3" type="field"/>
        <element action="delete" uuid="4ba25a1e-cc63-4ce8-a53d-0ba8ea494efc" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="54bb3383-60e9-421a-9a0c-d511657de874" type="field"/>
        <element action="delete" uuid="c3d3ffca-0e15-4221-8d5b-838df495db64" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="4ba25a1e-cc63-4ce8-a53d-0ba8ea494efc" type="field"/>
        <element action="delete" uuid="6f481e01-44f5-47f9-99f8-29a299a107ed" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="c3d3ffca-0e15-4221-8d5b-838df495db64" type="field"/>
        <element action="delete" uuid="0702ed73-a688-4d89-961f-bf9ffcb1c65b" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="6f481e01-44f5-47f9-99f8-29a299a107ed" type="field"/>
        <element action="delete" uuid="011b8f1f-fd97-4323-91be-68dd6781ab64" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="0702ed73-a688-4d89-961f-bf9ffcb1c65b" type="field"/>
        <element action="delete" uuid="b4399f62-8c1b-4860-b3d2-991a80bd673f" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="011b8f1f-fd97-4323-91be-68dd6781ab64" type="field"/>
        <element action="delete" uuid="9b124997-6e1c-4ed4-bb3e-94829f2a45e5" parent-uuid="454be441-0c95-4305-b051-390e970926c3" sibling-uuid="b4399f62-8c1b-4860-b3d2-991a80bd673f" type="field"/>
        <element action="delete" uuid="b85bc84a-e0a3-41ed-8967-b9b6e46f76f1" parent-uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" type="field"/>
        <element action="delete" uuid="f636dd97-ccbe-485b-893a-9808fadb6415" parent-uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" sibling-uuid="b85bc84a-e0a3-41ed-8967-b9b6e46f76f1" type="field"/>
        <element action="delete" uuid="694bc95c-d288-43e2-a81e-b08ac0c36ecb" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" type="field"/>
        <element action="delete" uuid="1ce1e2af-0a0a-4cd4-b6ae-3dc352a56980" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="694bc95c-d288-43e2-a81e-b08ac0c36ecb" type="field"/>
        <element action="delete" uuid="93a4671e-69ac-4102-8878-cabea1d3d277" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="1ce1e2af-0a0a-4cd4-b6ae-3dc352a56980" type="field"/>
        <element action="delete" uuid="496706ae-b3ff-4b33-a450-6029ac7d690a" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="93a4671e-69ac-4102-8878-cabea1d3d277" type="field"/>
        <element action="delete" uuid="822b7121-1d86-40f5-bda2-5d5280254712" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="496706ae-b3ff-4b33-a450-6029ac7d690a" type="field"/>
        <element action="delete" uuid="4510b25b-6ed1-49b4-9ee8-769654d181db" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="822b7121-1d86-40f5-bda2-5d5280254712" type="field"/>
        <element action="delete" uuid="c377e116-f3ed-4492-bc8e-6258222d9bed" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="4510b25b-6ed1-49b4-9ee8-769654d181db" type="field"/>
        <element action="delete" uuid="87867dc5-1bd8-4afa-b949-1002c86a3aa6" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="c377e116-f3ed-4492-bc8e-6258222d9bed" type="field"/>
        <element action="delete" uuid="62079b9b-0e75-466d-ba1d-9678ec70a3b0" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="87867dc5-1bd8-4afa-b949-1002c86a3aa6" type="field"/>
        <element action="delete" uuid="b33497df-cec0-46bd-bd80-aef12ab8de8b" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="62079b9b-0e75-466d-ba1d-9678ec70a3b0" type="field"/>
        <element action="delete" uuid="88454642-12dc-4a11-9020-9dcd0940db1a" parent-uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" sibling-uuid="b33497df-cec0-46bd-bd80-aef12ab8de8b" type="field"/>
        <element action="delete" uuid="ee0a8553-2b7e-4a58-adf4-411d3440ecd9" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" type="field"/>
        <element action="delete" uuid="7cd91459-4dc1-49a0-9a0b-badf80f1c921" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="ee0a8553-2b7e-4a58-adf4-411d3440ecd9" type="field"/>
        <element action="delete" uuid="ba00be97-4268-41f9-af70-5aa0e7f69ca8" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="7cd91459-4dc1-49a0-9a0b-badf80f1c921" type="field"/>
        <element action="delete" uuid="c28f6f3a-f16d-40a5-8487-f0d93ac29b6b" parent-uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" sibling-uuid="ba00be97-4268-41f9-af70-5aa0e7f69ca8" type="field"/>
        <element action="delete" uuid="aea1d2c2-97b0-478a-8ed4-e5197af6fdd0" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" type="field"/>
        <element action="delete" uuid="0d1f8f2b-55ed-4f78-9bca-5c97a36cfdab" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="aea1d2c2-97b0-478a-8ed4-e5197af6fdd0" type="field"/>
        <element action="delete" uuid="8997d9c4-6df4-4fbd-9212-70dc163edd7f" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="0d1f8f2b-55ed-4f78-9bca-5c97a36cfdab" type="field"/>
        <element action="delete" uuid="d1b90088-9ec0-449c-ae7a-3b881fb19f61" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="8997d9c4-6df4-4fbd-9212-70dc163edd7f" type="field"/>
        <element action="delete" uuid="f3124eb2-d4d7-4a1d-bb52-39efb6feb67c" parent-uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" sibling-uuid="d1b90088-9ec0-449c-ae7a-3b881fb19f61" type="field"/>
        <element action="delete" uuid="1ae2624e-8a4f-486e-87a1-efa211373a36" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" type="field"/>
        <element action="delete" uuid="52a94217-3251-4ace-a9e4-ddda9cdf3792" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="1ae2624e-8a4f-486e-87a1-efa211373a36" type="field"/>
        <element action="delete" uuid="72057204-4d8c-4772-bb78-50bd38aca301" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="52a94217-3251-4ace-a9e4-ddda9cdf3792" type="field"/>
        <element action="delete" uuid="1d8ac20d-6253-4833-ba11-cfdd2fd03cfc" parent-uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" sibling-uuid="72057204-4d8c-4772-bb78-50bd38aca301" type="field"/>
        <element action="delete" uuid="09585620-d9f5-4751-802e-b60da0ebaeca" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" type="field"/>
        <element action="delete" uuid="a458dd59-049b-46d8-9915-28e00b27bdae" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="09585620-d9f5-4751-802e-b60da0ebaeca" type="field"/>
        <element action="delete" uuid="0fb1dff5-9a42-43e3-aba4-c939148b6425" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="a458dd59-049b-46d8-9915-28e00b27bdae" type="field"/>
        <element action="delete" uuid="e39f954d-c47a-4784-9cd3-840e2ce9aa3f" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="0fb1dff5-9a42-43e3-aba4-c939148b6425" type="field"/>
        <element action="delete" uuid="e3ef5648-6d32-42c9-8636-c61f8e1759fb" parent-uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" sibling-uuid="e39f954d-c47a-4784-9cd3-840e2ce9aa3f" type="field"/>
        <element action="delete" uuid="657eff54-80b6-4bf7-96c4-3dfa869a7bcf" parent-uuid="3e835401-9d64-4134-ac69-80d9791dcd96" type="field"/>
        <element action="delete" uuid="7d088907-19d7-4d9f-b026-e531c904dc2c" parent-uuid="3e835401-9d64-4134-ac69-80d9791dcd96" sibling-uuid="657eff54-80b6-4bf7-96c4-3dfa869a7bcf" type="field"/>
        <element action="delete" uuid="00465a63-e2e3-48dd-b539-fd82d84b7332" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" type="field"/>
        <element action="delete" uuid="b34ad2e0-a3f6-4182-88e8-96139db3e1ae" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" sibling-uuid="00465a63-e2e3-48dd-b539-fd82d84b7332" type="field"/>
        <element action="delete" uuid="6365d17e-b0d2-46fa-90c9-7f2be9b71614" parent-uuid="10dd906c-e63a-4959-8018-348d97ada592" sibling-uuid="b34ad2e0-a3f6-4182-88e8-96139db3e1ae" type="field"/>
        <element action="delete" uuid="22a30273-5e14-45fe-81a2-bbc15477073d" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" type="field"/>
        <element action="delete" uuid="7758d7d6-7b6d-4de1-adb6-82ee0e067adb" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="22a30273-5e14-45fe-81a2-bbc15477073d" type="field"/>
        <element action="delete" uuid="0661bbed-8ee7-4809-8748-36c50ccede55" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="7758d7d6-7b6d-4de1-adb6-82ee0e067adb" type="field"/>
        <element action="delete" uuid="06e16532-734d-4aac-9900-b6ba49fec65e" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="0661bbed-8ee7-4809-8748-36c50ccede55" type="field"/>
        <element action="delete" uuid="96536e9d-a2e7-4c1f-bcb5-bb6b8fd28b9d" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="06e16532-734d-4aac-9900-b6ba49fec65e" type="field"/>
        <element action="delete" uuid="59be2e1e-5e16-4988-8eeb-f01265dc87f6" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="96536e9d-a2e7-4c1f-bcb5-bb6b8fd28b9d" type="field"/>
        <element action="delete" uuid="353cb2ed-9a22-4bba-bbe3-a41972eca2a1" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="59be2e1e-5e16-4988-8eeb-f01265dc87f6" type="field"/>
        <element action="delete" uuid="6a53f38b-4032-4877-b1aa-0d77448a4e26" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="353cb2ed-9a22-4bba-bbe3-a41972eca2a1" type="field"/>
        <element action="delete" uuid="e6521f9c-dcdf-4d68-8d8c-5144228814ff" parent-uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" sibling-uuid="6a53f38b-4032-4877-b1aa-0d77448a4e26" type="field"/>
        <element action="delete" uuid="2c573faf-4fe6-411a-bf78-49bf79aefb4c" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" type="field"/>
        <element action="delete" uuid="0a7c407b-668d-4700-b04c-c227876b6b48" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="2c573faf-4fe6-411a-bf78-49bf79aefb4c" type="field"/>
        <element action="delete" uuid="2f27dfff-f480-43e6-b2f3-9f260a4b369a" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="0a7c407b-668d-4700-b04c-c227876b6b48" type="field"/>
        <element action="delete" uuid="401fb08e-edd2-477b-96f5-de2fa1bea961" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="2f27dfff-f480-43e6-b2f3-9f260a4b369a" type="field"/>
        <element action="delete" uuid="50b73174-94fc-4ebd-9962-9d8f7c932136" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="401fb08e-edd2-477b-96f5-de2fa1bea961" type="field"/>
        <element action="delete" uuid="8819b19b-de40-4fe3-a60d-5a4f9275fda4" parent-uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" sibling-uuid="50b73174-94fc-4ebd-9962-9d8f7c932136" type="field"/>
        <element action="delete" uuid="463b6889-26b8-44f0-823e-f287b4ad11a9" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" type="field"/>
        <element action="delete" uuid="d7b61f35-0a6c-4572-8d25-4fc8aca0cb37" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="463b6889-26b8-44f0-823e-f287b4ad11a9" type="field"/>
        <element action="delete" uuid="78a98c94-45d4-4fb2-834f-5e502959093b" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="d7b61f35-0a6c-4572-8d25-4fc8aca0cb37" type="field"/>
        <element action="delete" uuid="7df816f7-f569-461b-bbfc-a16bc48232e9" parent-uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" sibling-uuid="78a98c94-45d4-4fb2-834f-5e502959093b" type="field"/>
        <element action="delete" uuid="5b84f8ac-bba2-49de-a137-efbbf1dd3751" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" type="field"/>
        <element action="delete" uuid="dc2a4aba-51e7-4273-a40e-dc7d273f398e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="5b84f8ac-bba2-49de-a137-efbbf1dd3751" type="field"/>
        <element action="delete" uuid="33d12be4-44d7-4eb5-92a3-66b2c6961bde" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="dc2a4aba-51e7-4273-a40e-dc7d273f398e" type="field"/>
        <element action="delete" uuid="1271a38e-b93a-4117-afb9-81b297a7da28" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="33d12be4-44d7-4eb5-92a3-66b2c6961bde" type="field"/>
        <element action="delete" uuid="286676b3-9782-449c-90ea-68b93e1bd22e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="1271a38e-b93a-4117-afb9-81b297a7da28" type="field"/>
        <element action="delete" uuid="14e31992-07f3-4fe3-a4f9-03718087074e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="286676b3-9782-449c-90ea-68b93e1bd22e" type="field"/>
        <element action="delete" uuid="63d9e738-46e3-461d-a40e-496e805458c2" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="14e31992-07f3-4fe3-a4f9-03718087074e" type="field"/>
        <element action="delete" uuid="b755137a-5d46-41ea-8682-cb3317ec6673" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="63d9e738-46e3-461d-a40e-496e805458c2" type="field"/>
        <element action="delete" uuid="36911513-e69f-4d6d-96a3-fffbb304f5b1" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="b755137a-5d46-41ea-8682-cb3317ec6673" type="field"/>
        <element action="delete" uuid="0cc44858-c422-445f-b899-bbb962c65020" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="36911513-e69f-4d6d-96a3-fffbb304f5b1" type="field"/>
        <element action="delete" uuid="0552d56f-ff13-4142-ab21-a6e423512a68" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0cc44858-c422-445f-b899-bbb962c65020" type="field"/>
        <element action="delete" uuid="cdb3c559-ae3c-4070-b2b1-56b7d52a1795" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0552d56f-ff13-4142-ab21-a6e423512a68" type="field"/>
        <element action="delete" uuid="81de3cfc-5fd0-4d6d-bfe2-cd9f7aa6ecb2" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="cdb3c559-ae3c-4070-b2b1-56b7d52a1795" type="field"/>
        <element action="delete" uuid="6be3df92-eaa3-49d8-8230-46f528a2977a" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="81de3cfc-5fd0-4d6d-bfe2-cd9f7aa6ecb2" type="field"/>
        <element action="delete" uuid="4f23e376-395a-4360-b78b-1fc19cabebad" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="6be3df92-eaa3-49d8-8230-46f528a2977a" type="field"/>
        <element action="delete" uuid="92888e7f-16e2-42fb-9e84-941f6e2ef3d0" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="4f23e376-395a-4360-b78b-1fc19cabebad" type="field"/>
        <element action="delete" uuid="488ccc61-c0f4-44da-b35f-2c26ac91a3cd" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="92888e7f-16e2-42fb-9e84-941f6e2ef3d0" type="field"/>
        <element action="delete" uuid="bb1ce1df-63b6-45d4-a73e-f77a5d61aea3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="488ccc61-c0f4-44da-b35f-2c26ac91a3cd" type="field"/>
        <element action="delete" uuid="32dbc17d-1eb0-43bd-8571-7f64b203a2a0" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="bb1ce1df-63b6-45d4-a73e-f77a5d61aea3" type="field"/>
        <element action="delete" uuid="f8252c86-9b02-4336-8ebc-01972d43cebf" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="32dbc17d-1eb0-43bd-8571-7f64b203a2a0" type="field"/>
        <element action="delete" uuid="902de522-a0be-4457-b3e0-803e94a3e7b4" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="f8252c86-9b02-4336-8ebc-01972d43cebf" type="field"/>
        <element action="delete" uuid="2467a5c8-a8e2-4a9d-baac-6a8501d74812" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="902de522-a0be-4457-b3e0-803e94a3e7b4" type="field"/>
        <element action="delete" uuid="eab3a2fd-c787-4992-b3f5-6150493f02bd" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="2467a5c8-a8e2-4a9d-baac-6a8501d74812" type="field"/>
        <element action="delete" uuid="0b09e87d-62ff-4b8e-a175-fc4dd7bd5fb3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="eab3a2fd-c787-4992-b3f5-6150493f02bd" type="field"/>
        <element action="delete" uuid="f1b6b41d-9089-4d17-a2a8-d292939c9d8e" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0b09e87d-62ff-4b8e-a175-fc4dd7bd5fb3" type="field"/>
        <element action="delete" uuid="24fe7619-b59d-410b-9de0-8c8bf2f20d6c" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="f1b6b41d-9089-4d17-a2a8-d292939c9d8e" type="field"/>
        <element action="delete" uuid="69486b7b-b9a6-4fbf-be0c-9877ab403e3a" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="24fe7619-b59d-410b-9de0-8c8bf2f20d6c" type="field"/>
        <element action="delete" uuid="c3c9c2e1-e2a5-480e-80d7-1cdb0823e9a3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="69486b7b-b9a6-4fbf-be0c-9877ab403e3a" type="field"/>
        <element action="delete" uuid="0e01c6c0-d87b-49a3-81e2-c4a7bbfd1de3" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="c3c9c2e1-e2a5-480e-80d7-1cdb0823e9a3" type="field"/>
        <element action="delete" uuid="973d7774-21ec-4ff5-bd98-789c2b90ae64" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="0e01c6c0-d87b-49a3-81e2-c4a7bbfd1de3" type="field"/>
        <element action="delete" uuid="3aff6f14-8a42-43fe-80d3-63c3a0829e46" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="973d7774-21ec-4ff5-bd98-789c2b90ae64" type="field"/>
        <element action="delete" uuid="f0a3d21e-6f1a-4f76-a6cf-ebc3a5553e03" parent-uuid="829ce0e0-6079-426f-9097-508acae5d0c8" sibling-uuid="3aff6f14-8a42-43fe-80d3-63c3a0829e46" type="field"/>
        <element action="delete" uuid="caad7105-10bc-4333-993d-6eefddaf68a1" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" type="field"/>
        <element action="delete" uuid="8010da92-d54b-4297-9874-e8bf12c7cb1e" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="caad7105-10bc-4333-993d-6eefddaf68a1" type="field"/>
        <element action="delete" uuid="b8be86a6-f803-4c7d-8756-208f68ba0cd3" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="8010da92-d54b-4297-9874-e8bf12c7cb1e" type="field"/>
        <element action="delete" uuid="1126ccd2-91c2-4925-8162-8a8f0eecb9e7" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="b8be86a6-f803-4c7d-8756-208f68ba0cd3" type="field"/>
        <element action="delete" uuid="180b9aea-4aa2-404a-b299-141ef1f5b89f" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="1126ccd2-91c2-4925-8162-8a8f0eecb9e7" type="field"/>
        <element action="delete" uuid="75bbabeb-943d-4644-9b54-1783bc354aec" parent-uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" sibling-uuid="180b9aea-4aa2-404a-b299-141ef1f5b89f" type="field"/>
        <element action="delete" uuid="0151e606-390e-4919-9eb5-ad31163d2e77" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" type="field"/>
        <element action="delete" uuid="a608d944-19c1-4b9d-8620-e76a80b61470" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="0151e606-390e-4919-9eb5-ad31163d2e77" type="field"/>
        <element action="delete" uuid="fba6c013-6bab-4a35-98fe-56225eec1e05" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="a608d944-19c1-4b9d-8620-e76a80b61470" type="field"/>
        <element action="delete" uuid="25d4041f-1249-47f5-b306-09dbd822365f" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="fba6c013-6bab-4a35-98fe-56225eec1e05" type="field"/>
        <element action="delete" uuid="dd9da9de-bf7e-49f4-9844-99286bb250f1" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="25d4041f-1249-47f5-b306-09dbd822365f" type="field"/>
        <element action="delete" uuid="ba6077d4-b15d-4b4d-9158-ec6db9e2e44e" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="dd9da9de-bf7e-49f4-9844-99286bb250f1" type="field"/>
        <element action="delete" uuid="ba888559-82f0-4b12-842c-0580fdc448a2" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="ba6077d4-b15d-4b4d-9158-ec6db9e2e44e" type="field"/>
        <element action="delete" uuid="52ef3e6c-96a3-4b6e-b7c1-6e12b458838d" parent-uuid="1712273f-edb8-4272-a266-71bf382ae475" sibling-uuid="ba888559-82f0-4b12-842c-0580fdc448a2" type="field"/>
        <element action="delete" uuid="a0fb6758-6db3-4414-998d-0f9b51623e84" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" type="field"/>
        <element action="delete" uuid="3afeb924-2065-495b-9cd2-025a91ff1beb" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="a0fb6758-6db3-4414-998d-0f9b51623e84" type="field"/>
        <element action="delete" uuid="ddd59297-e4b5-41ce-8e56-10c23de6b7db" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="3afeb924-2065-495b-9cd2-025a91ff1beb" type="field"/>
        <element action="delete" uuid="b0576c88-d7b1-4896-be2c-34b45cfa17b7" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="ddd59297-e4b5-41ce-8e56-10c23de6b7db" type="field"/>
        <element action="delete" uuid="1afee284-ce46-4ca3-af0c-11210ff41009" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="b0576c88-d7b1-4896-be2c-34b45cfa17b7" type="field"/>
        <element action="delete" uuid="f0d71e96-e6df-4673-8153-9fd91d66fb69" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="1afee284-ce46-4ca3-af0c-11210ff41009" type="field"/>
        <element action="delete" uuid="3724896b-66b1-4fbf-8a2a-e1180b677b90" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="f0d71e96-e6df-4673-8153-9fd91d66fb69" type="field"/>
        <element action="delete" uuid="83e1bd08-8336-4e76-a435-abe057beef5a" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="3724896b-66b1-4fbf-8a2a-e1180b677b90" type="field"/>
        <element action="delete" uuid="38099a04-afac-4cbd-9139-198574bf19ec" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="83e1bd08-8336-4e76-a435-abe057beef5a" type="field"/>
        <element action="delete" uuid="cf78edea-76b7-4916-9bf5-9d1c28af01db" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="38099a04-afac-4cbd-9139-198574bf19ec" type="field"/>
        <element action="delete" uuid="202a2d4d-8cf4-40b0-956f-6e93bb2c0a21" parent-uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" sibling-uuid="cf78edea-76b7-4916-9bf5-9d1c28af01db" type="field"/>
        <element action="delete" uuid="45f9630c-28fe-4994-891e-e88cbbf05ac5" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" type="field"/>
        <element action="delete" uuid="2f1da3a2-61cd-4060-bf63-1488ac8faecd" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="45f9630c-28fe-4994-891e-e88cbbf05ac5" type="field"/>
        <element action="delete" uuid="f069f0e1-e1a5-45d3-b7a5-a5ca993ff0d8" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="2f1da3a2-61cd-4060-bf63-1488ac8faecd" type="field"/>
        <element action="delete" uuid="da27ed38-be00-4b43-9643-3de303dfaa33" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="f069f0e1-e1a5-45d3-b7a5-a5ca993ff0d8" type="field"/>
        <element action="delete" uuid="71f8bc99-e020-4d4d-ba18-b2f25c3fe1c2" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="da27ed38-be00-4b43-9643-3de303dfaa33" type="field"/>
        <element action="delete" uuid="38d54a11-26bd-4b1d-85bf-54de87a351e6" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="71f8bc99-e020-4d4d-ba18-b2f25c3fe1c2" type="field"/>
        <element action="delete" uuid="c0371173-e603-4133-880f-61c8562acf64" parent-uuid="7ae11b79-658c-4be9-bb30-909d106ed581" sibling-uuid="38d54a11-26bd-4b1d-85bf-54de87a351e6" type="field"/>
        <element action="delete" uuid="21a750ca-6ded-4933-8175-6e530a6e4708" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" type="field"/>
        <element action="delete" uuid="6848b2d1-62eb-449a-9270-************" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="21a750ca-6ded-4933-8175-6e530a6e4708" type="field"/>
        <element action="delete" uuid="2d908445-4e70-4381-ad4b-15b3cbf86b24" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="6848b2d1-62eb-449a-9270-************" type="field"/>
        <element action="delete" uuid="59db3129-4b61-4ebf-bd36-7c94063e443a" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="2d908445-4e70-4381-ad4b-15b3cbf86b24" type="field"/>
        <element action="delete" uuid="1691dae9-6c3a-4b4f-8ef0-a175989d74d5" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="59db3129-4b61-4ebf-bd36-7c94063e443a" type="field"/>
        <element action="delete" uuid="a918c3ed-e07e-40de-b256-78a69011375f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="1691dae9-6c3a-4b4f-8ef0-a175989d74d5" type="field"/>
        <element action="delete" uuid="41591aa9-7352-47e1-881e-0b825a4eaa49" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="a918c3ed-e07e-40de-b256-78a69011375f" type="field"/>
        <element action="delete" uuid="8d64b0b1-5efb-42d9-b2c6-4b8a7d677e0f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="41591aa9-7352-47e1-881e-0b825a4eaa49" type="field"/>
        <element action="delete" uuid="42e26651-0dd9-4962-bc25-1fe912422578" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="8d64b0b1-5efb-42d9-b2c6-4b8a7d677e0f" type="field"/>
        <element action="delete" uuid="42bce263-ef64-4cd7-9692-16a43f156871" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="42e26651-0dd9-4962-bc25-1fe912422578" type="field"/>
        <element action="delete" uuid="99e98933-e92e-4ab4-8da0-ff68dfb6300f" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="42bce263-ef64-4cd7-9692-16a43f156871" type="field"/>
        <element action="delete" uuid="1b6e4a86-e9e3-4ec0-a650-aa3c34ef8a91" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="99e98933-e92e-4ab4-8da0-ff68dfb6300f" type="field"/>
        <element action="delete" uuid="239b72be-342a-413b-8f32-b252a7ee7c38" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="1b6e4a86-e9e3-4ec0-a650-aa3c34ef8a91" type="field"/>
        <element action="delete" uuid="6751ab47-1a80-4749-837e-879b35e2f461" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="239b72be-342a-413b-8f32-b252a7ee7c38" type="field"/>
        <element action="delete" uuid="809f701b-4b39-4474-be86-a1233e1ed207" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="6751ab47-1a80-4749-837e-879b35e2f461" type="field"/>
        <element action="delete" uuid="ed79f728-cfa4-46b8-bd3d-6ebdf7663cb4" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="809f701b-4b39-4474-be86-a1233e1ed207" type="field"/>
        <element action="delete" uuid="cb4b8b08-5a97-4808-b093-29f15152b65b" parent-uuid="ab568cc9-259d-4657-9554-972d9374935e" sibling-uuid="ed79f728-cfa4-46b8-bd3d-6ebdf7663cb4" type="field"/>
        <element action="delete" uuid="21520d9f-17e5-4b40-a56b-c5718b7c4c8a" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" type="field"/>
        <element action="delete" uuid="fa414586-644b-49ea-8bb3-d3e663d01184" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="21520d9f-17e5-4b40-a56b-c5718b7c4c8a" type="field"/>
        <element action="delete" uuid="43ab29dc-ea6a-4d7b-97f6-41ca7bf542c7" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="fa414586-644b-49ea-8bb3-d3e663d01184" type="field"/>
        <element action="delete" uuid="fa806905-166f-41f0-a945-7e23631a5734" parent-uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" sibling-uuid="43ab29dc-ea6a-4d7b-97f6-41ca7bf542c7" type="field"/>
        <element action="delete" uuid="d51484ff-e64b-41c8-b37b-288cf819de5e" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" type="field"/>
        <element action="delete" uuid="70b545d4-1f81-4499-b4f0-da64802bce8e" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="d51484ff-e64b-41c8-b37b-288cf819de5e" type="field"/>
        <element action="delete" uuid="250e2f7e-6236-4078-a405-c6f7b4e47c2f" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="70b545d4-1f81-4499-b4f0-da64802bce8e" type="field"/>
        <element action="delete" uuid="425cb305-64ad-4eab-97a5-4d4cd147db57" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="250e2f7e-6236-4078-a405-c6f7b4e47c2f" type="field"/>
        <element action="delete" uuid="6046088e-a8c4-4d11-9d5c-83a2f0bd4583" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="425cb305-64ad-4eab-97a5-4d4cd147db57" type="field"/>
        <element action="delete" uuid="ff1775d2-1e87-4b27-abc6-8e9fc576c90f" parent-uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" sibling-uuid="6046088e-a8c4-4d11-9d5c-83a2f0bd4583" type="field"/>
        <element action="delete" uuid="b5bffab3-45b0-4c3b-8805-f0e00c682897" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" type="field"/>
        <element action="delete" uuid="5b8cef40-c92a-4062-bfd1-ec2584c403f8" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="b5bffab3-45b0-4c3b-8805-f0e00c682897" type="field"/>
        <element action="delete" uuid="cab35cee-c59b-42cd-9421-c91f6f19d2f3" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="5b8cef40-c92a-4062-bfd1-ec2584c403f8" type="field"/>
        <element action="delete" uuid="f766f99c-3930-469e-9469-11bbc5c54a97" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="cab35cee-c59b-42cd-9421-c91f6f19d2f3" type="field"/>
        <element action="delete" uuid="de3a6236-fa5a-49b1-8585-f55a5b27eb9c" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="f766f99c-3930-469e-9469-11bbc5c54a97" type="field"/>
        <element action="delete" uuid="43264548-6aa2-4c03-b0d8-857e7fcbe417" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="de3a6236-fa5a-49b1-8585-f55a5b27eb9c" type="field"/>
        <element action="delete" uuid="ede11297-8efc-4e78-a74d-d67275180e9f" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="43264548-6aa2-4c03-b0d8-857e7fcbe417" type="field"/>
        <element action="delete" uuid="e22553ca-2ff1-4181-8bcd-9bb036a76c98" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="ede11297-8efc-4e78-a74d-d67275180e9f" type="field"/>
        <element action="delete" uuid="db6dd027-a35b-4777-a905-f4e6e889015a" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="e22553ca-2ff1-4181-8bcd-9bb036a76c98" type="field"/>
        <element action="delete" uuid="91fc8cb0-ae78-4922-8aaa-f826ddfd42bb" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="db6dd027-a35b-4777-a905-f4e6e889015a" type="field"/>
        <element action="delete" uuid="d005fcc3-3a1b-4c7c-afd9-11fb79cc303a" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="91fc8cb0-ae78-4922-8aaa-f826ddfd42bb" type="field"/>
        <element action="delete" uuid="fb936629-fc7e-44f2-a51d-72fd2d71cdc2" parent-uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" sibling-uuid="d005fcc3-3a1b-4c7c-afd9-11fb79cc303a" type="field"/>
        <element action="delete" uuid="ec04e58f-9811-4799-ac3c-c19387fd32ad" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" type="field"/>
        <element action="delete" uuid="5a0e65cb-c358-4860-a8d2-9d34eb4357fd" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="ec04e58f-9811-4799-ac3c-c19387fd32ad" type="field"/>
        <element action="delete" uuid="dfd1faed-cca6-490c-8e43-23c5db4144bf" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="5a0e65cb-c358-4860-a8d2-9d34eb4357fd" type="field"/>
        <element action="delete" uuid="af3f0bfa-b53c-443f-807b-ae80b670893d" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="dfd1faed-cca6-490c-8e43-23c5db4144bf" type="field"/>
        <element action="delete" uuid="028a8746-4392-4b1c-9af4-ce8a67cdb663" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="af3f0bfa-b53c-443f-807b-ae80b670893d" type="field"/>
        <element action="delete" uuid="be6dee56-c2a6-4e89-b388-58f0abb43e84" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="028a8746-4392-4b1c-9af4-ce8a67cdb663" type="field"/>
        <element action="delete" uuid="a2c6817e-d06e-4ff3-951e-bab7c56dacdf" parent-uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" sibling-uuid="be6dee56-c2a6-4e89-b388-58f0abb43e84" type="field"/>
        <element action="delete" uuid="0e6d55ef-38e2-43af-9b2c-501383cf3084" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" type="field"/>
        <element action="delete" uuid="6941571c-105d-433e-a2f1-09e0dc5f6d33" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="0e6d55ef-38e2-43af-9b2c-501383cf3084" type="field"/>
        <element action="delete" uuid="e3a9d02c-3ac9-4ff1-86dc-a2fba003f869" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="6941571c-105d-433e-a2f1-09e0dc5f6d33" type="field"/>
        <element action="delete" uuid="e8c607d6-6971-44de-83c7-5c6569f3a9c3" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="e3a9d02c-3ac9-4ff1-86dc-a2fba003f869" type="field"/>
        <element action="delete" uuid="97d8c3a8-bfda-4053-a715-1b726708a852" parent-uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" sibling-uuid="e8c607d6-6971-44de-83c7-5c6569f3a9c3" type="field"/>
        <element action="delete" uuid="622fb3b6-070e-45ef-8974-51ef403a3d84" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" type="field"/>
        <element action="delete" uuid="28d30705-5def-482e-a115-995034a298b5" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="622fb3b6-070e-45ef-8974-51ef403a3d84" type="field"/>
        <element action="delete" uuid="3071faca-8c26-4db8-8ed7-b882604c1776" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="28d30705-5def-482e-a115-995034a298b5" type="field"/>
        <element action="delete" uuid="a6c13841-57cb-4307-8cfb-98ad9e4cee19" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="3071faca-8c26-4db8-8ed7-b882604c1776" type="field"/>
        <element action="delete" uuid="dc218146-d079-4c87-8e44-b82bbb34589c" parent-uuid="46682376-f61c-4572-a9e3-168c0e4c9610" sibling-uuid="a6c13841-57cb-4307-8cfb-98ad9e4cee19" type="field"/>
        <element action="delete" uuid="e60e041b-e8bc-4b56-95de-f5fd24ad5462" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" type="field"/>
        <element action="delete" uuid="0eaabf16-56a1-46ea-baa9-2c7321b1844e" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="e60e041b-e8bc-4b56-95de-f5fd24ad5462" type="field"/>
        <element action="delete" uuid="b335630d-7db0-4b82-be9f-912d1a839d8c" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="0eaabf16-56a1-46ea-baa9-2c7321b1844e" type="field"/>
        <element action="delete" uuid="39156b81-0371-4f1b-bf28-83043b1fcb6c" parent-uuid="a1794938-ae91-4a9c-934c-be9182e9b308" sibling-uuid="b335630d-7db0-4b82-be9f-912d1a839d8c" type="field"/>
        <element action="delete" uuid="e9e7c2bc-57a8-4101-957a-7597ed4bcf3a" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" type="field"/>
        <element action="delete" uuid="74a8f1f2-e1a1-475f-9e2b-ab47a4fcbc3d" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="e9e7c2bc-57a8-4101-957a-7597ed4bcf3a" type="field"/>
        <element action="delete" uuid="78b40280-72a9-488a-b253-5a12e9654b34" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="74a8f1f2-e1a1-475f-9e2b-ab47a4fcbc3d" type="field"/>
        <element action="delete" uuid="db67dc47-eb6e-45c7-88af-3baa96d50043" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="78b40280-72a9-488a-b253-5a12e9654b34" type="field"/>
        <element action="delete" uuid="e6df4719-1580-4536-9fa3-3053472dd1c2" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="db67dc47-eb6e-45c7-88af-3baa96d50043" type="field"/>
        <element action="delete" uuid="9be13c91-3788-4dd7-a24d-b5b2e3122b02" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="e6df4719-1580-4536-9fa3-3053472dd1c2" type="field"/>
        <element action="delete" uuid="0c648c68-6695-4ac4-89e0-2df413e0617b" parent-uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" sibling-uuid="9be13c91-3788-4dd7-a24d-b5b2e3122b02" type="field"/>
        <element action="delete" uuid="141c9c92-95aa-4f19-aa10-2af43c13de22" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" type="field"/>
        <element action="delete" uuid="09120c0c-a148-403f-91f9-a9e1cdc9f51a" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="141c9c92-95aa-4f19-aa10-2af43c13de22" type="field"/>
        <element action="delete" uuid="26abed5c-6a9a-4b66-b399-059bfd46f424" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="09120c0c-a148-403f-91f9-a9e1cdc9f51a" type="field"/>
        <element action="delete" uuid="c7ae4cca-766b-4983-8edb-b7f9ef898bbb" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="26abed5c-6a9a-4b66-b399-059bfd46f424" type="field"/>
        <element action="delete" uuid="4896c4e9-41b5-4bf1-acdd-cd688fb5cac5" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="c7ae4cca-766b-4983-8edb-b7f9ef898bbb" type="field"/>
        <element action="delete" uuid="0c039ccf-6b1b-4e8f-b45c-5c3d0b0e3bf1" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="4896c4e9-41b5-4bf1-acdd-cd688fb5cac5" type="field"/>
        <element action="delete" uuid="e6d143ae-5ae7-44fe-b7e2-2fb48ba5395f" parent-uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" sibling-uuid="0c039ccf-6b1b-4e8f-b45c-5c3d0b0e3bf1" type="field"/>
        <element action="delete" uuid="ddc047d7-3f8f-482e-bb03-64060574a0c4" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" type="field"/>
        <element action="delete" uuid="8c0d45a8-7c30-49f8-9488-02288167a3a6" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="ddc047d7-3f8f-482e-bb03-64060574a0c4" type="field"/>
        <element action="delete" uuid="cfdda226-a6fe-46f7-894b-cfc048fc9272" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="8c0d45a8-7c30-49f8-9488-02288167a3a6" type="field"/>
        <element action="delete" uuid="197993d1-491d-4a8d-a875-455221357f02" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="cfdda226-a6fe-46f7-894b-cfc048fc9272" type="field"/>
        <element action="delete" uuid="e24a5704-3710-4eaa-951b-b78f0bbd3daa" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="197993d1-491d-4a8d-a875-455221357f02" type="field"/>
        <element action="delete" uuid="a4f28047-5a75-4c3e-8582-af988626e81f" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="e24a5704-3710-4eaa-951b-b78f0bbd3daa" type="field"/>
        <element action="delete" uuid="e423d4ed-1c28-4062-ac94-f74dfd934f53" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="a4f28047-5a75-4c3e-8582-af988626e81f" type="field"/>
        <element action="delete" uuid="ce38c599-f87a-43b6-bb6d-7b17c84389ab" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="e423d4ed-1c28-4062-ac94-f74dfd934f53" type="field"/>
        <element action="delete" uuid="cdf5f0e0-ade1-4e1b-b12a-ad311564ed4e" parent-uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" sibling-uuid="ce38c599-f87a-43b6-bb6d-7b17c84389ab" type="field"/>
        <element action="delete" uuid="f7463670-db71-4c81-a5de-b26f098b44dc" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" type="field"/>
        <element action="delete" uuid="25d60f10-b398-4aa0-8767-d656d23cfd4b" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="f7463670-db71-4c81-a5de-b26f098b44dc" type="field"/>
        <element action="delete" uuid="195100f6-b1ad-4ca4-9ca9-51d9b37f1954" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="25d60f10-b398-4aa0-8767-d656d23cfd4b" type="field"/>
        <element action="delete" uuid="ddd985d7-aa8b-4f2c-95f6-52dbf747e69a" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="195100f6-b1ad-4ca4-9ca9-51d9b37f1954" type="field"/>
        <element action="delete" uuid="68030ff3-7fec-4055-9544-38d84cc12422" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="ddd985d7-aa8b-4f2c-95f6-52dbf747e69a" type="field"/>
        <element action="delete" uuid="10c09743-3a7b-44e5-9a8f-fc1f74b1c53c" parent-uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" sibling-uuid="68030ff3-7fec-4055-9544-38d84cc12422" type="field"/>
        <element action="delete" uuid="310f3903-37f0-4008-8ef8-e1d995d6bd90" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="73bc825e-d72b-4d8b-858d-a9e18d785fcc" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="f0f8d4a8-7d0e-4f5f-a7d1-fe7259eb9a5d" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="b1f57ea0-4950-403e-acac-d008bcb1e4c0" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="51fb4335-8a24-4360-a32d-acd8a2433244" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="3486427d-73f9-4133-96f8-4645ba654331" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="09a133b4-bca6-4a6f-b7fc-eec9958e0aa5" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="6585548f-164a-4056-bb5e-685545f2d882" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="454be441-0c95-4305-b051-390e970926c3" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="010639e6-8c02-4e72-98f6-87c15e1645bf" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="e7877b56-a4fc-483e-ba93-145e59197c0a" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="3615161b-d9f6-40da-9b00-9754ebdfaf67" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="d130dd81-5673-49f1-bbba-1fa536952ae5" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="6d0d1cc6-c6b1-4c5c-8ad7-8cac5a408f6b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="2fbcff9c-32db-4048-af0b-6e71ba95410a" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="3e835401-9d64-4134-ac69-80d9791dcd96" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="10dd906c-e63a-4959-8018-348d97ada592" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="adcbc81e-e7f1-4fa0-8cec-d1916f1f9007" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="63fe15ed-3a72-440d-a722-8c03e11a66c2" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="1c2572a9-f46a-4a48-a099-ae5bb97233ed" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="829ce0e0-6079-426f-9097-508acae5d0c8" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="9fe8e35a-1d1d-4e48-a42b-5e3ae11d8fab" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="1712273f-edb8-4272-a266-71bf382ae475" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="38c4f148-b7c7-4b19-8eb7-1e18c9e22a15" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="7ae11b79-658c-4be9-bb30-909d106ed581" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="ab568cc9-259d-4657-9554-972d9374935e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="47e5f30c-cecf-41de-8de8-f826906e5f1e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="3a08f560-191f-4aa9-88a1-078ecb591eda" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="6ecf0537-7fc1-408f-b965-5ef2e610ba56" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="30142d2f-4fc4-4778-9b5e-ba8d3d0a228f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="bdb151f3-319b-4d60-8f10-be010b476a2f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="46682376-f61c-4572-a9e3-168c0e4c9610" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="a1794938-ae91-4a9c-934c-be9182e9b308" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="100b0cd6-6e94-426e-adfa-5d07e0e70b1e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="dcd8b4ef-83bc-48bb-a668-2f81f74f5f9b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="5a1542d7-d9c3-415d-bce8-5975c179cb11" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="delete" uuid="6eb417fb-a6e2-43cd-ac8e-aab72e9d9869" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="update" uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="module">
          <property name="migrations-path" value="migrations"/>
          <property name="models-disabled" value="true"/>
        </element>
        <element action="add" uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="CalendarMemberfunction"/>
          <property name="name" value="\App\Models\CalendarMemberfunction"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="bad42bc1-039a-413e-b2a2-535a85de24cb" parent-uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7" type="field">
          <property name="name" value="calendar_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9584accf-c83f-449d-85ac-ef59c051ea7e" parent-uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7" sibling-uuid="bad42bc1-039a-413e-b2a2-535a85de24cb" type="field">
          <property name="name" value="memberfunction_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="7ee267a8-958f-4b7c-98be-90d6956af2dc" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="many-to-many">
          <property name="mn-entity" value="\App\Models\CalendarMemberfunction"/>
        </element>
        <element action="add" uuid="615d9315-8baf-45af-9131-db36712014d8" parent-uuid="7ee267a8-958f-4b7c-98be-90d6956af2dc" type="many-to-many-entity">
          <property name="alias" value="calendars"/>
          <property name="name" value="\App\Models\Calendar"/>
          <property name="owning-side" value="true"/>
        </element>
        <element action="add" uuid="f29e2705-d23f-40fb-b296-ae37e13face2" parent-uuid="7ee267a8-958f-4b7c-98be-90d6956af2dc" type="many-to-many-entity">
          <property name="alias" value="memberfunctions"/>
          <property name="name" value="\App\Models\Memberfunction"/>
          <property name="owning-side" value="false"/>
        </element>
        <element action="add" uuid="1defd779-a7f9-490c-a68f-571f1da9e73b" parent-uuid="615d9315-8baf-45af-9131-db36712014d8" type="many-to-many-field">
          <property name="from" value="calendar_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="00cfd129-6fb1-450d-96bf-8b0f26a0a5d6" parent-uuid="f29e2705-d23f-40fb-b296-ae37e13face2" type="many-to-many-field">
          <property name="from" value="memberfunction_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="a3c5959d-3824-4cd8-b0d6-74d204485b94" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="comment">
          <property name="description" value="only non-public calendars can be limitted to specific memberfunctions"/>
        </element>
        <element action="reparent" uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="8e206929-1d19-46af-b6df-4c0df9b99050" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
        <element action="reparent" uuid="441d93ef-93f8-431b-aead-7c85754ed18c" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity"/>
      </revision>
      <revision uuid="4e6e2731-be66-4e70-89c2-07f45a1f25b0" date="2024-01-15 18:38:47.412269" exportable="ignored">
        <element action="add" uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="socials"/>
          <property name="name" value="\App\Models\socials"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="ede684d5-3c30-415f-a1c8-2ceffd7b1d02" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f3e9b42f-1c6b-41eb-9a13-3b0f749ba85f" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" sibling-uuid="ede684d5-3c30-415f-a1c8-2ceffd7b1d02" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a0ca0b95-63d9-4680-b4c8-b5a2c5c168a9" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" sibling-uuid="f3e9b42f-1c6b-41eb-9a13-3b0f749ba85f" type="field">
          <property name="name" value="display_name"/>
          <property name="required" value="true"/>
          <property name="size" value="25"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="e5f4fa07-b532-4ea6-83d9-f1cdb1b17ac9" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" sibling-uuid="a0ca0b95-63d9-4680-b4c8-b5a2c5c168a9" type="field">
          <property name="name" value="link"/>
          <property name="required" value="true"/>
          <property name="size" value="150"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="cc663b6e-43b7-4aad-b404-9e0aa8c3f622" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" sibling-uuid="e5f4fa07-b532-4ea6-83d9-f1cdb1b17ac9" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="6d4388f7-a332-481a-9583-88c6b5c3e887" parent-uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" sibling-uuid="cc663b6e-43b7-4aad-b404-9e0aa8c3f622" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="079c3434-7d6d-4469-868d-0dc423ef52a8" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\socials"/>
          <property name="inverse-alias" value="domain"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="socials"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="119b4bfa-dbdd-4e7f-a796-e8be05e1d227" parent-uuid="079c3434-7d6d-4469-868d-0dc423ef52a8" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="reparent" uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="reparent" uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
        <element action="reparent" uuid="441d93ef-93f8-431b-aead-7c85754ed18c" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity"/>
      </revision>
      <revision uuid="9f9eb576-4dd5-49f9-85b2-55d64359fc1f" date="2024-04-13 19:43:47.277019" exportable="ignored">
        <element action="add" uuid="6e68c994-ef99-40f3-addf-4c269530aead" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="446ff82b-059f-4102-978b-842d4d5f3844" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="region">
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="update" uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" parent-uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" type="entity">
          <property name="local-name" value="Cms"/>
          <property name="name" value="\App\Models\Cms"/>
        </element>
        <element action="add" uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity">
          <property name="local-name" value="Reservationnumber"/>
          <property name="name" value="\App\Models\Reservationnumber"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity">
          <property name="local-name" value="Reservation"/>
          <property name="name" value="\App\Models\Reservation"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="update" uuid="72f8d58c-fa0b-4130-9ef2-484bcef0e041" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" type="field">
          <property name="unique"/>
        </element>
        <element action="update" uuid="95a9855f-84d8-4fc7-a4aa-5fd6e3367705" parent-uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" sibling-uuid="72f8d58c-fa0b-4130-9ef2-484bcef0e041" type="field">
          <property name="unique"/>
        </element>
        <element action="add" uuid="2d80d1ae-4963-4b54-b85d-c06aaf1ea398" parent-uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="46f8f5cb-5c2d-4f93-ac69-5e40e2e7d494" parent-uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" sibling-uuid="2d80d1ae-4963-4b54-b85d-c06aaf1ea398" type="field">
          <property name="name" value="reservation_id"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="30737666-0afc-4889-b6c3-4f91aa16cd57" parent-uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" sibling-uuid="46f8f5cb-5c2d-4f93-ac69-5e40e2e7d494" type="field">
          <property name="name" value="reservation_number"/>
          <property name="required" value="true"/>
          <property name="size" value="15"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="ecaa3962-80df-46d5-b8e6-3961a2998c32" parent-uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" sibling-uuid="30737666-0afc-4889-b6c3-4f91aa16cd57" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="dccacf43-2391-4480-815f-2286ad6bb88d" parent-uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" sibling-uuid="ecaa3962-80df-46d5-b8e6-3961a2998c32" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="a4bea739-27da-4e2e-bd41-39a25c207cf0" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ec7a8fb5-7609-4b6e-823c-72e74699ffb7" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="a4bea739-27da-4e2e-bd41-39a25c207cf0" type="field">
          <property name="name" value="event_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f32a46ee-de30-4847-baa8-dbc89b8eda62" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="ec7a8fb5-7609-4b6e-823c-72e74699ffb7" type="field">
          <property name="name" value="email"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c5a08820-a082-46dd-9bc5-bb3d63798f58" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="f32a46ee-de30-4847-baa8-dbc89b8eda62" type="field">
          <property name="name" value="fullname"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="c0a0f818-e341-44c8-a06f-9966b86dddb7" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="c5a08820-a082-46dd-9bc5-bb3d63798f58" type="field">
          <property name="enum-values" value="'pending', 'confirmed', 'canceled'"/>
          <property name="name" value="status"/>
          <property name="type" value="enum"/>
        </element>
        <element action="add" uuid="8c220577-c0b9-4620-a998-09ea85e809e1" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="c0a0f818-e341-44c8-a06f-9966b86dddb7" type="field">
          <property name="name" value="num_tickets"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="82ba1226-065f-46fe-a643-f28ad6284ca4" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="8c220577-c0b9-4620-a998-09ea85e809e1" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="*************-49af-a9b5-1e06ccbba353" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="82ba1226-065f-46fe-a643-f28ad6284ca4" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="3f5ea40a-8129-47fc-8026-d92c88402f5a" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="842675c2-0e96-423d-972d-53c18500a2d1" type="field">
          <property name="name" value="max_tickets"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="01c12284-9ee2-45c6-9a77-4c72bb101e11" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Reservationnumber"/>
          <property name="inverse-alias" value="reservation"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="reservationnumbers"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Reservation"/>
        </element>
        <element action="add" uuid="504277a9-94c4-42aa-8e66-d28c5ae800c1" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Reservation"/>
          <property name="inverse-alias" value="event"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="reservations"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="ebd5a700-d190-4cdd-bdfb-059d27e405b8" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\RoleUser"/>
          <property name="inverse-alias" value="user"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="roleUsers"/>
          <property name="to" value="\App\Models\User"/>
        </element>
        <element action="add" uuid="f0be962e-c320-4801-b408-3c27334358ee" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\RoleUser"/>
          <property name="inverse-alias" value="role"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="roleUsers"/>
          <property name="to" value="\App\Models\Role"/>
        </element>
        <element action="add" uuid="be6234fd-73d7-4597-9e7e-39a09a680f5e" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\User"/>
          <property name="inverse-alias" value="domain"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="users"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="f2ec6c96-e24c-4217-8c74-c556936e2da9" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\MemberMemberfunction"/>
          <property name="inverse-alias" value="memberfunction"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="memberMemberfunctions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Memberfunction"/>
        </element>
        <element action="add" uuid="e8b0c164-e449-4de9-98ee-9228ec88e844" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\MemberMemberfunction"/>
          <property name="inverse-alias" value="member"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="memberMemberfunctions"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Member"/>
        </element>
        <element action="add" uuid="e6b5537f-5f8d-414e-8718-c963d83b07a0" parent-uuid="01c12284-9ee2-45c6-9a77-4c72bb101e11" type="association-field">
          <property name="from" value="reservation_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="e6d6bdd5-a169-4dfe-b665-8dc941f58f89" parent-uuid="504277a9-94c4-42aa-8e66-d28c5ae800c1" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="13d41ce2-06af-43b6-89fd-27415273caa4" parent-uuid="ebd5a700-d190-4cdd-bdfb-059d27e405b8" type="association-field">
          <property name="from" value="user_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="7d526b93-4f46-4b08-ae65-f373a0650fc3" parent-uuid="f0be962e-c320-4801-b408-3c27334358ee" type="association-field">
          <property name="from" value="role_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="0df1c7c6-289f-4969-8432-1739993fe797" parent-uuid="be6234fd-73d7-4597-9e7e-39a09a680f5e" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="bfa05c00-236c-48b6-8484-8bbcefc118e4" parent-uuid="f2ec6c96-e24c-4217-8c74-c556936e2da9" type="association-field">
          <property name="from" value="memberfunction_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="b57f5dc3-abe9-4b7f-ad9c-de08f03ac803" parent-uuid="e8b0c164-e449-4de9-98ee-9228ec88e844" type="association-field">
          <property name="from" value="member_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="reparent" uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" parent-uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" type="entity"/>
        <element action="reparent" uuid="79f657f0-**************-473b038ae0b6" parent-uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" type="entity"/>
        <element action="reparent" uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" parent-uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" type="entity"/>
        <element action="reparent" uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" parent-uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" type="entity"/>
        <element action="reparent" uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" parent-uuid="446ff82b-059f-4102-978b-842d4d5f3844" type="entity"/>
        <element action="reparent" uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" parent-uuid="446ff82b-059f-4102-978b-842d4d5f3844" type="entity"/>
        <element action="reparent" uuid="441d93ef-93f8-431b-aead-7c85754ed18c" parent-uuid="446ff82b-059f-4102-978b-842d4d5f3844" type="entity"/>
        <element action="reparent" uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" parent-uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" type="entity"/>
        <element action="reparent" uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" parent-uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" type="entity"/>
        <element action="reparent" uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7" parent-uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" type="entity"/>
        <element action="reparent" uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" parent-uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600" type="entity"/>
        <element action="reparent" uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" parent-uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600" type="entity"/>
        <element action="reparent" uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" parent-uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600" type="entity"/>
        <element action="reorder" uuid="9e2a9f0d-b76e-473a-98a9-c20a2d2182e7" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="3f5ea40a-8129-47fc-8026-d92c88402f5a" previous-sibling-uuid="842675c2-0e96-423d-972d-53c18500a2d1" type="field" helper="true"/>
        <element action="reparent" uuid="a3c5959d-3824-4cd8-b0d6-74d204485b94" parent-uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" type="comment"/>
      </revision>
      <revision uuid="63b57763-857a-4968-8808-256331d06d73" date="2024-06-21 11:27:31.298314" exportable="ignored">
        <element action="add" uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="entity">
          <property name="local-name" value="Emaillogentry"/>
          <property name="name" value="\App\Models\Emaillogentry"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="4cd27654-88e7-4e2e-9919-1c121e562d62" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="3800dd68-0a75-4bcb-a1f5-23a7154a025f" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="4cd27654-88e7-4e2e-9919-1c121e562d62" type="field">
          <property name="name" value="domain_id"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="ccccf173-15df-4ab5-8604-e5a8da330623" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="3800dd68-0a75-4bcb-a1f5-23a7154a025f" type="field">
          <property name="name" value="to"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="aa3dbfb6-6278-4293-95b5-5ef69d53b279" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="ccccf173-15df-4ab5-8604-e5a8da330623" type="field">
          <property name="name" value="cc"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="b67f51d4-2f8e-4a58-88fc-85978fa02c76" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="aa3dbfb6-6278-4293-95b5-5ef69d53b279" type="field">
          <property name="name" value="bcc"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="6b6a80c9-c24b-40c0-bb5a-0e7798466a96" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="b67f51d4-2f8e-4a58-88fc-85978fa02c76" type="field">
          <property name="name" value="from"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="08d4ee0c-4136-4aa5-a518-8facee535c46" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="6b6a80c9-c24b-40c0-bb5a-0e7798466a96" type="field">
          <property name="name" value="subject"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="142d3954-0906-41ae-b854-0631bbe296f6" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="08d4ee0c-4136-4aa5-a518-8facee535c46" type="field">
          <property name="name" value="body"/>
          <property name="required" value="true"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="fa157359-d572-44be-8874-18da91739c14" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="142d3954-0906-41ae-b854-0631bbe296f6" type="field">
          <property name="name" value="attachments"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="382255b1-7fdc-41c1-92fd-6e2bea061a5d" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="fa157359-d572-44be-8874-18da91739c14" type="field">
          <property name="name" value="unique_token"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="df6d4587-efe7-4df2-bd68-23117b737905" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="382255b1-7fdc-41c1-92fd-6e2bea061a5d" type="field">
          <property name="default" value="queued"/>
          <property name="name" value="status"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="314d938c-9276-46f2-8ed6-ec6937e1dc51" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="df6d4587-efe7-4df2-bd68-23117b737905" type="field">
          <property name="name" value="log"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="*************-4730-af14-48003e8ec831" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="314d938c-9276-46f2-8ed6-ec6937e1dc51" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="0e9cc8fc-ed87-414b-bbee-d39cc5e494df" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="*************-4730-af14-48003e8ec831" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="06b83a03-b836-4a89-97bc-86e6b9d09dad" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Emaillogentry"/>
          <property name="inverse-alias" value="domain"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="emaillogentries"/>
          <property name="parent-required" value="true"/>
          <property name="to" value="\App\Models\Domain"/>
        </element>
        <element action="add" uuid="67f3ff3c-f01a-4906-a929-e25ef8d1bf4a" parent-uuid="06b83a03-b836-4a89-97bc-86e6b9d09dad" type="association-field">
          <property name="from" value="domain_id"/>
          <property name="to" value="id"/>
        </element>
      </revision>
      <revision uuid="8ab0d408-51de-4771-bf56-e4b95023e8f7" date="2024-06-21 11:30:25.851402" exportable="ignored">
        <element action="add" uuid="c97c50e9-40db-4a6b-a067-b9805cd48305" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="6b6a80c9-c24b-40c0-bb5a-0e7798466a96" type="field">
          <property name="name" value="reply_to"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="reorder" uuid="08d4ee0c-4136-4aa5-a518-8facee535c46" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="c97c50e9-40db-4a6b-a067-b9805cd48305" previous-sibling-uuid="6b6a80c9-c24b-40c0-bb5a-0e7798466a96" type="field" helper="true"/>
      </revision>
      <revision uuid="887823be-b107-4ad0-9896-3ce244caf525" date="2025-07-02 18:56:45.507127" exportable="true">
        <element action="delete" uuid="b00f3d18-6d34-4149-90f7-2fc8553becc4" parent-uuid="bd3d3ecd-7847-4d8c-bf40-8c1decfb4a79" type="association-field"/>
        <element action="delete" uuid="e6d6bdd5-a169-4dfe-b665-8dc941f58f89" parent-uuid="504277a9-94c4-42aa-8e66-d28c5ae800c1" type="association-field"/>
        <element action="delete" uuid="bd3d3ecd-7847-4d8c-bf40-8c1decfb4a79" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association"/>
        <element action="delete" uuid="504277a9-94c4-42aa-8e66-d28c5ae800c1" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association"/>
        <element action="delete" uuid="ec7a8fb5-7609-4b6e-823c-72e74699ffb7" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="a4bea739-27da-4e2e-bd41-39a25c207cf0" type="field"/>
        <element action="delete" uuid="3f5ea40a-8129-47fc-8026-d92c88402f5a" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="842675c2-0e96-423d-972d-53c18500a2d1" type="field"/>
        <element action="add" uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity">
          <property name="local-name" value="Performance"/>
          <property name="name" value="\App\Models\Performance"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity">
          <property name="local-name" value="TicketType"/>
          <property name="name" value="\App\Models\TicketType"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="bd194a83-8d1b-424b-9912-bda3a6064be6" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity">
          <property name="local-name" value="EventTickettype"/>
          <property name="name" value="\App\Models\EventTickettype"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="add" uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" parent-uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" type="entity">
          <property name="local-name" value="calendar_event"/>
          <property name="name" value="\App\Models\calendar_event"/>
          <property name="namespace" value="\App\Models"/>
        </element>
        <element action="update" uuid="842675c2-0e96-423d-972d-53c18500a2d1" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="88843e88-af55-4888-b117-1a110e55c87b" type="field">
          <property name="name" value="max_tickets"/>
          <property name="required" value="true"/>
        </element>
        <element action="update" uuid="df6d4587-efe7-4df2-bd68-23117b737905" parent-uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" sibling-uuid="382255b1-7fdc-41c1-92fd-6e2bea061a5d" type="field">
          <property name="description" value="one of queued, sent, failed, unknown"/>
        </element>
        <element action="add" uuid="b1c8913e-d913-49ce-b7c5-bf1935711aa9" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="*************-49af-a9b5-1e06ccbba353" type="field">
          <property name="name" value="event_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="1877bd8b-c8a4-402f-b064-952cbc6f9fbf" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="17785a1b-92b8-493e-8302-a94460cbc48e" type="field">
          <property name="name" value="performance_id"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="6ec71544-6e11-4104-9532-42eb053a1cbf" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="842675c2-0e96-423d-972d-53c18500a2d1" type="field">
          <property name="name" value="venue_desc_1"/>
          <property name="required" value="true"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="98a4f3d2-02b7-438c-80de-604edb761669" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="6ec71544-6e11-4104-9532-42eb053a1cbf" type="field">
          <property name="name" value="venue_desc_2"/>
          <property name="size" value="255"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="f1a3bcc8-f7db-4427-bcf8-3d8bffbe5338" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="8babf87e-b18c-44bb-bf29-eb8d66b7929e" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="f1a3bcc8-f7db-4427-bcf8-3d8bffbe5338" type="field">
          <property name="name" value="title"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="32a62df3-60b1-47f6-a67e-ada49d9a256e" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="8babf87e-b18c-44bb-bf29-eb8d66b7929e" type="field">
          <property name="name" value="description"/>
          <property name="type" value="text"/>
        </element>
        <element action="add" uuid="8e879eeb-cdc8-4377-973a-dd8149437dce" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="32a62df3-60b1-47f6-a67e-ada49d9a256e" type="field">
          <property name="name" value="duration_minutes"/>
          <property name="type" value="integer"/>
        </element>
        <element action="add" uuid="625da235-cb2e-4798-bc13-1fe3086a2edb" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="8e879eeb-cdc8-4377-973a-dd8149437dce" type="field">
          <property name="default" value="false"/>
          <property name="name" value="published_on_web"/>
          <property name="required" value="true"/>
          <property name="type" value="boolean"/>
        </element>
        <element action="add" uuid="32b30228-5e12-4336-a530-e4dc969df7ee" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="625da235-cb2e-4798-bc13-1fe3086a2edb" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="19f42fe7-1cee-4512-8824-609f82c25f1c" parent-uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" sibling-uuid="32b30228-5e12-4336-a530-e4dc969df7ee" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="4227f7cb-85b2-414a-b79d-a6255320affe" parent-uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="f9d617a1-d0f4-4623-8ab1-6a2fda1e6678" parent-uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" sibling-uuid="4227f7cb-85b2-414a-b79d-a6255320affe" type="field">
          <property name="default" value="standard"/>
          <property name="description" value="e.g. child, oap, loge,...."/>
          <property name="name" value="name"/>
          <property name="required" value="true"/>
          <property name="size" value="50"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="49933f4c-18d4-48c7-a844-2cc973227857" parent-uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" sibling-uuid="f9d617a1-d0f4-4623-8ab1-6a2fda1e6678" type="field">
          <property name="default" value="std"/>
          <property name="name" value="code"/>
          <property name="required" value="true"/>
          <property name="size" value="3"/>
          <property name="type" value="string"/>
        </element>
        <element action="add" uuid="92ca17e2-9eaa-4c04-8648-d2925fb7e080" parent-uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" sibling-uuid="49933f4c-18d4-48c7-a844-2cc973227857" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="6c4ff461-120b-4025-a7a9-81903b6d99ac" parent-uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" sibling-uuid="92ca17e2-9eaa-4c04-8648-d2925fb7e080" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="20906b66-4529-415f-82d8-35a412860d2b" parent-uuid="bd194a83-8d1b-424b-9912-bda3a6064be6" type="field">
          <property name="name" value="event_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="9520214b-dc93-4784-996c-7ce59846e805" parent-uuid="bd194a83-8d1b-424b-9912-bda3a6064be6" sibling-uuid="20906b66-4529-415f-82d8-35a412860d2b" type="field">
          <property name="name" value="ticket_type_id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="a07d2622-6b59-4746-ad76-a587ed1e1f9b" parent-uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" type="field">
          <property name="auto-increment" value="true"/>
          <property name="name" value="id"/>
          <property name="primary" value="true"/>
          <property name="required" value="true"/>
          <property name="type" value="bigInteger"/>
          <property name="unique" value="true"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="e3a5ad69-19f7-48b3-9537-d80560d7fb6c" parent-uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" sibling-uuid="a07d2622-6b59-4746-ad76-a587ed1e1f9b" type="field">
          <property name="name" value="calendar_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="4b3ba359-cbe9-4bc9-a6e0-2c9bcc37f51c" parent-uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" sibling-uuid="e3a5ad69-19f7-48b3-9537-d80560d7fb6c" type="field">
          <property name="name" value="event_id"/>
          <property name="type" value="integer"/>
          <property name="unsigned" value="true"/>
        </element>
        <element action="add" uuid="dd0679f0-238b-44ef-9d6d-67e65e3b41b8" parent-uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" sibling-uuid="4b3ba359-cbe9-4bc9-a6e0-2c9bcc37f51c" type="field">
          <property name="name" value="created_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="cda2b7b4-1ce8-47ba-af70-dbdf4d8b91a0" parent-uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" sibling-uuid="dd0679f0-238b-44ef-9d6d-67e65e3b41b8" type="field">
          <property name="name" value="updated_at"/>
          <property name="type" value="timestamp"/>
        </element>
        <element action="add" uuid="681da3ff-60a7-46dd-b7d7-0deecf6baecb" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Reservation"/>
          <property name="inverse-alias" value="event"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="reservations"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="24ad61b4-a3e1-4c96-ae7e-25a1a920a17f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\Event"/>
          <property name="inverse-alias" value="performance"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="events"/>
          <property name="to" value="\App\Models\Performance"/>
        </element>
        <element action="add" uuid="7523d9e9-2d8a-4dd2-8f28-5308e2768d8f" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\calendar_event"/>
          <property name="inverse-alias" value="calendar"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="calendarEvents"/>
          <property name="to" value="\App\Models\Calendar"/>
        </element>
        <element action="add" uuid="59934611-12c7-43e4-8a31-57fb1be3e8af" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="association">
          <property name="from" value="\App\Models\calendar_event"/>
          <property name="inverse-alias" value="event"/>
          <property name="many-inverse" value="false"/>
          <property name="many-owner" value="true"/>
          <property name="owner-alias" value="calendarEvents"/>
          <property name="to" value="\App\Models\Event"/>
        </element>
        <element action="add" uuid="e9057c76-fefe-4c51-9554-5cfb468648be" parent-uuid="681da3ff-60a7-46dd-b7d7-0deecf6baecb" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="156c5bb4-9dcc-41ac-974e-79f8d07e419c" parent-uuid="24ad61b4-a3e1-4c96-ae7e-25a1a920a17f" type="association-field">
          <property name="from" value="performance_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="e9acc54b-da10-4a98-9dd5-8ecbfb438bee" parent-uuid="7523d9e9-2d8a-4dd2-8f28-5308e2768d8f" type="association-field">
          <property name="from" value="calendar_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="4320c5ff-b766-46df-bd90-62c83b478242" parent-uuid="59934611-12c7-43e4-8a31-57fb1be3e8af" type="association-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="15095fdc-1c49-4ab9-b218-0d393cb4f877" parent-uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" type="many-to-many">
          <property name="mn-entity" value="\App\Models\EventTickettype"/>
        </element>
        <element action="add" uuid="f872f1ba-9c05-47e7-a93b-8afccdd92365" parent-uuid="15095fdc-1c49-4ab9-b218-0d393cb4f877" type="many-to-many-entity">
          <property name="alias" value="events"/>
          <property name="name" value="\App\Models\Event"/>
          <property name="owning-side" value="true"/>
        </element>
        <element action="add" uuid="069c10e6-0043-4ca3-a7b1-8b54ac5defb7" parent-uuid="15095fdc-1c49-4ab9-b218-0d393cb4f877" type="many-to-many-entity">
          <property name="alias" value="ticketTypes"/>
          <property name="name" value="\App\Models\TicketType"/>
          <property name="owning-side" value="false"/>
        </element>
        <element action="add" uuid="5f049e8e-3dd3-4cd5-bfad-0c0b8b3beee8" parent-uuid="f872f1ba-9c05-47e7-a93b-8afccdd92365" type="many-to-many-field">
          <property name="from" value="event_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="ae2b19f6-75d6-4940-a979-e321baeeeee0" parent-uuid="069c10e6-0043-4ca3-a7b1-8b54ac5defb7" type="many-to-many-field">
          <property name="from" value="ticket_type_id"/>
          <property name="to" value="id"/>
        </element>
        <element action="add" uuid="4508cea5-f461-4b61-9e62-399c62220d50" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="comment">
          <property name="description" value="calendar_id is still here to be ablte to create a migration script"/>
        </element>
        <element action="reparent" uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" parent-uuid="6e68c994-ef99-40f3-addf-4c269530aead" type="entity"/>
        <element action="reorder" uuid="f32a46ee-de30-4847-baa8-dbc89b8eda62" parent-uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" sibling-uuid="a4bea739-27da-4e2e-bd41-39a25c207cf0" previous-sibling-uuid="ec7a8fb5-7609-4b6e-823c-72e74699ffb7" type="field" helper="true"/>
        <element action="reorder" uuid="1cfad8d3-1b1b-42cd-82c6-299e4035b9c4" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="1877bd8b-c8a4-402f-b064-952cbc6f9fbf" previous-sibling-uuid="17785a1b-92b8-493e-8302-a94460cbc48e" type="field" helper="true"/>
        <element action="reorder" uuid="9e2a9f0d-b76e-473a-98a9-c20a2d2182e7" parent-uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" sibling-uuid="98a4f3d2-02b7-438c-80de-604edb761669" previous-sibling-uuid="3f5ea40a-8129-47fc-8026-d92c88402f5a" type="field" helper="true"/>
      </revision>
    </migrations>
  </module>
  <visual-data>
    <association uuid="01c12284-9ee2-45c6-9a77-4c72bb101e11" color="#969696"/>
    <association uuid="06b83a03-b836-4a89-97bc-86e6b9d09dad" color="#969696" split="1"/>
    <association uuid="079c3434-7d6d-4469-868d-0dc423ef52a8" color="#969696"/>
    <association uuid="24ad61b4-a3e1-4c96-ae7e-25a1a920a17f" caption1-position-x="0" caption1-position-y="0" center-position-x="0" center-position-y="0" color="#969696"/>
    <association uuid="26ed87af-afdb-4d88-b4ba-72688be3780b" center-position-x="16" center-position-y="0" color="#0A783D" split="1"/>
    <association uuid="4d340f46-2f02-44b0-b63f-0f8574fbb320" center-position-x="0" center-position-y="3" color="#61D351" split="1"/>
    <association uuid="59934611-12c7-43e4-8a31-57fb1be3e8af" color="#969696"/>
    <association uuid="617802d2-a8dc-4c51-8e05-6e668183351b" center-position-x="0" center-position-y="1" color="#2E63AF" split="1"/>
    <association uuid="64f95f2e-f94d-4606-b0d6-13eada0bcd44" center-position-x="1" center-position-y="23" color="#CDC32B"/>
    <association uuid="681da3ff-60a7-46dd-b7d7-0deecf6baecb" caption1-position-x="0" caption1-position-y="0" center-position-x="0" center-position-y="0" color="#969696"/>
    <association uuid="7523d9e9-2d8a-4dd2-8f28-5308e2768d8f" color="#969696"/>
    <association uuid="7d5a7c17-1601-4417-9cde-faf6c11ae20f" center-position-x="0" center-position-y="0" color="#00B4D0"/>
    <association uuid="be6234fd-73d7-4597-9e7e-39a09a680f5e" color="#969696" split="1"/>
    <association uuid="d77acbf8-d163-428a-b6c3-5039f27fbb5e" center-position-x="14" center-position-y="0" color="#B31F24" split="1"/>
    <association uuid="e5c31f2b-c548-4f47-83ae-0e365dbcd0ff" center-position-x="-2" center-position-y="0" color="#F4931F"/>
    <association uuid="e8b0c164-e449-4de9-98ee-9228ec88e844" color="#969696"/>
    <association uuid="ebd5a700-d190-4cdd-bdfb-059d27e405b8" color="#969696"/>
    <association uuid="f0be962e-c320-4801-b408-3c27334358ee" color="#969696"/>
    <association uuid="f2ec6c96-e24c-4217-8c74-c556936e2da9" color="#969696"/>
    <comment uuid="4508cea5-f461-4b61-9e62-399c62220d50" bg-color="#FFFFE0" position-x="8" position-y="223" size-x="0" size-x2="168" size-y="0" size-y2="42" txt-color="#000000"/>
    <comment uuid="a3c5959d-3824-4cd8-b0d6-74d204485b94" bg-color="#FFFFE0" position-x="-7" position-y="215" size-x="0" size-x2="168" size-y="0" size-y2="42" txt-color="#000000"/>
    <entity uuid="17c2822a-8872-4e28-9a7f-fbcd68c585d8" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="146" position-y="22" size-x="0" size-x2="163" size-y="0" size-y2="381"/>
    <entity uuid="1aaa6059-099e-4990-b8c0-ae4b4f219b4f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-210" position-y="746" size-x="0" size-x2="126" size-y="0" size-y2="101"/>
    <entity uuid="1c5f6ed4-d5bc-4f80-b078-ebfa649e87b9" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="152" position-y="667" size-x="0" size-x2="111" size-y="0" size-y2="101"/>
    <entity uuid="1d0862a3-e0be-42cc-a942-8d3f7a75b479" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="449" position-y="-37" size-x="0" size-x2="150" size-y="0" size-y2="87"/>
    <entity uuid="1d7ea4e9-ed86-4565-a297-f25fd3b3a1f7" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="154" position-y="398" size-x="0" size-x2="109" size-y="0" size-y2="115"/>
    <entity uuid="262302d8-44dc-40b7-ba02-2dfcb663bd3f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="168" position-y="10" size-x="0" size-x2="135" size-y="0" size-y2="199"/>
    <entity uuid="2fd1e626-a064-4527-aadb-463d6d49a7cb" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-19" position-y="533" size-x="0" size-x2="159" size-y="0" size-y2="367"/>
    <entity uuid="309f3dad-9a7c-4d3d-82f8-b56f642897a7" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="169" position-y="206" size-x="0" size-x2="147" size-y="0" size-y2="60"/>
    <entity uuid="3e47d25f-e55c-42bf-a2a3-c99d97f2cf7c" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="20" position-y="596" size-x="0" size-x2="116" size-y="0" size-y2="59"/>
    <entity uuid="441d93ef-93f8-431b-aead-7c85754ed18c" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-10" position-y="30" size-x="0" size-x2="143" size-y="0" size-y2="143"/>
    <entity uuid="4898a62d-7c7b-4467-a95e-f79db179d7a9" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="13" position-y="679" size-x="0" size-x2="119" size-y="0" size-y2="143"/>
    <entity uuid="4f6449c1-9ed9-4c88-9638-d80686ed9757" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="3" position-y="319" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="5570e8e1-7cd8-47d4-ad08-86d4e880d418" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-202" position-y="226" size-x="0" size-x2="129" size-y="0" size-y2="227"/>
    <entity uuid="56e32159-e90b-4f79-8cac-3bd9fcacc02c" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="12" position-y="851" size-x="0" size-x2="142" size-y="0" size-y2="73"/>
    <entity uuid="60d6b655-8621-4d2b-8121-dcdd3d7941c9" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="419" position-y="14" size-x="0" size-x2="140" size-y="0" size-y2="129"/>
    <entity uuid="6c4abbc0-c551-4975-a38c-9d575c39e85a" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="277" position-y="-9" size-x="0" size-x2="139" size-y="0" size-y2="115"/>
    <entity uuid="754f8ef4-f69a-493d-8ff1-df096d5a8efd" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-91" position-y="67" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="79f657f0-**************-473b038ae0b6" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="625" position-y="19" size-x="0" size-x2="132" size-y="0" size-y2="115"/>
    <entity uuid="81a60ca3-167f-4580-8eb5-205daa105ed1" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="22" position-y="521" size-x="0" size-x2="88" size-y="0" size-y2="45"/>
    <entity uuid="833e63f5-b99a-48b4-ac6d-2a65baf7fcbf" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="58" position-y="36" size-x="0" size-x2="119" size-y="0" size-y2="115"/>
    <entity uuid="8738b7a2-64d9-40d5-bb83-c9b995d75478" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="237" position-y="50" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="8b989576-e44b-4d3f-b2a0-9a28f0788d62" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="6" position-y="209" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="8e206929-1d19-46af-b6df-4c0df9b99050" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="4" position-y="965" size-x="0" size-x2="128" size-y="0" size-y2="73"/>
    <entity uuid="ad0d491c-ea65-4400-9ef2-c40fb948e341" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="24" position-y="393" size-x="0" size-x2="108" size-y="0" size-y2="101"/>
    <entity uuid="ae9b7531-7184-4c96-928a-8a354efb42ad" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-194" position-y="566" size-x="0" size-x2="119" size-y="0" size-y2="73"/>
    <entity uuid="b34b78e3-f010-4877-b8d9-0dac9d42c792" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="11" position-y="1059" size-x="0" size-x2="158" size-y="0" size-y2="157"/>
    <entity uuid="bd194a83-8d1b-424b-9912-bda3a6064be6" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="105" position-y="-24" size-x="0" size-x2="127" size-y="0" size-y2="60"/>
    <entity uuid="c6cba5a6-fa7b-4de2-98bf-b8781a6342b1" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-100" position-y="225" size-x="0" size-x2="136" size-y="0" size-y2="73"/>
    <entity uuid="e34b1c6c-f086-412f-961e-96c5c7e53b9d" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="106" position-y="83" size-x="0" size-x2="135" size-y="0" size-y2="171"/>
    <entity uuid="e41265a8-bb46-40a8-bf42-64e4b2782703" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="429" position-y="133" size-x="0" size-x2="119" size-y="0" size-y2="129"/>
    <entity uuid="ebd38607-b784-404d-9fe9-12a8cbf31eb7" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="109" position-y="-150" size-x="0" size-x2="119" size-y="0" size-y2="87"/>
    <entity uuid="f728d7d0-8d23-40c9-a89d-0d480dd41c7f" bg-color="#FFFFFF" hdr-color="#D2D2D2" position-x="-8" position-y="34" size-x="0" size-x2="125" size-y="0" size-y2="115"/>
    <many-to-many-association uuid="15095fdc-1c49-4ab9-b218-0d393cb4f877" color="#969696"/>
    <many-to-many-association uuid="7ee267a8-958f-4b7c-98be-90d6956af2dc" color="#969696"/>
    <many-to-many-association-entity uuid="615d9315-8baf-45af-9131-db36712014d8" center-position-x="0" center-position-y="0"/>
    <many-to-many-association-entity uuid="f29e2705-d23f-40fb-b296-ae37e13face2" center-position-x="0" center-position-y="-15"/>
    <many-to-many-association-entity uuid="f872f1ba-9c05-47e7-a93b-8afccdd92365" center-position-x="0" center-position-y="0"/>
    <module uuid="8100aa0b-e00e-4413-ab2d-4247eb3e8087" bg-color="#FEFCE8" position-x="50" position-y="50" size-x="617" size-x2="1519" size-y="53" size-y2="1245"/>
    <project uuid="b3da5a30-0faf-4b9f-b73c-9b403e049012" size-x="573" size-x2="2620" size-y="53" size-y2="2925"/>
    <region uuid="08f57b34-3fab-4a5b-aa4c-122ab1b38600" bg-color="#E7F5FD" position-x="384" position-y="559" size-x="172" size-x2="338" size-y="0" size-y2="423"/>
    <region uuid="230cdefb-b95e-44ed-9a59-ba1bcbe00a62" bg-color="#FDEBEF" position-x="-553" position-y="-319" size-x="44" size-x2="296" size-y="-331" size-y2="1567"/>
    <region uuid="446ff82b-059f-4102-978b-842d4d5f3844" bg-color="#F7FF65" position-x="876" position-y="509" size-x="58" size-x2="160" size-y="0" size-y2="419"/>
    <region uuid="6e68c994-ef99-40f3-addf-4c269530aead" bg-color="#D3F2A9" position-x="680" position-y="211" size-x="0" size-x2="622" size-y="179" size-y2="275"/>
    <region uuid="8bd21f01-c579-447f-a44e-9b46bea5f62b" bg-color="#EAE4F1" position-x="162" position-y="217" size-x="27" size-x2="386" size-y="0" size-y2="290"/>
    <region uuid="e3e823bc-dbef-4de8-bb49-f489359471a1" bg-color="#FEEFE3" position-x="-162" position-y="-41" size-x="48" size-x2="803" size-y="9" size-y2="230"/>
  </visual-data>
</skipper>
