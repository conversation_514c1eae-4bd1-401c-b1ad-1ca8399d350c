<?php namespace Scolavisa\scolib;

use Scolavisa\scolib\Mydat2Ndat;
use Scolavisa\scolib\Ndat2Mydat;

/**
 *  LocaleDate
 *
 *  Converts a date to a localespecific date
 *  @uses Mydat2Ndat
 *  @uses Ndat2Mydat
 *
 *  <AUTHOR>
 */
class LocaleDate {

    public static function getLocaleSpecificDate($date, $locale='nl') {
        switch ($locale) {
            case 'nl':
                return Mydat2Ndat::getNdat($date);
                break;
            case 'en':
                return Ndat2Mydat::getMydat($date);
                break;
            default:
                return Mydat2Ndat::getNdat($date);
                break;
        }        
    }
}