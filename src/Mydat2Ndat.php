<?php namespace Scolavisa\scolib;

/**
 *  Mydat2Ndat
 *
 *  Converts a mysql formatted date (yyyy-mm-dd) to a dutch date string (dd-mm-yyyy)
 *  a time part will also be included in the returned string. The function will
 *  automatically try to determine the correct date from a variety of input formats.
 *
 *  <AUTHOR>
 */
class Mydat2Ndat {

    public static function getNdat($input = "") {
        if ($input === '') {
            return '';
        }

        // if / then make that -
        $input = str_replace('/', '-', $input);

        // if $input has spaces as separator
        // if all numbers
        $tempDat = str_replace(' ', '', $input);
        $tempDat = str_replace(':', '', $tempDat);

        if (is_numeric($tempDat)) {
            $parts = explode(' ', $input);
            if (count($parts) > 2) {
                // if we have more parts than the date save that in $rest
                $rest = array_slice($parts, 3);
                $input = (strlen($parts[0]) === 1 ? '0' . $parts[0] : $parts[0]) .
                '-' . (strlen($parts[1]) === 1 ? '0' . $parts[1] : $parts[1]) .
                '-' .
                $parts[2] .
                " " . implode(' ', $rest);
            }
            $input = trim($input);
        }

        // try to see if its already in NL format
        if (substr($input, 2, 1) === '-') {
            return $input;
        }
        if (strpos($input, '-') === false) {
            return $input;
        }
        try {
            $d = new \DateTime($input);
            if (strlen($input) === 10) {
                return $d->format('d-m-Y');
            } else {
                return $d->format('d-m-Y H:i');
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
}