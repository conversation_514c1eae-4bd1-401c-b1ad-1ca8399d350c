<?php namespace Scolavisa\scolib;

/**
 *  Ndat2Mydat
 *
 *  Converts a dutch date string (dd-mm-yyyy) to a mysql formatted date (yyyy-mm-dd).
 *  A time part will also be included in the returned string. The function will
 *  automatically try to determine the correct date from a variety of input formats.
 *
 *  <AUTHOR>
 */
class Ndat2Mydat {
    public static function getMydat($input = '', $returnFalseOnInvalid = false) {

        $returnInvalid = $returnFalseOnInvalid ? false : $input;

        // if $input ends with time string, remove timestring, convert the date, concat timestring
        $savedTimeString = '';
        preg_match_all('/(.+)((\ |T)\d\d:\d\d(:\d\d)?)/', $input, $parts, PREG_PATTERN_ORDER);

        if (!empty($parts[0])) {
            // remove timestring = get everything before T or space
            $input = $parts[1][0];
            $savedTimeString = $parts[2][0];
        }

        // empty: return
        if ((empty($input))) {
            return $returnInvalid;
        }

        // only use - , no / or _
        $input = str_replace('/', '-', $input);
        $input = str_replace('_', '-', $input);

        // If the length is <= 8, the dashes are missing.
        // We expect this to be a ddmmyyyy string or a ddmmyy string
        if ((strlen($input) <= 8)&&(strpos($input, "-") === false) ){
            $input = substr($input, 0, 2) . "-" . substr($input, 2,2) . "-" . substr($input, 4);
        }

        // check if all numeric, there may be spaces where we expect dashes
        $tempDat = str_replace(' ', '', $input);
        if (is_numeric($tempDat)) {
            $parts = explode(" ", $input);
        } else {
            $parts = explode("-", $input);
        }
        if (count($parts) == 3) {
            $input = (strlen($parts[0]) === 1 ? '0' . $parts[0] : $parts[0]) .
                '-' . (strlen($parts[1]) === 1 ? '0' . $parts[1] : $parts[1]) .
                '-' . $parts[2];
        }

        // is the year missing? must end in at least two digits
        if (!preg_match("/.*\d\d$/", $input)) {
            return $returnInvalid;
        }

        // already in mysql format?
        if (substr($input, 4, 1) === '-') {
            return $input . $savedTimeString;
        }

        // short style month? maybe its "30 juni 1963"
        if (preg_match("/jan|feb|maart|mrt|apr|mei|jun|jul|aug|sept|okt|oct|nov|dec/", $input)) {
            $input = str_replace(" januari ", "-01-", $input);
            $input = str_replace(" jan ", "-01-", $input);
            $input = str_replace(" februari ", "-02-", $input);
            $input = str_replace(" feb ", "-02-", $input);
            $input = str_replace(" febr ", "-02-", $input);
            $input = str_replace(" maart ", "-03-", $input);
            $input = str_replace(" mrt ", "-03-", $input);
            $input = str_replace(" april ", "-04-", $input);
            $input = str_replace(" apr ", "-04-", $input);
            $input = str_replace(" mei ", "-05-", $input);
            $input = str_replace(" juni ", "-06-", $input);
            $input = str_replace(" jun ", "-06-", $input);
            $input = str_replace(" juli ", "-07-", $input);
            $input = str_replace(" jul ", "-07-", $input);
            $input = str_replace(" augustus ", "-08-", $input);
            $input = str_replace(" aug ", "-08-", $input);
            $input = str_replace(" september ", "-09-", $input);
            $input = str_replace(" sept ", "-09-", $input);
            $input = str_replace(" oktober ", "-10-", $input);
            $input = str_replace(" okt ", "-10-", $input);
            $input = str_replace(" october ", "-10-", $input);
            $input = str_replace(" oct ", "-10-", $input);
            $input = str_replace(" november ", "-11-", $input);
            $input = str_replace(" nov ", "-11-", $input);
            $input = str_replace(" december ", "-12-", $input);
            $input = str_replace(" dec ", "-12-", $input);
        }

        // is the century missing in a date string?
        // if we try 20.. and the resulting date is a future date, consider this fault and use 19..
        // otherwise use 20.. as century
        if (preg_match("/.+-[0-9]{2}$/", $input)) {
            $newDateString20 = substr($input, 0, -2) . "20" . substr($input, -2);
            if (strlen($newDateString20) === 10) {
                $input20 = new \DateTime($newDateString20);
                $today = new \DateTime();
                if ($input20 > $today) {
                    // future date, use 19xx
                    $input = substr($input, 0, -2) . "19" . substr($input, -2);
                } else {
                    $input = substr($input, 0, -2) . "20" . substr($input, -2);
                }
            } else {
                return $returnInvalid;
            }
        }

        // finally, convert the resulting date sting
        try {
            $d = \DateTime::createFromFormat('d-m-Y', $input);
            if ($d) {
                $input = $d->format('Y-m-d');
            }
            if (!$d) {
                return $returnInvalid;
            }
            return $input . $savedTimeString;
        } catch (Exception $e) {
            // couldn't convert the string. return the input
            return $returnInvalid;
        }
    }
}