<?php namespace Scolavisa\scolib;

use Scolavisa\scolib\Validation;

/**
 *  String Manipulator
 *
 *  Utility for working with string
 *
 * @uses Validation
 * <AUTHOR>
 */
class StringManipulator
{

    /**
     * This function splits the input in telefoon number and extra text
     * assuming that the telephone number is the left-most part!
     * @param $input
     * @return array
     */
    static function telephoneExtractor($input)
    {
        // does the input only contain [0-9-+.] between 10 and 17 characters
        // 0612345678 and +31 6 12 34 55 66 both match
        $re = '/^[0-9-+\s]{10,17}$/m';
        if (preg_match($re, $input)) {
            return array('tel' => $input);
        }
        $retArr = [];
        // is the position of the first character that is not [0-9-+\s] >= 10
        preg_match('/[^0-9-+]+$/m', $input, $matches, PREG_OFFSET_CAPTURE);
        if (isset($matches[0]) && $matches[0][1] >= 10) {
            $position = $matches[0][1];
            $extraText = $matches[0][0];
            // split the input into the number folowed by "the rest"
            $retArr = ['tel' => trim(substr($input, 0, $position)), 'extra' => trim($extraText)];
        }
        return $retArr;
    }

    /**
     * This function tries to recognize the email part (only one) in a string
     * It returns an array with key 'email' and 'extra'. If the string only conbtains an email address
     * the key extra will not be returned
     * @param $input
     * @return array
     */
    static function emailExtractor($input)
    {
        $input = filter_var(trim($input), FILTER_SANITIZE_STRING, FILTER_FLAG_STRIP_HIGH);
        $retArr = [];

        if ($input === '') return [];

        if (Validation::isValidEmail($input)) {
            $retArr['email'] = $input;
        } else {
            preg_match_all("/[\._a-zA-Z0-9-]+@[\._a-zA-Z0-9-]+/i", $input, $matches, PREG_OFFSET_CAPTURE);
            if (isset($matches[0]) && isset($matches[0][0])) {
                $retArr['email'] = $matches[0][0][0];
                $retArr['extra'] = trim(substr($input, strlen($matches[0][0][0])));
            }
        }
        return $retArr;
    }

    /**
     * In case a price was entered using a comma as decimal separator convert it to a point
     * leave if already well-formed
     * remove any thousand-separator
     * @param string $input
     * @return string
     */
    static function price2database(string $input): string
    {
        $re1 = '/[a-z]/';
        if (preg_match($re1, $input)) {
            return "0.00";
        }
        // if no decimals, add decimals
        if (strpos($input, '.') === false && strpos($input, ',') === false) {
            return $input . ".00";
        }
        // remove any thousand separators
        if (strpos($input, ',') !== false && strpos($input, '.') !== false) {
            // remove all 100,000.00 => 10000000
            $output = str_replace(",", "", $input);
            $output = str_replace(".", "", $output);
            // now insert a dot before the last two digits 10000000 => 100000.00
            $pos = strlen($output) - 2;
            $output = substr($output, 0, $pos) . "." . substr($output, $pos);
        } else {
            $output = $input;
        }
        $result = str_replace(",", ".", $output);
        // if only 1 dec, add 1
        return sprintf("%01.2F", $result);
    }

    /**
     * about the same as price2database, but doesn't create fix two decimal places
     * this is not very precise because we don't know if 100,001
     * if the comma is a thousand separator or a decimal, only so much we can do
     * @param string $input
     * @return string
     */
    static function float2database(string $input): string
    {
        $re1 = '/[a-z]/';
        if (preg_match($re1, $input)) {
            return "0";
        }
        $result = str_replace(",", ".", $input);
        return $result;
    }
}
