<?php namespace Scolavisa\scolib;

/**
 *  MiscDate
 *
 *  Generic date functions
 *  @uses Mydat2Ndat
 *  @uses Ndat2Mydat
 *
 *  <AUTHOR>
 */
class MiscDate {
    /**
     * Determine age today by means of a birth date
     * @param string $birthDate format: Y-m-d
     * @param string $format
     * @param string $tz
     * @return int
     * @throws \Exception
     */
    public static function calculateAge(
        string $birthDate,
        string $format = 'Y-m-d',
        string $tz='Europe/Amsterdam'
    ) {
        if (empty($birthDate) || !MiscDate::validateDate($birthDate, $format)) {
            return 0;
        }

        $timezone = new \DateTimeZone($tz);
        return \DateTime::createFromFormat('Y-m-d', $birthDate, $timezone)
            ->diff(new \DateTime('now', $timezone))
            ->y;
    }

    // Helpers

    /**
     * Check if the date has a valid signature
     * @param $date
     * @param string $format, e.g 'Y-m-d'
     * @return bool
     */
    public static function validateDate($date, string $format = 'Y-m-d')
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    /**
     * determine if date range 1 overlaps with date range 2
     * not inclusive: the same time is considered to be overlapping
     * @param $d1s string date 1 start
     * @param $d1e string date 1 end
     * @param $d2s string date 2 start
     * @param $d2e string date 2 end
     * @return bool
     */
    public static function overlaps(string $d1s, string $d1e, string $d2s, string $d2e): bool
    {
        try {
            $d1sObj = new \DateTime($d1s);
            $d1eObj = new \DateTime($d1e);
            $d2sObj = new \DateTime($d2s);
            $d2eObj = new \DateTime($d2e);
        } catch (\Exception $exception) {
            if (empty($d1sObj) || empty($d1eObj) || empty($d2sObj) || empty($d2eObj)) {
                throw new \InvalidArgumentException("some date strings could not be used to generate a datetime object");
            }
            throw $exception;
        }
        return ($d1sObj < $d2eObj) && ($d2sObj < $d1eObj);
    }
}
