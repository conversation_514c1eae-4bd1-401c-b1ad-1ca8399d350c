<?php namespace Scolavisa\scolib;

/**
 *  Color
 *
 *  Function to help choosing and converting colors
 *
 * <AUTHOR>
 */
class Color
{
    // set of 20 contrasting colors ordered by index
    public static $colorsets = [
        'emptyset' => [], // for unittest
        '20contrasting' => [
            '#e6194b',
            '#3cb44b',
            '#ffe119',
            '#4363d8',
            '#f58231',
            '#911eb4',
            '#46f0f0',
            '#f032e6',
            '#bcf60c',
            '#fabebe',
            '#008080',
            '#e6beff',
            '#9a6324',
            '#fffac8',
            '#800000',
            '#aaffc3',
            '#808000',
            '#ffd8b1',
            '#000075',
            '#808080'
        ],
    ];

    /**
     * Gets the hex value of a color by index from a colorset defined by the setname
     * If the requested set does not exist or is empty, respond with the default color
     * @param int index default: 0
     * @param string setname defaul: 20contrasting
     * @return string
     */
    public static function getHexColorFromSet($index = 0, $setname = '20contrasting')
    {
        $defaultResponse = self::$colorsets["20contrasting"][0];
        // does the requested set exist?
        $setLength = isset(self::$colorsets[$setname]) ? count(self::$colorsets[$setname]) : 0;
        $indexIsInt = $index === intval($index);
        if (($setLength === 0) || (!$indexIsInt) || ($index < 0)) {
            return $defaultResponse;
        } else {
            // get modulo index
            return self::$colorsets[$setname][$index % $setLength];
        }
    }

    /**
     * Gets all colors of a set
     * @param string setname
     * @return array
     */
    public static function getAllColorsOfSet($setname = '')
    {
        if (($setname !== '') && (isset(self::$colorsets[$setname])) && (count(self::$colorsets[$setname]) > 0)) {
            return self::$colorsets[$setname];
        } else {
            return [];
        }
    }

}
