<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
            <title>Documentation</title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="../">
    <link rel="icon" href="images/favicon.ico"/>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/base.css">
            <link rel="preconnect" href="https://fonts.gstatic.com">
        <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Source+Code+Pro:wght@400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="css/template.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.0/css/all.min.css" integrity="sha256-ybRkN9dBjhcS2qrW1z+hfCxq+1aBdwyQM5wlQoQVt/0=" crossorigin="anonymous" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/themes/prism-okaidia.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.css">
                <script src="https://cdn.jsdelivr.net/npm/fuse.js@3.4.6"></script>
        <script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@2"></script>
        <script src="js/search.js"></script>
        <script defer src="js/searchIndex.js"></script>
    </head>
<body id="top">
    <header class="phpdocumentor-header phpdocumentor-section">
    <h1 class="phpdocumentor-title"><a href="" class="phpdocumentor-title__link">Documentation</a></h1>
    <input class="phpdocumentor-header__menu-button" type="checkbox" id="menu-button" name="menu-button" />
    <label class="phpdocumentor-header__menu-icon" for="menu-button">
        <i class="fas fa-bars"></i>
    </label>
    <section data-search-form class="phpdocumentor-search">
    <label>
        <span class="visually-hidden">Search for</span>
        <svg class="phpdocumentor-search__icon" width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="7.5" cy="7.5" r="6.5" stroke="currentColor" stroke-width="2"/>
            <line x1="12.4892" y1="12.2727" x2="19.1559" y2="18.9393" stroke="currentColor" stroke-width="3"/>
        </svg>
        <input type="search" class="phpdocumentor-field phpdocumentor-search__field" placeholder="Loading .." disabled />
    </label>
</section>

    <nav class="phpdocumentor-topnav">
    <ul class="phpdocumentor-topnav__menu">
        </ul>
</nav>
</header>

    <main class="phpdocumentor">
        <div class="phpdocumentor-section">
            <input class="phpdocumentor-sidebar__menu-button" type="checkbox" id="sidebar-button" name="sidebar-button" />
<label class="phpdocumentor-sidebar__menu-icon" for="sidebar-button">
    Menu
</label>
<aside class="phpdocumentor-column -four phpdocumentor-sidebar">
                    <section class="phpdocumentor-sidebar__category">
            <h2 class="phpdocumentor-sidebar__category-header">Namespaces</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="namespaces/scolavisa.html" class="">Scolavisa</a>
</h4>
    <ul class="phpdocumentor-list">
                    <li>
                <a href="namespaces/scolavisa-scolib.html" class="">scolib</a>
                
            </li>
            </ul>

                        </section>
                <section class="phpdocumentor-sidebar__category">
            <h2 class="phpdocumentor-sidebar__category-header">Packages</h2>
                                    <h4 class="phpdocumentor-sidebar__root-namespace">
    <a href="packages/Application.html" class="">Application</a>
</h4>

                        </section>
            
    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Reports</h2>
                <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/deprecated.html">Deprecated</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/errors.html">Errors</a></h3>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="reports/markers.html">Markers</a></h3>
    </section>

    <section class="phpdocumentor-sidebar__category">
        <h2 class="phpdocumentor-sidebar__category-header">Indices</h2>
        <h3 class="phpdocumentor-sidebar__root-package"><a href="indices/files.html">Files</a></h3>
    </section>
</aside>

            <div class="phpdocumentor-column -eight phpdocumentor-content">
                    <ul class="phpdocumentor-breadcrumbs">
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/scolavisa.html">Scolavisa</a></li>
            <li class="phpdocumentor-breadcrumb"><a href="namespaces/scolavisa-scolib.html">scolib</a></li>
    </ul>

    <article class="phpdocumentor-element -class">
        <h2 class="phpdocumentor-content__title">
    Ndat2Mydat

    
            <div class="phpdocumentor-element__package">
            in package
            <ul class="phpdocumentor-breadcrumbs">
                                    <li class="phpdocumentor-breadcrumb"><a href="packages/Application.html">Application</a></li>
                            </ul>
        </div>
    
    
    </h2>

        <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/Ndat2Mydat.php"><a href="files/src-ndat2mydat.html"><abbr title="src/Ndat2Mydat.php">Ndat2Mydat.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">12</span>

    </aside>

            <p class="phpdocumentor-summary">Ndat2Mydat</p>

    <section class="phpdocumentor-description"><p>Converts a dutch date string (dd-mm-yyyy) to a mysql formatted date (yyyy-mm-dd).
A time part will also be included in the returned string. The function will
automatically try to determine the correct date from a variety of input formats.</p>
</section>


    <h5 class="phpdocumentor-tag-list__heading" id="tags">
        Tags
        <a href="#tags" class="headerlink"><i class="fas fa-link"></i></a>
    </h5>
    <dl class="phpdocumentor-tag-list">
                                    <dt class="phpdocumentor-tag-list__entry">
                    <span class="phpdocumentor-tag__name">author</span>
                </dt>
                <dd class="phpdocumentor-tag-list__definition">
                                                                                
                                                 <section class="phpdocumentor-description"><p>scolavisa</p>
</section>

                                    </dd>
                        </dl>






<h3 id="toc">
    Table of Contents
    <a href="#toc" class="headerlink"><i class="fas fa-link"></i></a>
</h3>

<dl class="phpdocumentor-table-of-contents">
                        <dt class="phpdocumentor-table-of-contents__entry -method -public">
    <a href="classes/Scolavisa-scolib-Ndat2Mydat.html#method_getMydat">getMydat()</a>
    <span>
                                &nbsp;: mixed    </span>
</dt>
<dd></dd>

        </dl>



        

        

            <section class="phpdocumentor-methods">
        <h3 class="phpdocumentor-elements__header" id="methods">
            Methods
            <a href="classes/Scolavisa-scolib-Ndat2Mydat.html#methods" class="headerlink"><i class="fas fa-link"></i></a>
        </h3>
                    <article
        class="phpdocumentor-element
            -method
            -public
                                    -static                    "
>
    <h4 class="phpdocumentor-element__name" id="method_getMydat">
        getMydat()
        <a href="classes/Scolavisa-scolib-Ndat2Mydat.html#method_getMydat" class="headerlink"><i class="fas fa-link"></i></a>
    </h4>
    <aside class="phpdocumentor-element-found-in">
    <abbr class="phpdocumentor-element-found-in__file" title="src/Ndat2Mydat.php"><a href="files/src-ndat2mydat.html"><abbr title="src/Ndat2Mydat.php">Ndat2Mydat.php</abbr></a></abbr>
    :
    <span class="phpdocumentor-element-found-in__line">13</span>

    </aside>

    
    <code class="phpdocumentor-code phpdocumentor-signature ">
    <span class="phpdocumentor-signature__visibility">public</span>
            <span class="phpdocumentor-signature__static">static</span>        <span class="phpdocumentor-signature__name">getMydat</span><span>(</span><span class="phpdocumentor-signature__argument"><span>[</span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$input</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span><span> ]</span></span><span class="phpdocumentor-signature__argument"><span>[</span><span>, </span><span class="phpdocumentor-signature__argument__return-type">mixed&nbsp;</span><span class="phpdocumentor-signature__argument__name">$returnFalseOnInvalid</span><span> = </span><span class="phpdocumentor-signature__argument__default-value">false</span><span> ]</span></span><span>)</span><span> : </span><span class="phpdocumentor-signature__response_type">mixed</span></code>

        <section class="phpdocumentor-description"></section>

        <h5 class="phpdocumentor-argument-list__heading">Parameters</h5>
    <dl class="phpdocumentor-argument-list">
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$input</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">&#039;&#039;</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
                    <dt class="phpdocumentor-argument-list__entry">
                <span class="phpdocumentor-signature__argument__name">$returnFalseOnInvalid</span>
                : <span class="phpdocumentor-signature__argument__return-type">mixed</span>
                 = <span class="phpdocumentor-signature__argument__default-value">false</span>            </dt>
            <dd class="phpdocumentor-argument-list__definition">
                    <section class="phpdocumentor-description"></section>

            </dd>
            </dl>

    

        <h5 class="phpdocumentor-return-value__heading">Return values</h5>
    <span class="phpdocumentor-signature__response_type">mixed</span>
            &mdash;
        
    
</article>
            </section>

        
    <script type="text/javascript">
        function loadExternalCodeSnippets(line) {
            Array.prototype.slice.call(document.querySelectorAll('pre[data-src]')).forEach((pre) => {
                var src = pre.getAttribute('data-src').replace( /\\/g, '/');
                var extension = (src.match(/\.(\w+)$/) || [, ''])[1];
                var language = 'php';

                var code = document.createElement('code');
                code.className = 'language-' + language;

                pre.textContent = '';

                pre.setAttribute('data-line', line)
                code.textContent = 'Loading…';

                pre.appendChild(code);

                var xhr = new XMLHttpRequest();

                xhr.open('GET', src, true);

                xhr.onreadystatechange = function () {
                    if (xhr.readyState == 4) {

                        if (xhr.status < 400 && xhr.responseText) {
                            code.textContent = xhr.responseText;

                            Prism.highlightElement(code);
                        }
                        else if (xhr.status >= 400) {
                            code.textContent = '✖ Error ' + xhr.status + ' while fetching file: ' + xhr.statusText;
                        }
                        else {
                            code.textContent = '✖ Error: File does not exist, is empty or trying to view from localhost';
                        }
                    }
                };

                xhr.send(null);
            });
        }

        var modals = document.querySelectorAll("[data-modal]");

        modals.forEach(function (trigger) {
            trigger.addEventListener("click", function (event) {
                //event.preventDefault();
                const modal = document.getElementById(trigger.dataset.modal);
                modal.classList.add("phpdocumentor-modal__open");
                loadExternalCodeSnippets(trigger.dataset.line)
                const exits = modal.querySelectorAll("[data-exit-button]");
                exits.forEach(function (exit) {
                    exit.addEventListener("click", function (event) {
                        event.preventDefault();
                        modal.classList.remove("phpdocumentor-modal__open");
                    });
                });
            });
        });
    </script>

    </article>
                <section data-search-results class="phpdocumentor-search-results phpdocumentor-search-results--hidden">
    <section class="phpdocumentor-search-results__dialog">
        <header class="phpdocumentor-search-results__header">
            <h2 class="phpdocumentor-search-results__title">Search results</h2>
            <button class="phpdocumentor-search-results__close"><i class="fas fa-times"></i></button>
        </header>
        <section class="phpdocumentor-search-results__body">
            <ul class="phpdocumentor-search-results__entries"></ul>
        </section>
    </section>
</section>
            </div>
        </div>
        <a href="classes/Scolavisa-scolib-Ndat2Mydat.html#top" class="phpdocumentor-back-to-top"><i class="fas fa-chevron-circle-up"></i></a>

    </main>

    <script>
        cssVars({});
    </script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-numbers/prism-line-numbers.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.23.0/plugins/line-highlight/prism-line-highlight.min.js"></script>
</body>
</html>
