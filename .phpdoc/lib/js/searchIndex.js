Search.appendIndex(
    [
                {
            "fqsen": "\\Scolavisa\\scolib\\Color",
            "name": "Color",
            "summary": "Color",
            "url": "classes/Scolavisa-scolib-Color.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Color\u003A\u003AgetHexColorFromSet\u0028\u0029",
            "name": "getHexColorFromSet",
            "summary": "Gets\u0020the\u0020hex\u0020value\u0020of\u0020a\u0020color\u0020by\u0020index\u0020from\u0020a\u0020colorset\u0020defined\u0020by\u0020the\u0020setname\nIf\u0020the\u0020requested\u0020set\u0020does\u0020not\u0020exist\u0020or\u0020is\u0020empty,\u0020respond\u0020with\u0020the\u0020default\u0020color",
            "url": "classes/Scolavisa-scolib-Color.html#method_getHexColorFromSet"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Color\u003A\u003AgetAllColorsOfSet\u0028\u0029",
            "name": "getAllColorsOfSet",
            "summary": "Gets\u0020all\u0020colors\u0020of\u0020a\u0020set",
            "url": "classes/Scolavisa-scolib-Color.html#method_getAllColorsOfSet"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Color\u003A\u003A\u0024colorsets",
            "name": "colorsets",
            "summary": "",
            "url": "classes/Scolavisa-scolib-Color.html#property_colorsets"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\LocaleDate",
            "name": "LocaleDate",
            "summary": "LocaleDate",
            "url": "classes/Scolavisa-scolib-LocaleDate.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\LocaleDate\u003A\u003AgetLocaleSpecificDate\u0028\u0029",
            "name": "getLocaleSpecificDate",
            "summary": "",
            "url": "classes/Scolavisa-scolib-LocaleDate.html#method_getLocaleSpecificDate"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\MiscDate",
            "name": "MiscDate",
            "summary": "MiscDate",
            "url": "classes/Scolavisa-scolib-MiscDate.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\MiscDate\u003A\u003AcalculateAge\u0028\u0029",
            "name": "calculateAge",
            "summary": "Determine\u0020age\u0020today\u0020by\u0020means\u0020of\u0020a\u0020birth\u0020date",
            "url": "classes/Scolavisa-scolib-MiscDate.html#method_calculateAge"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\MiscDate\u003A\u003AvalidateDate\u0028\u0029",
            "name": "validateDate",
            "summary": "Check\u0020if\u0020the\u0020date\u0020has\u0020a\u0020valid\u0020signature",
            "url": "classes/Scolavisa-scolib-MiscDate.html#method_validateDate"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\MiscDate\u003A\u003Aoverlaps\u0028\u0029",
            "name": "overlaps",
            "summary": "determine\u0020if\u0020date\u0020range\u00201\u0020overlaps\u0020with\u0020date\u0020range\u00202\nnot\u0020inclusive\u003A\u0020the\u0020same\u0020time\u0020is\u0020considered\u0020to\u0020be\u0020overlapping",
            "url": "classes/Scolavisa-scolib-MiscDate.html#method_overlaps"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Mydat2Ndat",
            "name": "Mydat2Ndat",
            "summary": "Mydat2Ndat",
            "url": "classes/Scolavisa-scolib-Mydat2Ndat.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Mydat2Ndat\u003A\u003AgetNdat\u0028\u0029",
            "name": "getNdat",
            "summary": "",
            "url": "classes/Scolavisa-scolib-Mydat2Ndat.html#method_getNdat"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Ndat2Mydat",
            "name": "Ndat2Mydat",
            "summary": "Ndat2Mydat",
            "url": "classes/Scolavisa-scolib-Ndat2Mydat.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Ndat2Mydat\u003A\u003AgetMydat\u0028\u0029",
            "name": "getMydat",
            "summary": "",
            "url": "classes/Scolavisa-scolib-Ndat2Mydat.html#method_getMydat"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\StringManipulator",
            "name": "StringManipulator",
            "summary": "String\u0020Manipulator",
            "url": "classes/Scolavisa-scolib-StringManipulator.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\StringManipulator\u003A\u003AtelephoneExtractor\u0028\u0029",
            "name": "telephoneExtractor",
            "summary": "This\u0020function\u0020splits\u0020the\u0020input\u0020in\u0020telefoon\u0020number\u0020and\u0020extra\u0020text\nassuming\u0020that\u0020the\u0020telephone\u0020number\u0020is\u0020the\u0020left\u002Dmost\u0020part\u0021",
            "url": "classes/Scolavisa-scolib-StringManipulator.html#method_telephoneExtractor"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\StringManipulator\u003A\u003AemailExtractor\u0028\u0029",
            "name": "emailExtractor",
            "summary": "This\u0020function\u0020tries\u0020to\u0020recognize\u0020the\u0020email\u0020part\u0020\u0028only\u0020one\u0029\u0020in\u0020a\u0020string\nIt\u0020returns\u0020an\u0020array\u0020with\u0020key\u0020\u0027email\u0027\u0020and\u0020\u0027extra\u0027.\u0020If\u0020the\u0020string\u0020only\u0020conbtains\u0020an\u0020email\u0020address\nthe\u0020key\u0020extra\u0020will\u0020not\u0020be\u0020returned",
            "url": "classes/Scolavisa-scolib-StringManipulator.html#method_emailExtractor"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\StringManipulator\u003A\u003Aprice2database\u0028\u0029",
            "name": "price2database",
            "summary": "In\u0020case\u0020a\u0020price\u0020was\u0020entered\u0020using\u0020a\u0020comma\u0020as\u0020decimal\u0020separator\u0020convert\u0020it\u0020to\u0020a\u0020point\nleave\u0020if\u0020already\u0020well\u002Dformed\nremove\u0020any\u0020thousand\u002Dseparator",
            "url": "classes/Scolavisa-scolib-StringManipulator.html#method_price2database"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Validation",
            "name": "Validation",
            "summary": "Validation",
            "url": "classes/Scolavisa-scolib-Validation.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib\\Validation\u003A\u003AisValidEmail\u0028\u0029",
            "name": "isValidEmail",
            "summary": "wrapper\u0020for\u0020the\u0020PHP\u0020email\u0020filter",
            "url": "classes/Scolavisa-scolib-Validation.html#method_isValidEmail"
        },                {
            "fqsen": "\\",
            "name": "\\",
            "summary": "",
            "url": "namespaces/default.html"
        },                {
            "fqsen": "\\Scolavisa\\scolib",
            "name": "scolib",
            "summary": "",
            "url": "namespaces/scolavisa-scolib.html"
        },                {
            "fqsen": "\\Scolavisa",
            "name": "Scolavisa",
            "summary": "",
            "url": "namespaces/scolavisa.html"
        }            ]
);
