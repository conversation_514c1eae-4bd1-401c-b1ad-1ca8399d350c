{"name": "scolavisa/yfantis", "description": "Yfantis modules administration for wordpress websites.", "keywords": ["<PERSON><PERSON><PERSON>", "webmodules"], "license": "MIT", "type": "project", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^v12.19.3", "laravel/passport": "^v12.4.2", "laravel/tinker": "^2.8", "laravel/ui": "v4.6.1", "mailgun/mailgun-php": "^4.0", "scolavisa/scolib": "dev-master"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^v8.1", "phpunit/phpunit": "^11.5.25", "spatie/laravel-ignition": "^2.0"}, "repositories": [{"type": "git", "url": "**************:micksp/scolib.git"}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform-check": false, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}