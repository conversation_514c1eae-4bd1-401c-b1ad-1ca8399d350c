{"name": "scolavisa/scolib", "homepage": "https://www.scolavisa.eu", "description": "Scolavisa Composer package for generic functions", "version": "1.3.1", "keywords": ["libray", "scolavisa", "date", "string", "color"], "license": "proprietary", "authors": [{"name": "<PERSON>", "email": "micha<PERSON>@scolavisa.eu"}], "type": "vcs", "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^9.0", "spatie/phpunit-watcher": "^1.23"}, "autoload": {"psr-4": {"Scolavisa\\scolib\\": "src/"}}, "scripts": {"test": "vendor/bin/phpunit", "test:watch": "vendor/bin/phpunit-watcher watch"}}